# 文档路径: 07-developer-documentation > 03-vllm-engine > 01-llmengine > LLMEngine > 示例

## 示例

```python
# Please see the example/ folder for more detailed examples.
# 请参阅 example/ 文件夹获得更多详细信息


# initialize engine and request arguments
# 初始化引擎和请求参数
engine = LLMEngine.from_engine_args(engine_args)
example_inputs = [(0, "What is LLM?",
   SamplingParams(temperature=0.0))]


# Start the engine with an event loop
# 以事件循环启动引擎
while True:
    if example_inputs:
        req_id, prompt, sampling_params = example_inputs.pop(0)
        engine.add_request(str(req_id),prompt,sampling_params)


    # continue the request processing
    # 持续请求过程
    request_outputs = engine.step()
    for request_output in request_outputs:
        if request_output.finished:
            # return or show the request output
            # 返回或显示请求输出


    if not (engine.has_unfinished_requests() or example_inputs):
        break
```
