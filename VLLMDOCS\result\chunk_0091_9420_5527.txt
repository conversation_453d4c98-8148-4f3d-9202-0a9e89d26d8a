# 文档路径: 03-models > 05-using-lora-adapters > 使用 LoRA 适配器 > LoRA 适配器服务

## LoRA 适配器服务

LoRA 适配模型也可以通过 Open-AI 兼容的 vLLM 服务器提供服务。为此，我们在启动服务器时使用 `--lora-modules {name}={path} {name}={path}` 来指定每个 LoRA 模块：

```bash
vllm serve meta-llama/Llama-2-7b-hf \
    --enable-lora \
    --lora-modules sql-lora=$HOME/.cache/huggingface/hub/models--yard1--llama-2-7b-sql-lora-test/snapshots/0dfa347e8877a4d4ed19ee56c140fa518470028c/
```

**注意：**
提交 ID _0dfa347e8877a4d4ed19ee56c140fa518470028c_ 可能会随着时间的推移而改变。请检查您环境中的最新提交 ID，以确保您使用的是正确的提交 ID。

服务器入口点接受所有其他 LoRA 配置参数（`max_loras` 、 `max_lora_rank` 、 `max_cpu_loras` 等），这些参数将应用于所有即将到来的请求。查询 `/models` 端点后，我们应该看到 LoRA 及其基本模型：

```bash
curl localhost:8000/v1/models | jq .
{
    "object": "list",
    "data": [
        {
            "id": "meta-llama/Llama-2-7b-hf",
            "object": "model",
            ...
        },
        {
            "id": "sql-lora",
            "object": "model",
            ...
        }
    ]
}
```

请求可以通过 `model` 请求参数指定 LoRA 适配器，就像指定任何其他模型一样。这些请求将根据服务器端的 LoRA 配置进行处理（即与基础模型请求并行处理，如果提供了其他 LoRA 适配器请求且 `max_loras` 设置得足够高，也可能与其他 LoRA 适配器请求并行处理）。

以下是请求示例：

```bash
curl http://localhost:8000/v1/completions \
    -H "Content-Type: application/json" \
    -d '{
        "model": "sql-lora",
        "prompt": "San Francisco is a",
        "max_tokens": 7,
        "temperature": 0
    }' | jq
```
