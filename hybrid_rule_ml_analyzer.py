"""
混合规则+机器学习错误分析器

结合规则匹配和机器学习的方法，针对小样本场景优化
"""

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import classification_report, accuracy_score
from sklearn.preprocessing import LabelEncoder
import re
from typing import List, Dict, Tuple, Any, Optional
import logging
import json
import joblib
import os
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HybridRuleMLAnalyzer:
    """
    混合规则+机器学习分析器
    
    首先使用规则匹配，然后用机器学习处理不确定的情况
    """
    
    def __init__(self):
        self.vectorizer = None
        self.ml_model = None
        self.label_encoder = LabelEncoder()
        self.label_mapping = {}
        
        # 基于实际数据分析的精确规则
        self.precise_rules = {
            # 超时相关规则
            '大模型请求超600s未返回': [
                r'600000ms',
                r'600s',
                r'observe.*terminal.*signal.*600000ms',
                r'timeout.*600',
                r'did not observe.*600000ms'
            ],
            
            '大模型600S还没回答完，导致超时关闭连接': [
                r'600S.*回答完',
                r'超时关闭连接',
                r'timeout.*close.*connection'
            ],
            
            # 认证相关规则
            '大模型鉴权未通过': [
                r'authentication.*failed',
                r'auth.*failed',
                r'invalid.*token',
                r'鉴权.*未通过',
                r'认证.*失败'
            ],
            
            # 模型相关规则
            '请求体中模型名称错误': [
                r'model.*name.*error',
                r'model.*name.*not.*found',
                r'模型名称.*错误',
                r'invalid.*model.*name'
            ],
            
            # JSON/参数相关规则
            'tool_call中的arguments为非标准JSON体，大模型无法解析': [
                r'arguments.*json.*parse',
                r'json.*parse.*error.*arguments',
                r'arguments.*非标准.*json',
                r'invalid.*json.*arguments'
            ],
            
            '请求体使用function call时缺少arguments字段': [
                r'function.*call.*missing.*arguments',
                r'缺少.*arguments.*字段',
                r'missing.*arguments.*field'
            ],
            
            '非标准JSON入参': [
                r'非标准.*json.*入参',
                r'invalid.*json.*input',
                r'malformed.*json'
            ],
            
            # 内容相关规则
            '请求体输入内容过长（前置判断，非大模型判断）': [
                r'输入内容过长',
                r'content.*too.*long',
                r'input.*length.*exceed',
                r'前置判断.*非大模型判断'
            ],
            
            '请求体中content为空': [
                r'content.*empty',
                r'content.*为空',
                r'empty.*content'
            ],
            
            '请求体中content为数组': [
                r'content.*array',
                r'content.*为数组',
                r'content.*list'
            ],
            
            # 多模态相关规则
            '多模态使用中type为text时，text为数字': [
                r'type.*text.*text.*数字',
                r'multimodal.*text.*number',
                r'type.*text.*text.*为数字'
            ],
            
            '多模态使用中type为text时，text为一个对象': [
                r'type.*text.*text.*对象',
                r'multimodal.*text.*object',
                r'type.*text.*text.*为.*对象'
            ],
            
            '图片错误': [
                r'图片.*错误',
                r'image.*error',
                r'picture.*error'
            ],
            
            '图片base64编码数据部分未进行正确填充': [
                r'base64.*编码.*填充',
                r'base64.*padding.*error',
                r'图片.*base64.*填充'
            ],
            
            # 其他规则
            '用户主动切断大模型回复': [
                r'用户.*主动.*切断',
                r'user.*interrupt',
                r'manually.*stopped'
            ],
            
            '大模型回答是null': [
                r'回答.*null',
                r'answer.*null',
                r'response.*null'
            ],
            
            '响应体数据超过默认的缓冲区限制': [
                r'响应体.*缓冲区.*限制',
                r'response.*buffer.*limit',
                r'data.*exceed.*buffer'
            ]
        }
        
        # 关键词权重
        self.keyword_weights = {
            '600000ms': 10.0, '600s': 10.0, 'timeout': 5.0,
            'authentication': 8.0, 'failed': 5.0, 'token': 6.0,
            'model': 4.0, 'json': 6.0, 'arguments': 7.0,
            'content': 5.0, 'multimodal': 7.0, 'image': 6.0,
            'null': 6.0, 'error': 4.0, 'parse': 6.0
        }
    
    def rule_based_predict(self, text: str) -> Optional[Tuple[str, float]]:
        """
        基于规则的预测
        
        Args:
            text: 输入文本
            
        Returns:
            (预测标签, 置信度) 或 None
        """
        text_lower = text.lower()
        
        best_match = None
        best_score = 0
        
        for label, patterns in self.precise_rules.items():
            score = 0
            matched_patterns = 0
            
            for pattern in patterns:
                if re.search(pattern, text_lower, re.IGNORECASE):
                    matched_patterns += 1
                    # 根据模式复杂度给分
                    pattern_score = len(pattern.split('.*')) * 2
                    score += pattern_score
            
            # 计算最终得分
            if matched_patterns > 0:
                final_score = score * (matched_patterns / len(patterns))
                if final_score > best_score:
                    best_score = final_score
                    best_match = label
        
        # 如果得分足够高，返回规则预测结果
        if best_score >= 2.0:  # 阈值可调
            confidence = min(0.95, 0.5 + best_score * 0.1)
            return best_match, confidence
        
        return None
    
    def extract_ml_features(self, text: str) -> Dict[str, float]:
        """
        为机器学习提取特征
        
        Args:
            text: 输入文本
            
        Returns:
            特征字典
        """
        features = {}
        text_lower = text.lower()
        
        # 关键词特征
        for keyword, weight in self.keyword_weights.items():
            count = text_lower.count(keyword.lower())
            features[f'keyword_{keyword}'] = count * weight
        
        # 数字特征
        numbers = re.findall(r'\d+', text)
        features['number_count'] = len(numbers)
        features['has_600'] = 1 if '600' in text else 0
        features['has_http_code'] = 1 if any(n in ['400', '401', '403', '404', '500'] for n in numbers) else 0
        
        # 结构特征
        features['has_json'] = 1 if '{' in text or '}' in text else 0
        features['has_quotes'] = text.count('"') + text.count("'")
        features['text_length'] = len(text)
        features['word_count'] = len(text.split())
        
        # 语言特征
        chinese_chars = len(re.findall(r'[\u4e00-\u9fa5]', text))
        features['chinese_ratio'] = chinese_chars / len(text) if text else 0
        
        return features
    
    def train_ml_model(self, texts: List[str], labels: List[str]) -> Dict[str, Any]:
        """
        训练机器学习模型（用于规则无法处理的情况）
        
        Args:
            texts: 文本列表
            labels: 标签列表
            
        Returns:
            训练结果
        """
        # 标签编码
        y = self.label_encoder.fit_transform(labels)
        self.label_mapping = {i: label for i, label in enumerate(self.label_encoder.classes_)}
        
        # 特征提取
        # 1. TF-IDF特征
        self.vectorizer = TfidfVectorizer(
            max_features=100,
            ngram_range=(1, 2),
            min_df=1,
            max_df=0.9,
            stop_words=None
        )
        tfidf_features = self.vectorizer.fit_transform(texts)
        
        # 2. 手工特征
        manual_features = []
        for text in texts:
            features = self.extract_ml_features(text)
            manual_features.append(list(features.values()))
        
        manual_features = np.array(manual_features)
        
        # 3. 组合特征
        X = np.hstack([tfidf_features.toarray(), manual_features])
        
        # 训练模型
        models = {
            'RandomForest': RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                class_weight='balanced'
            ),
            'LogisticRegression': LogisticRegression(
                max_iter=1000,
                random_state=42,
                class_weight='balanced'
            )
        }
        
        results = {}
        cv = StratifiedKFold(n_splits=min(3, len(np.unique(y))), shuffle=True, random_state=42)
        
        for name, model in models.items():
            try:
                cv_scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
                model.fit(X, y)
                
                results[name] = {
                    'model': model,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std()
                }
                
                logger.info(f"ML {name}: CV准确率 = {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
                
            except Exception as e:
                logger.warning(f"ML {name} 训练失败: {e}")
                continue
        
        # 选择最佳模型
        if results:
            best_model_name = max(results.keys(), key=lambda k: results[k]['cv_mean'])
            self.ml_model = results[best_model_name]['model']
            logger.info(f"选择最佳ML模型: {best_model_name}")
        
        return results
    
    def ml_predict(self, text: str) -> Tuple[str, float]:
        """
        机器学习预测
        
        Args:
            text: 输入文本
            
        Returns:
            (预测标签, 置信度)
        """
        if self.ml_model is None:
            raise ValueError("ML模型未训练")
        
        # 特征提取
        tfidf_features = self.vectorizer.transform([text])
        manual_features = np.array([list(self.extract_ml_features(text).values())])
        X = np.hstack([tfidf_features.toarray(), manual_features])
        
        # 预测
        pred_proba = self.ml_model.predict_proba(X)[0]
        pred_class = np.argmax(pred_proba)
        
        return self.label_mapping[pred_class], pred_proba[pred_class]
    
    def train(self, texts: List[str], labels: List[str]) -> Dict[str, Any]:
        """
        训练混合模型
        
        Args:
            texts: 文本列表
            labels: 标签列表
            
        Returns:
            训练结果
        """
        logger.info("开始训练混合规则+ML错误分析模型...")
        logger.info(f"训练数据: {len(texts)} 条, 类别数: {len(set(labels))}")
        
        # 测试规则覆盖率
        rule_covered = 0
        rule_correct = 0
        
        for text, true_label in zip(texts, labels):
            rule_result = self.rule_based_predict(text)
            if rule_result:
                rule_covered += 1
                if rule_result[0] == true_label:
                    rule_correct += 1
        
        rule_coverage = rule_covered / len(texts)
        rule_accuracy = rule_correct / rule_covered if rule_covered > 0 else 0
        
        logger.info(f"规则覆盖率: {rule_coverage:.3f} ({rule_covered}/{len(texts)})")
        logger.info(f"规则准确率: {rule_accuracy:.3f} ({rule_correct}/{rule_covered})")
        
        # 训练ML模型
        ml_results = self.train_ml_model(texts, labels)
        
        # 混合模型评估
        correct_predictions = 0
        for text, true_label in zip(texts, labels):
            predicted_label, _, _ = self.predict(text)
            if predicted_label == true_label:
                correct_predictions += 1
        
        hybrid_accuracy = correct_predictions / len(texts)
        logger.info(f"混合模型准确率: {hybrid_accuracy:.3f}")
        
        return {
            'rule_coverage': rule_coverage,
            'rule_accuracy': rule_accuracy,
            'hybrid_accuracy': hybrid_accuracy,
            'ml_results': ml_results
        }
    
    def predict(self, text: str) -> Tuple[str, float, str]:
        """
        混合预测
        
        Args:
            text: 输入文本
            
        Returns:
            (预测标签, 置信度, 预测方法)
        """
        # 首先尝试规则预测
        rule_result = self.rule_based_predict(text)
        if rule_result:
            return rule_result[0], rule_result[1], "规则匹配"
        
        # 如果规则无法处理，使用ML模型
        if self.ml_model:
            ml_label, ml_confidence = self.ml_predict(text)
            return ml_label, ml_confidence, "机器学习"
        
        return "未知错误", 0.0, "无法预测"
    
    def predict_with_explanation(self, text: str) -> Dict[str, Any]:
        """
        带解释的预测
        
        Args:
            text: 输入文本
            
        Returns:
            详细预测结果
        """
        predicted_label, confidence, method = self.predict(text)
        
        # 找出匹配的规则
        matched_rules = []
        if method == "规则匹配":
            for label, patterns in self.precise_rules.items():
                if label == predicted_label:
                    for pattern in patterns:
                        if re.search(pattern, text.lower(), re.IGNORECASE):
                            matched_rules.append(pattern)
        
        # 提取关键词
        key_indicators = []
        text_lower = text.lower()
        for keyword, weight in self.keyword_weights.items():
            if keyword.lower() in text_lower and weight >= 5.0:
                key_indicators.append(keyword)
        
        return {
            'predicted_label': predicted_label,
            'confidence': confidence,
            'prediction_method': method,
            'matched_rules': matched_rules,
            'key_indicators': key_indicators,
            'is_confident': confidence > 0.5
        }
    
    def save_model(self, path: str):
        """保存模型"""
        os.makedirs(path, exist_ok=True)
        
        # 保存ML组件
        if self.ml_model:
            joblib.dump(self.ml_model, os.path.join(path, 'ml_model.pkl'))
        if self.vectorizer:
            joblib.dump(self.vectorizer, os.path.join(path, 'vectorizer.pkl'))
        joblib.dump(self.label_encoder, os.path.join(path, 'label_encoder.pkl'))
        
        # 保存配置
        config = {
            'precise_rules': self.precise_rules,
            'keyword_weights': self.keyword_weights,
            'label_mapping': self.label_mapping
        }
        
        with open(os.path.join(path, 'config.json'), 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"混合模型已保存到: {path}")

def main():
    """主函数"""
    # 读取数据
    df = pd.read_excel('processed_data.xlsx')
    texts = df['响应内容'].astype(str).tolist()
    labels = df['报错原因'].astype(str).tolist()
    
    # 创建和训练模型
    analyzer = HybridRuleMLAnalyzer()
    results = analyzer.train(texts, labels)
    
    # 保存模型
    analyzer.save_model('models/hybrid_classifier')
    
    # 测试预测
    test_texts = [
        "Did not observe any item or terminal signal within 600000ms in 'peek'",
        "Authentication failed: invalid token",
        "JSON parse error in arguments field",
        "Model name not found in request",
        "Connection timeout after 30 seconds",
        "请求体中模型名称错误",
        "大模型鉴权未通过",
        "tool_call中的arguments为非标准JSON体",
        "用户主动切断大模型回复"
    ]
    
    logger.info("\n混合模型测试预测:")
    for text in test_texts:
        try:
            result = analyzer.predict_with_explanation(text)
            logger.info(f"文本: {text}")
            logger.info(f"预测: {result['predicted_label']} (置信度: {result['confidence']:.3f})")
            logger.info(f"方法: {result['prediction_method']}")
            logger.info(f"可信度: {'是' if result['is_confident'] else '否'}")
            if result['matched_rules']:
                logger.info(f"匹配规则: {result['matched_rules'][:2]}")
            if result['key_indicators']:
                logger.info(f"关键指示词: {result['key_indicators']}")
            logger.info("-" * 60)
        except Exception as e:
            logger.error(f"预测失败: {e}")

if __name__ == "__main__":
    main()
