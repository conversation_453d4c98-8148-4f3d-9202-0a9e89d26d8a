# 文档路径: 01-getting-started > 04-installation-with-cpu > 使用 CPU 安装 > 相关运行时环境变量

## 相关运行时环境变量


- `VLLM_CPU_KVCACHE_SPACE`：指定 KV 缓存大小（例如，`VLLM_CPU_KVCACHE_SPACE=40` 表示 KV 缓存空间为 40 GB），设置得越大，允许 vLLM 并行处理的请求就越多。该参数应根据用户的硬件配置和内存管理模式来设置。
- `VLLM_CPU_OMP_THREADS_BIND`: 指定专用于 OpenMP 线程的 CPU 内核。例如， `VLLM_CPU_OMP_THREADS_BIND=0-31`表示将有 32 个 OpenMP 线程绑定在 0-31 个 CPU 内核上。`VLLM_CPU_OMP_THREADS_BIND=0-31|32-63` 表示将有 2 个张量并行进程，rank0 的 32 个 OpenMP 线程绑定在 0-31 个 CPU 内核上，rank1 的 OpenMP 线程绑定在 32-63 个 CPU 内核上。
