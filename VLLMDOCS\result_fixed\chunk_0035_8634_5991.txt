# 文档路径: 01-getting-started > 08-quickstart > 快速入门 > 兼容 OpenAI 服务器 > 在 vLLM 中使用 OpenAI Completions API

本指南将说明如何使用 vLLM 进行以下操作：

- 对数据集进行离线批量推理；
- 为大语言模型构建 API 服务器；
- 启动与 OpenAI 兼容的 API 服务器。

在继续进行本指南之前，请务必完成[安装说明](https://docs.vllm.ai/en/latest/getting_started/installation.html#installation)。

**注意**

默认情况下，vLLM 从 [HuggingFace](https://huggingface.co/) 下载模型。如果您想在以下示例中使用 [ModelScope](https://www.modelscope.cn) 中的模型，请设置环境变量：

```shell
export VLLM_USE_MODELSCOPE=True
```

## 兼容 OpenAI 服务器

### 在 vLLM 中使用 OpenAI Completions API


使用输入提示查询模型：

```plain
curl http://localhost:8000/v1/completions \
    -H "Content-Type: application/json" \
    -d '{
        "model": "facebook/opt-125m",
        "prompt": "San Francisco is a",
        "max_tokens": 7,
        "temperature": 0
    }'
```

由于该服务器与 OpenAI API 兼容，因此您可以把它作为使用 OpenAI API 的任意应用程序的直接替代品。例如，另一种查询服务器的方法是通过 `openai`的 python 包：

```python
from openai import OpenAI

# Modify OpenAI's API key and API base to use vLLM's API server.
# 使用 vLLM 的 API 服务器需要修改 OpenAI 的 API 密钥和 API 库。

openai_api_key = "EMPTY"
openai_api_base = "http://localhost:8000/v1"
client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)
completion = client.completions.create(model="facebook/opt-125m",
                                      prompt="San Francisco is a")
print("Completion result:", completion)
```

有关更详细的客户端示例，请参阅 [examples/openai_completion_client.py](https://github.com/vllm-project/vllm/blob/main/examples/openai_completion_client.py)。
