# 文档路径: 03-models > 01-supported-models > 支持的模型 > 仅文本语言模型 > 文本生成 > 文本 Embedding > 获奖模型

vLLM 支持 [HuggingFace Transformers](https://huggingface.co/models) 中的各种生成性 Transformer 模型。以下是 vLLM 目前支持的模型架构列表。除了每种架构之外，我们还提供了一些使用它的流行模型。

## 仅文本语言模型



### 文本生成


| 架构                         | 模型                                                | HF 模型案例                                                                                                                                                        | [LoRA](https://LoRA) | [PP](https://PP) |
| :--------------------------- | :-------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------- | :--------------- |
| AquilaForCausalLM            | Aquila, Aquila2                                     | BAAI/Aquila-7B, BAAI/AquilaChat-7B, etc.                                                                                                                           | ✅︎                  | ✅︎              |
| ArcticForCausalLM            | Arctic                                              | Snowflake/snowflake-arctic-base, Snowflake/snowflake-arctic-instruct, etc.                                                                                         |                      | ✅︎              |
| BaiChuanForCausalLM          | Baichuan2, Baichuan                                 | baichuan-inc/Baichuan2-13B-Chat, baichuan-inc/Baichuan-7B, etc.                                                                                                    | ✅︎                  | ✅︎              |
| BloomForCausalLM             | BLOOM, BLOOMZ, BLOOMChat                            | bigscience/bloom, bigscience/bloomz, etc.                                                                                                                          |                      | ✅︎              |
| BartForConditionalGeneration | BART                                                | facebook/bart-base, facebook/bart-large-cnn, etc.                                                                                                                  |                      |                  |
| ChatGLMModel                 | ChatGLM                                             | THUDM/chatglm2-6b, THUDM/chatglm3-6b, etc.                                                                                                                         | ✅︎                  | ✅︎              |
| CohereForCausalLM            | Command-R                                           | CohereForAI/c4ai-command-r-v01, etc.                                                                                                                               | ✅︎                  | ✅︎              |
| DbrxForCausalLM              | DBRX                                                | databricks/dbrx-base, databricks/dbrx-instruct, etc.                                                                                                               |                      | ✅︎              |
| DeciLMForCausalLM            | DeciLM                                              | Deci/DeciLM-7B, Deci/DeciLM-7B-instruct, etc.                                                                                                                      |                      | ✅︎              |
| DeepseekForCausalLM          | DeepSeek                                            | deepseek-ai/deepseek-llm-67b-base, deepseek-ai/deepseek-llm-7b-chat etc.                                                                                           |                      | ✅︎              |
| DeepseekV2ForCausalLM        | DeepSeek-V2                                         | deepseek-ai/DeepSeek-V2, deepseek-ai/DeepSeek-V2-Chat etc.                                                                                                         |                      | ✅︎              |
| ExaoneForCausalLM            | EXAONE-3                                            | LGAI-EXAONE/EXAONE-3.0-7.8B-Instruct, etc.                                                                                                                         | ✅︎                  | ✅︎              |
| FalconForCausalLM            | Falcon                                              | tiiuae/falcon-7b, tiiuae/falcon-40b, tiiuae/falcon-rw-7b, etc.                                                                                                     |                      | ✅︎              |
| GemmaForCausalLM             | Gemma                                               | google/gemma-2b, google/gemma-7b, etc.                                                                                                                             | ✅︎                  | ✅︎              |
| Gemma2ForCausalLM            | Gemma2                                              | google/gemma-2-9b, google/gemma-2-27b, etc.                                                                                                                        | ✅︎                  | ✅︎              |
| GPT2LMHeadModel              | GPT-2                                               | gpt2, gpt2-xl, etc.                                                                                                                                                |                      | ✅︎              |
| GPTBigCodeForCausalLM        | StarCoder, SantaCoder, WizardCoder                  | bigcode/starcoder, bigcode/gpt_bigcode-santacoder, WizardLM/WizardCoder-15B-V1.0, etc.                                                                             | ✅︎                  | ✅︎              |
| GPTJForCausalLM              | GPT-J                                               | EleutherAI/gpt-j-6b, nomic-ai/gpt4all-j, etc.                                                                                                                      |                      | ✅︎              |
| GPTNeoXForCausalLM           | GPT-NeoX, Pythia, OpenAssistant, Dolly V2, StableLM | EleutherAI/gpt-neox-20b, EleutherAI/pythia-12b, OpenAssistant/oasst-sft-4-pythia-12b-epoch-3.5, databricks/dolly-v2-12b, stabilityai/stablelm-tuned-alpha-7b, etc. |                      | ✅︎              |
| GraniteForCausalLM           | PowerLM                                             | ibm/PowerLM-3b etc.                                                                                                                                                | ✅︎                  | ✅︎              |
| GraniteMoeForCausalLM        | PowerMoE                                            | ibm/PowerMoE-3b etc.                                                                                                                                               | ✅︎                  | ✅︎              |
| InternLMForCausalLM          | InternLM                                            | internlm/internlm-7b, internlm/internlm-chat-7b, etc.                                                                                                              | ✅︎                  | ✅︎              |
| InternLM2ForCausalLM         | InternLM2                                           | internlm/internlm2-7b, internlm/internlm2-chat-7b, etc.                                                                                                            |                      | ✅︎              |
| JAISLMHeadModel              | Jais                                                | core42/jais-13b, core42/jais-13b-chat, core42/jais-30b-v3, core42/jais-30b-chat-v3, etc.                                                                           |                      | ✅︎              |
| JambaForCausalLM             | Jamba                                               | ai21labs/AI21-Jamba-1.5-Large, ai21labs/AI21-Jamba-1.5-Mini, ai21labs/Jamba-v0.1, etc.                                                                             | ✅︎                  |                  |
| LlamaForCausalLM             | Llama 3.1, Llama 3, Llama 2, LLaMA, Yi              | meta-llama/Meta-Llama-3.1-405B-Instruct, meta-llama/Meta-Llama-3.1-70B, meta-llama/Meta-Llama-3-70B-Instruct, meta-llama/Llama-2-70b-hf, 01-ai/Yi-34B, etc.        | ✅︎                  | ✅︎              |
| MambaForCausalLM             | Mamba                                               | state-spaces/mamba-130m-hf, state-spaces/mamba-790m-hf, state-spaces/mamba-2.8b-hf, etc.                                                                           |                      |                  |
| MiniCPMForCausalLM           | MiniCPM                                             | openbmb/MiniCPM-2B-sft-bf16, openbmb/MiniCPM-2B-dpo-bf16, openbmb/MiniCPM-S-1B-sft, etc.                                                                           | ✅︎                  | ✅︎              |
| MiniCPM3ForCausalLM          | MiniCPM3                                            | openbmb/MiniCPM3-4B, etc.                                                                                                                                          | ✅︎                  | ✅︎              |
| MistralForCausalLM           | Mistral, Mistral-Instruct                           | mistralai/Mistral-7B-v0.1, mistralai/Mistral-7B-Instruct-v0.1, etc.                                                                                                | ✅︎                  | ✅︎              |
| MixtralForCausalLM           | Mixtral-8x7B, Mixtral-8x7B-Instruct                 | mistralai/Mixtral-8x7B-v0.1, mistralai/Mixtral-8x7B-Instruct-v0.1, mistral-community/Mixtral-8x22B-v0.1, etc.                                                      | ✅︎                  | ✅︎              |
| MPTForCausalLM               | MPT, MPT-Instruct, MPT-Chat, MPT-StoryWriter        | mosaicml/mpt-7b, mosaicml/mpt-7b-storywriter, mosaicml/mpt-30b, etc.                                                                                               |                      | ✅︎              |
| NemotronForCausalLM          | Nemotron-3, Nemotron-4, Minitron                    | nvidia/Minitron-8B-Base, mgoin/Nemotron-4-340B-Base-hf-FP8, etc.                                                                                                   | ✅︎                  | ✅︎              |
| OLMoForCausalLM              | OLMo                                                | allenai/OLMo-1B-hf, allenai/OLMo-7B-hf, etc.                                                                                                                       |                      | ✅︎              |
| OLMoEForCausalLM             | OLMoE                                               | allenai/OLMoE-1B-7B-0924, allenai/OLMoE-1B-7B-0924-Instruct, etc.                                                                                                  | ✅︎                  | ✅︎              |
| OPTForCausalLM               | OPT, OPT-IML                                        | facebook/opt-66b, facebook/opt-iml-max-30b, etc.                                                                                                                   |                      | ✅︎              |
| OrionForCausalLM             | Orion                                               | OrionStarAI/Orion-14B-Base, OrionStarAI/Orion-14B-Chat, etc.                                                                                                       |                      | ✅︎              |
| PhiForCausalLM               | Phi                                                 | microsoft/phi-1_5, microsoft/phi-2, etc.                                                                                                                           | ✅︎                  | ✅︎              |
| Phi3ForCausalLM              | Phi-3                                               | microsoft/Phi-3-mini-4k-instruct, microsoft/Phi-3-mini-128k-instruct, microsoft/Phi-3-medium-128k-instruct, etc.                                                   | ✅︎                  | ✅︎              |
| Phi3SmallForCausalLM         | Phi-3-Small                                         | microsoft/Phi-3-small-8k-instruct, microsoft/Phi-3-small-128k-instruct, etc.                                                                                       |                      | ✅︎              |
| PhiMoEForCausalLM            | Phi-3.5-MoE                                         | microsoft/Phi-3.5-MoE-instruct, etc.                                                                                                                               | ✅︎                  | ✅︎              |
| PersimmonForCausalLM         | Persimmon                                           | adept/persimmon-8b-base, adept/persimmon-8b-chat, etc.                                                                                                             |                      | ✅︎              |
| QWenLMHeadModel              | Qwen                                                | Qwen/Qwen-7B, Qwen/Qwen-7B-Chat, etc.                                                                                                                              |                      | ✅︎              |
| Qwen2ForCausalLM             | Qwen2                                               | Qwen/Qwen2-beta-7B, Qwen/Qwen2-beta-7B-Chat, etc.                                                                                                                  | ✅︎                  | ✅︎              |
| Qwen2MoeForCausalLM          | Qwen2MoE                                            | Qwen/Qwen1.5-MoE-A2.7B, Qwen/Qwen1.5-MoE-A2.7B-Chat, etc.                                                                                                          |                      | ✅︎              |
| StableLmForCausalLM          | StableLM                                            | stabilityai/stablelm-3b-4e1t, stabilityai/stablelm-base-alpha-7b-v2, etc.                                                                                          |                      | ✅︎              |
| Starcoder2ForCausalLM        | Starcoder2                                          | bigcode/starcoder2-3b, bigcode/starcoder2-7b, bigcode/starcoder2-15b, etc.                                                                                         |                      | ✅︎              |
| SolarForCausalLM             | Solar Pro                                           | upstage/solar-pro-preview-instruct, etc.                                                                                                                           | ✅︎                  | ✅︎              |
| XverseForCausalLM            | XVERSE                                              | xverse/XVERSE-7B-Chat, xverse/XVERSE-13B-Chat, xverse/XVERSE-65B-Chat, etc.                                                                                        | ✅︎                  | ✅︎              |

**注意：**

目前，vLLM 的 ROCm 版本仅支持 Mistral 和 Mixtral，上下文长度最多为 4096。


### 文本 Embedding


| 架构         | 模型          | HF 模型案例                           | [LoRA](https://docs.vllm.ai/en/latest/models/lora.html#lora) | [PP](https://docs.vllm.ai/en/latest/serving/distributed_serving.html#distributed-serving) |
| :----------- | :------------ | :------------------------------------ | :----------------------------------------------------------- | :---------------------------------------------------------------------------------------- |
| Gemma2Model  | Gemma2-based  | BAAI/bge-multilingual-gemma2, etc.    |                                                              | ✅︎                                                                                       |
| MistralModel | Mistral-based | intfloat/e5-mistral-7b-instruct, etc. |                                                              | ✅︎                                                                                       |

**注意：**

有些模型架构同时支持生成和嵌入任务。在这种情况下，你需要传入 `--task embedding` 参数，才能以嵌入模式运行该模型。


### 获奖模型


| 架构                | 模型        | HF 模型案例                    | [LoRA](https://docs.vllm.ai/en/latest/models/lora.html#lora) | [PP](https://docs.vllm.ai/en/latest/serving/distributed_serving.html#distributed-serving) |
| :------------------ | :---------- | :----------------------------- | :----------------------------------------------------------- | :---------------------------------------------------------------------------------------- |
| Qwen2ForRewardModel | Qwen2-based | Qwen/Qwen2.5-Math-RM-72B, etc. |                                                              | ✅︎                                                                                       |

**注意：**

作为临时措施，这些模型通过 Embeddings API 获得支持。请参阅[该 RFC](https://github.com/vllm-project/vllm/issues/8967) 了解最新变化。
