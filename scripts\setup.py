"""
项目环境设置脚本

用于初始化项目环境，下载必要的模型和数据
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_directories():
    """创建必要的目录结构"""
    directories = [
        "models",
        "data",
        "logs",
        "outputs",
        "config",
        "scripts"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"创建目录: {directory}")

def install_dependencies():
    """安装项目依赖"""
    logger.info("安装项目依赖...")
    
    try:
        # 使用uv安装依赖
        subprocess.run(["uv", "sync"], check=True)
        logger.info("依赖安装完成")
    except subprocess.CalledProcessError as e:
        logger.error(f"依赖安装失败: {e}")
        sys.exit(1)

def download_pretrained_models():
    """下载预训练模型（可选）"""
    logger.info("检查预训练模型...")
    
    # 这里可以添加下载预训练模型的逻辑
    # 例如从Hugging Face下载BERT模型
    
    logger.info("预训练模型检查完成")

def setup_logging():
    """设置日志配置"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 创建日志配置文件
    log_config = """
[loggers]
keys=root

[handlers]
keys=consoleHandler,fileHandler

[formatters]
keys=simpleFormatter

[logger_root]
level=INFO
handlers=consoleHandler,fileHandler

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=simpleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=FileHandler
level=INFO
formatter=simpleFormatter
args=('logs/app.log', 'a', 'utf-8')

[formatter_simpleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s
"""
    
    with open("config/logging.conf", "w", encoding="utf-8") as f:
        f.write(log_config.strip())
    
    logger.info("日志配置完成")

def create_example_scripts():
    """创建示例脚本"""
    
    # 创建快速训练脚本
    quick_train_script = """#!/usr/bin/env python
# -*- coding: utf-8 -*-
\"\"\"
快速训练脚本
\"\"\"

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from train_model import main
import argparse

if __name__ == "__main__":
    # 使用默认配置进行快速训练
    sys.argv = [
        "quick_train.py",
        "--data", "processed_data.xlsx",
        "--output", "models/error_classifier",
        "--config", "config/model_config.json",
        "--evaluate"
    ]
    main()
"""
    
    with open("scripts/quick_train.py", "w", encoding="utf-8") as f:
        f.write(quick_train_script)
    
    # 创建快速预测脚本
    quick_predict_script = """#!/usr/bin/env python
# -*- coding: utf-8 -*-
\"\"\"
快速预测脚本
\"\"\"

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from predict_service import PredictionService

def main():
    # 创建预测服务
    service = PredictionService("models/error_classifier")
    
    # 示例预测
    test_texts = [
        "HTTP 500 Internal Server Error",
        "Connection timeout after 30 seconds", 
        "Authentication failed: invalid token",
        "JSON parse error: unexpected character",
        "Database connection refused"
    ]
    
    print("=" * 60)
    print("快速预测示例")
    print("=" * 60)
    
    for text in test_texts:
        result = service.predict_single(text, explain=True)
        print(f"文本: {text}")
        print(f"预测: {result.get('predicted_label', '未知')}")
        print(f"置信度: {result.get('confidence', 0):.3f}")
        print("-" * 40)

if __name__ == "__main__":
    main()
"""
    
    with open("scripts/quick_predict.py", "w", encoding="utf-8") as f:
        f.write(quick_predict_script)
    
    logger.info("示例脚本创建完成")

def main():
    """主函数"""
    logger.info("开始项目环境设置...")
    
    # 1. 创建目录结构
    setup_directories()
    
    # 2. 安装依赖
    install_dependencies()
    
    # 3. 下载预训练模型
    download_pretrained_models()
    
    # 4. 设置日志
    setup_logging()
    
    # 5. 创建示例脚本
    create_example_scripts()
    
    logger.info("项目环境设置完成！")
    logger.info("可以使用以下命令开始使用:")
    logger.info("  训练模型: uv run python train_model.py --data processed_data.xlsx")
    logger.info("  预测服务: uv run python predict_service.py --model models/error_classifier --mode single")
    logger.info("  快速训练: uv run python scripts/quick_train.py")
    logger.info("  快速预测: uv run python scripts/quick_predict.py")

if __name__ == "__main__":
    main()
