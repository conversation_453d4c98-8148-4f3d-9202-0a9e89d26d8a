# 文档路径: 07-developer-documentation > 03-vllm-engine > 01-llmengine > LLMEngine > 案例

## 案例

```python
# initialize engine
# 初始化引擎
engine = LLMEngine.from_engine_args(engine_args)
# set request arguments
example_prompt = "Who is the president of the United States?"
sampling_params = SamplingParams(temperature=0.0)
request_id = 0


# add the request to the engine
# 给引擎添加请求
engine.add_request(
   str(request_id),
   example_prompt,
   SamplingParams(temperature=0.0))
# continue the request processing
# 继续请求处理
...
```

> do_log_stats(scheduler_outputs: SchedulerOutputs | [None](https://docs.python.org/3/library/constants.html#None) = None, model_output: [List](https://docs.python.org/3/library/typing.html#typing.List)[SamplerOutput] | [None](https://docs.python.org/3/library/constants.html#None) = None, finished_before: [List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)] | [None](https://docs.python.org/3/library/constants.html#None) = None, skip: [List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)] | [None](https://docs.python.org/3/library/constants.html#None) = None) → [None](https://docs.python.org/3/library/constants.html#None)
[[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.do_log_stats)

当没有活动请求时强制记录日志。

> classmethod from_engine_args(engine_args: EngineArgs, usage_context: UsageContext = UsageContext.ENGINE_CONTEXT, stat_loggers: [Dict](https://docs.python.org/3/library/typing.html#typing.Dict)[[str](https://docs.python.org/3/library/stdtypes.html#str), StatLoggerBase] | [None](https://docs.python.org/3/library/constants.html#None) = None) → [LLMEngine](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine)
[[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.from_engine_args)

从引擎参数创建 LLM 引擎。

> get_decoding_config() → DecodingConfig
> [[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.get_decoding_config)

获取解码配置。

> get_lora_config() → LoRAConfig
> [[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.get_lora_config)

获取 LoRA 配置。

> get_model_config() → ModelConfig
> [[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.get_model_config)

获取模型配置。

> get_num_unfinished_requests() → [int](https://docs.python.org/3/library/functions.html#int) > [[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.get_num_unfinished_requests)

获取未完成的请求数。

> get_parallel_config() → ParallelConfig
> [[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.get_parallel_config)

获取并行配置。

> get_scheduler_config() → SchedulerConfig
> [[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.get_scheduler_config)

获取调度程序配置。

> has_unfinished_requests() → [bool](https://docs.python.org/3/library/functions.html#bool) > [[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.has_unfinished_requests)

如果有未完成的请求，则返回 True。

> has_unfinished_requests_for_virtual_engine(virtual_engine: [int](https://docs.python.org/3/library/functions.html#int)) → [bool](https://docs.python.org/3/library/functions.html#bool) > [[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.has_unfinished_requests_for_virtual_engine)

如果虚拟引擎有未完成的请求，则返回 True。

> step() → [List](https://docs.python.org/3/library/typing.html#typing.List)[RequestOutput | EmbeddingRequestOutput] > [[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.step)

执行一次解码迭代并返回新生成的结果。

![图片](/img/docs/07-03/Overview_of_the_step.png)
step 函数总览
