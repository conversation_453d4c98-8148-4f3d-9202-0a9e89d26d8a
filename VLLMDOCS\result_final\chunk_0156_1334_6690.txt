# 文档路径: 10-tutorials > 04-vllm-langchain-tutorial > 将 LangChain 与 vLLM 结合使用：完整教程 > 目录

[在线运行此教程](https://openbayes.com/console/hyperai-tutorials/containers/ODfeIHjjXbW)

LangChain 是提供构建复杂操作链的工具，而 vLLM 专注于高效的模型推理。两者结合应用可以简化并加速智能 LLM 应用程序的开发。

在本教程中，我们将介绍如何将 LangChain 与 vLLM 结合使用，从设置到分布式推理和量化的所有内容。

## 目录


- [1. 安装和设置 vLLM](#1.安装和设置vLLM)
- [2. 配置 vLLM 以与 LangChain 配合使用](#2.配置vLLM以与LangChain配合使用)
- [3. 使用 LangChain 和 vLLM 创建链](#3.使用LangChain和vLLM创建链)
- [4. 利用多 GPU 推理进行扩展](#4.利用多GPU推理进行扩展)
- [5. 利用量化提高效率](#5.利用量化提高效率)
- [6. 结论](#6.结论)
