"""
模型训练模块

实现基于Transformer的文本分类模型训练，针对小样本场景进行优化。
支持预训练模型微调、数据增强、模型集成等技术。
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
from transformers import (
    AutoModel, AutoTokenizer, AutoConfig,
    get_linear_schedule_with_warmup
)
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.utils.class_weight import compute_class_weight
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
import logging
import json
import os
from tqdm import tqdm
import joblib

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ErrorDataset(Dataset):
    """错误分析数据集"""
    
    def __init__(self, 
                 texts: List[str], 
                 labels: np.ndarray,
                 features: np.ndarray,
                 tokenizer,
                 max_length: int = 512):
        """
        初始化数据集
        
        Args:
            texts: 文本列表
            labels: 标签数组
            features: 手工特征矩阵
            tokenizer: 分词器
            max_length: 最大序列长度
        """
        self.texts = texts
        self.labels = labels
        self.features = features
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        feature = self.features[idx] if len(self.features) > 0 else np.array([])
        
        # 文本编码
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'features': torch.FloatTensor(feature),
            'labels': torch.LongTensor([label])
        }

class HybridErrorClassifier(nn.Module):
    """
    混合错误分类器
    
    结合Transformer文本编码和手工特征的混合模型
    """
    
    def __init__(self, 
                 model_name: str,
                 num_classes: int,
                 feature_dim: int,
                 dropout_rate: float = 0.3,
                 hidden_dim: int = 256):
        """
        初始化模型
        
        Args:
            model_name: 预训练模型名称
            num_classes: 分类类别数
            feature_dim: 手工特征维度
            dropout_rate: Dropout比率
            hidden_dim: 隐藏层维度
        """
        super(HybridErrorClassifier, self).__init__()
        
        self.model_name = model_name
        self.num_classes = num_classes
        self.feature_dim = feature_dim
        
        # 加载预训练模型
        self.bert = AutoModel.from_pretrained(model_name)
        self.bert_dim = self.bert.config.hidden_size
        
        # 文本特征处理
        self.text_dropout = nn.Dropout(dropout_rate)
        self.text_fc = nn.Linear(self.bert_dim, hidden_dim)
        
        # 手工特征处理
        if feature_dim > 0:
            self.feature_fc = nn.Linear(feature_dim, hidden_dim // 2)
            self.feature_dropout = nn.Dropout(dropout_rate)
            combined_dim = hidden_dim + hidden_dim // 2
        else:
            combined_dim = hidden_dim
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(combined_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in [self.text_fc, self.classifier]:
            if hasattr(module, 'weight'):
                nn.init.xavier_uniform_(module.weight)
            if hasattr(module, 'bias') and module.bias is not None:
                nn.init.constant_(module.bias, 0)
    
    def forward(self, input_ids, attention_mask, features=None):
        """
        前向传播
        
        Args:
            input_ids: 输入token ids
            attention_mask: 注意力掩码
            features: 手工特征
            
        Returns:
            分类logits
        """
        # BERT编码
        bert_output = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = bert_output.pooler_output
        
        # 文本特征处理
        text_features = self.text_dropout(pooled_output)
        text_features = torch.relu(self.text_fc(text_features))
        
        # 特征融合
        if self.feature_dim > 0 and features is not None and features.size(1) > 0:
            manual_features = self.feature_dropout(features)
            manual_features = torch.relu(self.feature_fc(manual_features))
            combined_features = torch.cat([text_features, manual_features], dim=1)
        else:
            combined_features = text_features
        
        # 分类
        logits = self.classifier(combined_features)
        return logits

class ModelTrainer:
    """
    模型训练器
    
    负责模型的训练、验证、保存和加载
    """
    
    def __init__(self,
                 model_name: str = "bert-base-chinese",
                 max_length: int = 512,
                 batch_size: int = 16,
                 learning_rate: float = 2e-5,
                 num_epochs: int = 10,
                 warmup_steps: int = 100,
                 weight_decay: float = 0.01,
                 device: str = None):
        """
        初始化训练器
        
        Args:
            model_name: 预训练模型名称
            max_length: 最大序列长度
            batch_size: 批次大小
            learning_rate: 学习率
            num_epochs: 训练轮数
            warmup_steps: 预热步数
            weight_decay: 权重衰减
            device: 设备
        """
        self.model_name = model_name
        self.max_length = max_length
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.num_epochs = num_epochs
        self.warmup_steps = warmup_steps
        self.weight_decay = weight_decay
        
        # 设备配置
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        logger.info(f"使用设备: {self.device}")
        
        # 初始化组件
        self.tokenizer = None
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.class_weights = None
        
        # 训练历史
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'val_accuracy': [],
            'val_f1': []
        }
    
    def load_tokenizer(self):
        """加载分词器"""
        if self.tokenizer is None:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            logger.info(f"加载分词器: {self.model_name}")
    
    def prepare_model(self, num_classes: int, feature_dim: int):
        """
        准备模型
        
        Args:
            num_classes: 分类类别数
            feature_dim: 特征维度
        """
        self.load_tokenizer()
        
        # 创建模型
        self.model = HybridErrorClassifier(
            model_name=self.model_name,
            num_classes=num_classes,
            feature_dim=feature_dim
        ).to(self.device)
        
        logger.info(f"创建混合分类器: {num_classes} 类, {feature_dim} 维特征")
        
        # 计算模型参数量
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        logger.info(f"模型参数: 总计 {total_params:,}, 可训练 {trainable_params:,}")
    
    def prepare_data_loaders(self, 
                           texts: List[str], 
                           labels: np.ndarray,
                           features: np.ndarray,
                           test_size: float = 0.2,
                           random_state: int = 42) -> Tuple[DataLoader, DataLoader]:
        """
        准备数据加载器
        
        Args:
            texts: 文本列表
            labels: 标签数组
            features: 特征矩阵
            test_size: 测试集比例
            random_state: 随机种子
            
        Returns:
            (训练数据加载器, 验证数据加载器)
        """
        # 分割数据
        try:
            # 检查是否可以进行分层抽样
            unique_labels, label_counts = np.unique(labels, return_counts=True)
            min_count = np.min(label_counts)

            if len(texts) > 10 and min_count >= 2:
                # 可以进行分层抽样
                train_texts, val_texts, train_labels, val_labels, train_features, val_features = train_test_split(
                    texts, labels, features,
                    test_size=test_size,
                    random_state=random_state,
                    stratify=labels
                )
            else:
                # 数据太少或类别分布不均，使用简单随机分割或全部数据
                if len(texts) > 5:
                    train_texts, val_texts, train_labels, val_labels, train_features, val_features = train_test_split(
                        texts, labels, features,
                        test_size=test_size,
                        random_state=random_state
                    )
                else:
                    # 数据太少，使用全部数据训练，部分数据验证
                    train_texts = val_texts = texts
                    train_labels = val_labels = labels
                    train_features = val_features = features
        except Exception as e:
            logger.warning(f"数据分割失败，使用全部数据: {e}")
            train_texts = val_texts = texts
            train_labels = val_labels = labels
            train_features = val_features = features
        
        logger.info(f"数据分割: 训练集 {len(train_texts)}, 验证集 {len(val_texts)}")
        
        # 计算类别权重
        unique_labels = np.unique(train_labels)
        num_classes = len(np.unique(labels))  # 使用总的类别数

        # 为所有类别计算权重
        class_weights = np.ones(num_classes)
        if len(unique_labels) > 1:
            computed_weights = compute_class_weight(
                'balanced',
                classes=unique_labels,
                y=train_labels
            )
            # 将计算出的权重分配到对应的类别
            for i, label in enumerate(unique_labels):
                class_weights[label] = computed_weights[i]

        self.class_weights = torch.FloatTensor(class_weights).to(self.device)
        
        # 创建数据集
        train_dataset = ErrorDataset(train_texts, train_labels, train_features, 
                                   self.tokenizer, self.max_length)
        val_dataset = ErrorDataset(val_texts, val_labels, val_features, 
                                 self.tokenizer, self.max_length)
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)
        
        return train_loader, val_loader

    def train_epoch(self, train_loader: DataLoader) -> float:
        """
        训练一个epoch

        Args:
            train_loader: 训练数据加载器

        Returns:
            平均训练损失
        """
        self.model.train()
        total_loss = 0
        num_batches = len(train_loader)

        progress_bar = tqdm(train_loader, desc="训练中")

        for batch in progress_bar:
            # 数据移到设备
            input_ids = batch['input_ids'].to(self.device)
            attention_mask = batch['attention_mask'].to(self.device)
            features = batch['features'].to(self.device)
            labels = batch['labels'].to(self.device).squeeze()

            # 前向传播
            self.optimizer.zero_grad()
            logits = self.model(input_ids, attention_mask, features)

            # 计算损失（使用类别权重）
            criterion = nn.CrossEntropyLoss(weight=self.class_weights)
            loss = criterion(logits, labels)

            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()

            if self.scheduler:
                self.scheduler.step()

            total_loss += loss.item()
            progress_bar.set_postfix({'loss': f'{loss.item():.4f}'})

        return total_loss / num_batches

    def validate_epoch(self, val_loader: DataLoader) -> Tuple[float, float, Dict]:
        """
        验证一个epoch

        Args:
            val_loader: 验证数据加载器

        Returns:
            (平均验证损失, 准确率, 详细指标)
        """
        self.model.eval()
        total_loss = 0
        all_predictions = []
        all_labels = []

        with torch.no_grad():
            for batch in tqdm(val_loader, desc="验证中"):
                # 数据移到设备
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                features = batch['features'].to(self.device)
                labels = batch['labels'].to(self.device).squeeze()

                # 前向传播
                logits = self.model(input_ids, attention_mask, features)

                # 计算损失
                criterion = nn.CrossEntropyLoss(weight=self.class_weights)
                loss = criterion(logits, labels)
                total_loss += loss.item()

                # 预测
                predictions = torch.argmax(logits, dim=1)
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

        # 计算指标
        avg_loss = total_loss / len(val_loader)
        accuracy = accuracy_score(all_labels, all_predictions)

        # 详细分类报告
        report = classification_report(all_labels, all_predictions, output_dict=True, zero_division=0)

        return avg_loss, accuracy, report

    def train(self,
              texts: List[str],
              labels: np.ndarray,
              features: np.ndarray,
              save_path: str = "models/error_classifier") -> Dict:
        """
        训练模型

        Args:
            texts: 文本列表
            labels: 标签数组
            features: 特征矩阵
            save_path: 模型保存路径

        Returns:
            训练历史
        """
        logger.info("开始模型训练...")

        # 准备模型
        num_classes = len(np.unique(labels))
        feature_dim = features.shape[1] if len(features) > 0 else 0
        self.prepare_model(num_classes, feature_dim)

        # 准备数据
        train_loader, val_loader = self.prepare_data_loaders(texts, labels, features)

        # 准备优化器和调度器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay
        )

        total_steps = len(train_loader) * self.num_epochs
        self.scheduler = get_linear_schedule_with_warmup(
            self.optimizer,
            num_warmup_steps=self.warmup_steps,
            num_training_steps=total_steps
        )

        # 训练循环
        best_accuracy = 0
        best_model_state = None

        for epoch in range(self.num_epochs):
            logger.info(f"Epoch {epoch + 1}/{self.num_epochs}")

            # 训练
            train_loss = self.train_epoch(train_loader)

            # 验证
            val_loss, val_accuracy, val_report = self.validate_epoch(val_loader)

            # 记录历史
            self.training_history['train_loss'].append(train_loss)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_accuracy'].append(val_accuracy)
            self.training_history['val_f1'].append(val_report['macro avg']['f1-score'])

            # 保存最佳模型
            if val_accuracy > best_accuracy:
                best_accuracy = val_accuracy
                best_model_state = self.model.state_dict().copy()

            logger.info(f"训练损失: {train_loss:.4f}, 验证损失: {val_loss:.4f}, "
                       f"验证准确率: {val_accuracy:.4f}, 最佳准确率: {best_accuracy:.4f}")

        # 恢复最佳模型
        if best_model_state:
            self.model.load_state_dict(best_model_state)

        # 保存模型
        self.save_model(save_path)

        logger.info(f"训练完成！最佳验证准确率: {best_accuracy:.4f}")

        return self.training_history

    def save_model(self, save_path: str):
        """
        保存模型

        Args:
            save_path: 保存路径
        """
        os.makedirs(save_path, exist_ok=True)

        # 保存模型权重
        torch.save(self.model.state_dict(), os.path.join(save_path, "model.pth"))

        # 保存模型配置
        config = {
            'model_name': self.model_name,
            'num_classes': self.model.num_classes,
            'feature_dim': self.model.feature_dim,
            'max_length': self.max_length,
            'class_weights': self.class_weights.cpu().tolist() if self.class_weights is not None else None
        }

        with open(os.path.join(save_path, "config.json"), 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        # 保存训练历史
        with open(os.path.join(save_path, "training_history.json"), 'w', encoding='utf-8') as f:
            json.dump(self.training_history, f, ensure_ascii=False, indent=2)

        # 保存分词器
        self.tokenizer.save_pretrained(save_path)

        logger.info(f"模型已保存到: {save_path}")

    def load_model(self, load_path: str):
        """
        加载模型

        Args:
            load_path: 加载路径
        """
        # 加载配置
        with open(os.path.join(load_path, "config.json"), 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 重建模型
        self.model_name = config['model_name']
        self.max_length = config['max_length']

        self.model = HybridErrorClassifier(
            model_name=config['model_name'],
            num_classes=config['num_classes'],
            feature_dim=config['feature_dim']
        ).to(self.device)

        # 加载权重
        self.model.load_state_dict(torch.load(
            os.path.join(load_path, "model.pth"),
            map_location=self.device
        ))

        # 加载分词器
        self.tokenizer = AutoTokenizer.from_pretrained(load_path)

        # 加载类别权重
        if config['class_weights']:
            self.class_weights = torch.FloatTensor(config['class_weights']).to(self.device)

        logger.info(f"模型已从 {load_path} 加载")

        return config
