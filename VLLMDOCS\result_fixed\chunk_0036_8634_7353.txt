# 文档路径: 01-getting-started > 08-quickstart > 快速入门 > 兼容 OpenAI 服务器 > 在 vLLM 中使用 OpenAI Chat API

## 兼容 OpenAI 服务器

### 在 vLLM 中使用 OpenAI Chat API


vLLM 服务器在设计上支持 OpenAI Chat API，允许您与模型进行动态对话。聊天界面是一种与模型交流更具交互性的方式，可以进行来回交流，并将对话历史存储下来。这对于需要上下文或更详细解释的任务非常有用。

使用 OpenAI Chat API 查询模型：

您可以使用[创建聊天补全 (create chat completion)](https://platform.openai.com/docs/api-reference/chat/completions/create) 端点在类似聊天的界面中与模型进行交流：

```plain
curl http://localhost:8000/v1/chat/completions \
    -H "Content-Type: application/json" \
    -d '{
        "model": "facebook/opt-125m",
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Who won the world series in 2020?"}
        ]
    }'
```

Python 客户端示例：

使用 _openai_ 的 python 包，您还可以以类似聊天的方式与模型进行交流:

```python
from openai import OpenAI
# Set OpenAI's API key and API base to use vLLM's API server.

# 使用 vLLM 的 API 服务器需要设置 OpenAI 的 API 密钥和 API 库。

openai_api_key = "EMPTY"
openai_api_base = "http://localhost:8000/v1"

client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)

chat_response = client.chat.completions.create(
    model="facebook/opt-125m",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Tell me a joke."},
    ]
)
print("Chat response:", chat_response)
```

有关 chat API 的更深入示例和高级功能，您可以参考 OpenAI 官方文档。
