# 文档路径: 03-models > 05-using-lora-adapters > 使用 LoRA 适配器 > LoRA 适配器服务

本文档向您展示如何在基本模型之上将 [LoRA 适配器](https://arxiv.org/abs/2106.09685) 与 vLLM 结合使用。

LoRA 适配器可与任何实现了 `SupportsLoRA` 的 vLLM 模型一起使用。

适配器能以最小的开销根据每个请求有效地服务。首先，我们下载适配器并将其保存在本地：

```python
from huggingface_hub import snapshot_download


sql_lora_path = snapshot_download(repo_id="yard1/llama-2-7b-sql-lora-test")
```

然后我们实例化基础模型并传入 `enable_lora=True` 标志:

```python
from vllm import LLM, SamplingParams
from vllm.lora.request import LoRARequest


llm = LLM(model="meta-llama/Llama-2-7b-hf", enable_lora=True)
```

我们现在可以提交提示并使用 `lora_request` 参数调用 `llm.generate` 。`LoRARequest` 的第一个参数是人为可识别的名称，第二个参数是适配器的全局唯一 ID，第三个参数是 LoRA 适配器的路径。

```python
sampling_params = SamplingParams(
    temperature=0,
    max_tokens=256,
    stop=["[/assistant]"]
)


prompts = [
     "[user] Write a SQL query to answer the question based on the table schema.\n\n context: CREATE TABLE table_name_74 (icao VARCHAR, airport VARCHAR)\n\n question: Name the ICAO for lilongwe international airport [/user] [assistant]",
     "[user] Write a SQL query to answer the question based on the table schema.\n\n context: CREATE TABLE table_name_11 (nationality VARCHAR, elector VARCHAR)\n\n question: When Anchero Pantaleone was the elector what is under nationality? [/user] [assistant]",
]


outputs = llm.generate(
    prompts,
    sampling_params,
    lora_request=LoRARequest("sql_adapter", 1, sql_lora_path)
)
```

查看 [examples/multilora_inference.py](https://github.com/vllm-project/vllm/blob/main/examples/multilora_inference.py)，了解如何将 LoRA 适配器与异步引擎结合使用以及如何使用更高级的配置选项。

## LoRA 适配器服务


LoRA 适配模型也可以通过 Open-AI 兼容的 vLLM 服务器提供服务。为此，我们在启动服务器时使用 `--lora-modules {name}={path} {name}={path}` 来指定每个 LoRA 模块：

```bash
vllm serve meta-llama/Llama-2-7b-hf \
    --enable-lora \
    --lora-modules sql-lora=$HOME/.cache/huggingface/hub/models--yard1--llama-2-7b-sql-lora-test/snapshots/0dfa347e8877a4d4ed19ee56c140fa518470028c/
```

**注意：**
提交 ID _0dfa347e8877a4d4ed19ee56c140fa518470028c_ 可能会随着时间的推移而改变。请检查您环境中的最新提交 ID，以确保您使用的是正确的提交 ID。

服务器入口点接受所有其他 LoRA 配置参数（`max_loras` 、 `max_lora_rank` 、 `max_cpu_loras` 等），这些参数将应用于所有即将到来的请求。查询 `/models` 端点后，我们应该看到 LoRA 及其基本模型：

```bash
curl localhost:8000/v1/models | jq .
{
    "object": "list",
    "data": [
        {
            "id": "meta-llama/Llama-2-7b-hf",
            "object": "model",
            ...
        },
        {
            "id": "sql-lora",
            "object": "model",
            ...
        }
    ]
}
```

请求可以通过 `model` 请求参数指定 LoRA 适配器，就像指定任何其他模型一样。这些请求将根据服务器端的 LoRA 配置进行处理（即与基础模型请求并行处理，如果提供了其他 LoRA 适配器请求且 `max_loras` 设置得足够高，也可能与其他 LoRA 适配器请求并行处理）。

以下是请求示例：

```bash
curl http://localhost:8000/v1/completions \
    -H "Content-Type: application/json" \
    -d '{
        "model": "sql-lora",
        "prompt": "San Francisco is a",
        "max_tokens": 7,
        "temperature": 0
    }' | jq
```
