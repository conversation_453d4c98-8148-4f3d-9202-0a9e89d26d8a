# 文档路径: 03-models > 02-adding-a-new-model > 添加新模型 > 6. 树外模型集成

## 6. 树外模型集成


我们还提供了一种无需修改 vLLM 代码库即可集成模型的方法。步骤 2、3、4 仍然是必需的，但您可以跳过步骤 1 和 5。

只需在代码中添加以下行：

```python
from vllm import ModelRegistry
from your_code import YourModelForCausalLM
ModelRegistry.register_model("YourModelForCausalLM", YourModelForCausalLM)
```

如果您的模型导入了初始化 CUDA 的模块，建议您改为延迟加载这些模块，以避免出现类似`RuntimeError: Cannot re-initialize CUDA in forked subprocess` 的错误。

```plain
from vllm import ModelRegistry


ModelRegistry.register_model("YourModelForCausalLM", "your_code:YourModelForCausalLM")


```

**重要**

如果您的模型是多模态模型，请确保模型类实现了 `SupportsMultiModal` 接口。

更多信息可点击[此处](https://docs.vllm.ai/en/latest/models/enabling_multimodal_inputs.html#enabling-multimodal-inputs)查阅。

如果您使用 `vllmserve <args>` 运行 API 服务器，则可以使用以下代码包装入口点：

```python
from vllm import ModelRegistry
from your_code import YourModelForCausalLM
ModelRegistry.register_model("YourModelForCausalLM", YourModelForCausalLM)
import runpy
runpy.run_module('vllm.entrypoints.openai.api_server', run_name='__main__')
```

将上述代码保存在文件中并使用 `python your_file.py <args>` 运行它。
