# 文档路径: 03-models > 03-enabling-multimodal-inputs > 启用多模态输入 > 1. 更新基础 vLLM 模型

本文档将引导您完成扩展 vLLM 模型的步骤，以便它接受「[多模态](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#multi-modality) 」 输入。

**另见**

`添加新模型`

## 1. 更新基础 vLLM 模型


假设您已经按照「这些步骤（添加新模型）」在 vLLM 中实现了模型。进一步更新模型的步骤如下：

- 实现 `SupportsMultiModal` 接口。

```diff
    + from vllm.model_executor.models.interfaces import SupportsMultiModal


    - class YourModelForImage2Seq(nn.Module):
    + class YourModelForImage2Seq(nn.Module, SupportsMultiModal):
```

**注意：**

模型类不必命名为 `*ForCausalLM`。可查看 [HuggingFace Transformers 文档](https://huggingface.co/docs/transformers/model_doc/auto#multimodal) 获取一些示例。

- 如果您还没有这样做，请为对应于多模态输入的每个输入张量在 `forward()` 中保留一个关键字参数，如下例所示:

```diff
    def forward(
        self,
        input_ids: torch.Tensor,
        positions: torch.Tensor,
        kv_caches: List[torch.Tensor],
        attn_metadata: AttentionMetadata,
    +     pixel_values: torch.Tensor,
    ) -> SamplerOutput:
```
