# 文档路径: 07-developer-documentation > 05-input-processing > 01-model_inputs_index > 输入处理 > 指南

每个模型都可以通过 [INPUT_REGISTRY](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.INPUT_REGISTRY) 和 [MULTIMODAL_REGISTRY](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MULTIMODAL_REGISTRY) 覆盖 vLLM 的输入处理管道 [input_processing_pipeline](https://docs.vllm.ai/en/latest/dev/input_processing/input_processing_pipeline.html#input-processing-pipeline) 的部分内容。

目前，这种机制仅在多模态模型中用于预处理[多模态](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#multi-modality)输入数据以及输入提示，但如有需要，也可以扩展到仅处理文本的语言模型。

## 指南


[输入处理管道](https://docs.vllm.ai/en/latest/dev/input_processing/input_processing_pipeline.html)
