# 文档路径: 03-models > 02-adding-a-new-model > 添加新模型 > 1. 引入你的模型代码

## 1. 引入你的模型代码


从 HuggingFace Transformers 存储库克隆 PyTorch 模型代码，并将其放入 [vllm/model_executor/models](https://github.com/vllm-project/vllm/tree/main/vllm/model_executor/models) 目录中。例如，vLLM 的 [OPT 模型](https://github.com/vllm-project/vllm/blob/main/vllm/model_executor/models/opt.py) 就是从 HuggingFace 的 [modeling_opt .py](https://github.com/huggingface/transformers/blob/main/src/transformers/models/opt/modeling_opt.py) 文件中改编而来的。

**警告**

复制模型代码时，请务必查看并遵守代码的版权和许可条款。
