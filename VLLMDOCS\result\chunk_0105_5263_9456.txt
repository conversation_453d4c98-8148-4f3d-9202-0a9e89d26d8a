# 文档路径: 04-quantization > 01-supported_hardware > 量化内核支持的硬件

下表展示了 vLLM 中各种量化实现与不同硬件平台的兼容性情况：

| Implementation        | Volta | Turing | Ampere | Ada | Hopper | AMD GPU | Intel GPU | x86 CPU | AWS Inferentia | Google TPU |
| :-------------------- | :---- | :----- | :----- | :-- | :----- | :------ | :-------- | :------ | :------------- | :--------- |
| AWQ                   | ✗     | ✅︎    | ✅︎    | ✅︎ | ✅︎    | ✗       | ✗         | ✅︎     | ✗              | ✗          |
| GPTQ                  | ✅︎   | ✅︎    | ✅︎    | ✅︎ | ✅︎    | ✗       | ✗         | ✗       | ✗              | ✗          |
| Marlin (GPTQ/AWQ/FP8) | ✗     | ✗      | ✅︎    | ✅︎ | ✅︎    | ✗       | ✗         | ✗       | ✗              | ✗          |
| INT8 (W8A8)           | ✗     | ✅︎    | ✅︎    | ✅︎ | ✅︎    | ✗       | ✗         | ✅︎     | ✗              | ✗          |
| FP8 (W8A8)            | ✗     | ✗      | ✗      | ✅︎ | ✅︎    | ✅︎     | ✗         | ✗       | ✗              | ✗          |
| AQLM                  | ✅︎   | ✅︎    | ✅︎    | ✅︎ | ✅︎    | ✗       | ✗         | ✗       | ✗              | ✗          |
| bitsandbytes          | ✅︎   | ✅︎    | ✅︎    | ✅︎ | ✅︎    | ✗       | ✗         | ✗       | ✗              | ✗          |
| DeepSpeedFP           | ✅︎   | ✅︎    | ✅︎    | ✅︎ | ✅︎    | ✗       | ✗         | ✗       | ✗              | ✗          |
| GGUF                  | ✅︎   | ✅︎    | ✅︎    | ✅︎ | ✅︎    | ✗       | ✗         | ✗       | ✗              | ✗          |