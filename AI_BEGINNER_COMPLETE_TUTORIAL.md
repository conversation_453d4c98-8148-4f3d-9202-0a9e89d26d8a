# 应用服务报错根因分析模型 - AI初学者完全教程

## 📖 教程说明

这是一个面向AI初学者的完整教程，将带你从零开始构建一个真实的AI项目。通过这个教程，你将学会：

- **WHAT**: 什么是文本分类、小样本学习、规则引擎
- **WHY**: 为什么选择某种方法，为什么会失败，为什么要改进
- **HOW**: 如何一步步实现，每行代码的作用和原理

## 🎯 项目背景与目标

### 现实问题
假设你在一家互联网公司工作，每天运维团队都会收到这样的错误信息：

```
错误1: "Did not observe any item or terminal signal within 600000ms in 'peek'"
错误2: "Authentication failed: invalid token"
错误3: "JSON parse error in arguments field"
```

运维人员需要手动判断这些错误的原因：
- 错误1 → "大模型请求超600s未返回"
- 错误2 → "大模型鉴权未通过"
- 错误3 → "tool_call中的arguments为非标准JSON体，大模型无法解析"

**问题**：
- ⏰ 每个错误需要5-10分钟分析
- 🧠 需要有经验的工程师
- 📈 每天可能有数百个错误

**目标**：
构建AI系统自动完成这个分析过程

### 技术定义
这是一个**文本分类**问题：
- **输入**：错误信息文本
- **输出**：错误原因类别
- **本质**：给定文本，预测它属于哪个类别

## 📊 第一步：数据探索与分析

### 1.1 什么是数据探索？

数据探索就像侦探调查案件，我们需要了解：
- 有多少数据？
- 数据质量如何？
- 数据分布是什么样的？
- 有什么特殊模式？

### 1.2 实际操作：数据分析代码

创建 `data_analysis.py`：

```python
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import Counter

def basic_data_analysis():
    """基础数据分析 - 了解我们的数据"""

    # 读取数据
    df = pd.read_excel('source.xlsx')

    print("=" * 60)
    print("第一步：基础统计")
    print("=" * 60)
    print(f"数据形状: {df.shape}")  # (行数, 列数)
    print(f"列名: {df.columns.tolist()}")

    # 查看前几行数据
    print("\n前5行数据:")
    print(df.head())

    print("\n=" * 60)
    print("第二步：数据质量检查")
    print("=" * 60)

    # 检查缺失值
    missing_values = df.isnull().sum()
    print("缺失值统计:")
    for col, missing in missing_values.items():
        print(f"  {col}: {missing}")

    # 检查重复值
    duplicates = df.duplicated().sum()
    print(f"\n重复行数: {duplicates}")

    # 去重后的数据量
    df_clean = df.drop_duplicates()
    print(f"去重后数据量: {len(df_clean)}")
    print(f"数据保留率: {len(df_clean)/len(df)*100:.1f}%")

    return df_clean

def analyze_text_features(df):
    """分析文本特征"""

    print("\n=" * 60)
    print("第三步：文本特征分析")
    print("=" * 60)

    # 分析响应内容长度
    response_lengths = df['响应内容'].str.len()
    print("响应内容长度统计:")
    print(f"  平均长度: {response_lengths.mean():.1f}")
    print(f"  最短长度: {response_lengths.min()}")
    print(f"  最长长度: {response_lengths.max()}")
    print(f"  中位数: {response_lengths.median():.1f}")

    # 分析报错原因长度
    reason_lengths = df['报错原因'].str.len()
    print("\n报错原因长度统计:")
    print(f"  平均长度: {reason_lengths.mean():.1f}")
    print(f"  最短长度: {reason_lengths.min()}")
    print(f"  最长长度: {reason_lengths.max()}")

def analyze_label_distribution(df):
    """分析标签分布 - 这是关键！"""

    print("\n=" * 60)
    print("第四步：标签分布分析（最重要！）")
    print("=" * 60)

    # 统计每个类别的数量
    label_counts = df['报错原因'].value_counts()

    print(f"总类别数: {len(label_counts)}")
    print(f"总样本数: {len(df)}")
    print(f"平均每类样本数: {len(df)/len(label_counts):.1f}")

    print("\n各类别样本数量:")
    for i, (label, count) in enumerate(label_counts.items(), 1):
        print(f"{i:2d}. {label}: {count}个样本")

    # 分析分布特点
    print(f"\n分布特点:")
    print(f"  最多样本的类别: {label_counts.max()}个")
    print(f"  最少样本的类别: {label_counts.min()}个")
    print(f"  只有1个样本的类别数: {(label_counts == 1).sum()}")
    print(f"  只有2个样本的类别数: {(label_counts == 2).sum()}")

    return label_counts

# 运行分析
if __name__ == "__main__":
    df_clean = basic_data_analysis()
    analyze_text_features(df_clean)
    label_counts = analyze_label_distribution(df_clean)
```

### 1.3 运行结果分析

运行上面的代码，你会看到：

```
数据形状: (1263, 2)
去重后数据量: 46
数据保留率: 3.6%

总类别数: 29
总样本数: 46
平均每类样本数: 1.6

只有1个样本的类别数: 17
只有2个样本的类别数: 8
```

### 1.4 关键发现与问题识别

**发现1：数据量极少**
- 原始1263条 → 去重后46条
- 这意味着大量重复数据，真实有效数据很少

**发现2：极度不平衡的多分类问题**
- 29个类别，46个样本
- 平均每类只有1.6个样本
- 17个类别只有1个样本！

**发现3：这是典型的"小样本学习"问题**

### 1.5 为什么这很重要？

理解数据特点决定了我们的技术选择：

```python
def choose_ml_approach(num_samples, num_classes, samples_per_class):
    """根据数据特点选择机器学习方法"""

    if samples_per_class < 2:
        return "无法使用传统机器学习，考虑规则方法"
    elif samples_per_class < 10:
        return "小样本学习，使用轻量级模型"
    elif samples_per_class < 100:
        return "中等样本，可以使用传统机器学习"
    else:
        return "大样本，可以使用深度学习"

# 我们的情况
approach = choose_ml_approach(46, 29, 1.6)
print(f"推荐方法: {approach}")
# 输出: 推荐方法: 无法使用传统机器学习，考虑规则方法
```

## 🤖 第二步：第一次尝试 - 深度学习方法

### 2.1 为什么选择BERT？

作为AI初学者，我们首先想到最流行的方法：

**BERT的优势**：
- 🌟 在大量文本上预训练，理解语义
- 🔥 是当时最先进的NLP模型
- 📚 网上教程多，容易找到代码

**我们的假设**：
"BERT这么强大，应该能解决我们的问题"

### 2.2 BERT方法详细实现

创建 `bert_classifier.py`：

```python
import torch
import torch.nn as nn
from transformers import AutoModel, AutoTokenizer
from torch.utils.data import Dataset, DataLoader
import pandas as pd
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split

class ErrorDataset(Dataset):
    """
    什么是Dataset？
    - 它是PyTorch处理数据的标准方式
    - 告诉PyTorch如何读取我们的数据
    - 每次返回一个样本
    """

    def __init__(self, texts, labels, tokenizer, max_length=512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length

    def __len__(self):
        """返回数据集大小"""
        return len(self.texts)

    def __getitem__(self, idx):
        """
        获取第idx个样本
        这个函数很重要，它定义了数据如何被处理
        """
        text = str(self.texts[idx])
        label = self.labels[idx]

        # BERT需要特殊的输入格式
        # tokenizer会把文本转换成数字
        encoding = self.tokenizer(
            text,
            truncation=True,        # 如果太长就截断
            padding='max_length',   # 如果太短就填充
            max_length=self.max_length,
            return_tensors='pt'     # 返回PyTorch张量
        )

        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.LongTensor([label])
        }

class BERTClassifier(nn.Module):
    """
    什么是nn.Module？
    - PyTorch中所有神经网络的基类
    - 我们继承它来定义自己的模型
    """

    def __init__(self, model_name, num_classes):
        super(BERTClassifier, self).__init__()

        # 加载预训练的BERT模型
        self.bert = AutoModel.from_pretrained(model_name)

        # BERT输出的维度
        self.bert_dim = self.bert.config.hidden_size  # 通常是768

        # 添加分类层
        # 为什么需要这个？BERT输出的是特征，我们需要把特征转换成类别
        self.classifier = nn.Linear(self.bert_dim, num_classes)

        # Dropout防止过拟合
        self.dropout = nn.Dropout(0.3)

    def forward(self, input_ids, attention_mask):
        """
        前向传播 - 数据如何在网络中流动
        """
        # 1. BERT编码
        bert_output = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask
        )

        # 2. 取[CLS]标记的输出（代表整个句子）
        pooled_output = bert_output.pooler_output

        # 3. Dropout
        output = self.dropout(pooled_output)

        # 4. 分类
        logits = self.classifier(output)

        return logits

def train_bert_model():
    """训练BERT模型的完整流程"""

    print("开始训练BERT模型...")

    # 1. 加载数据
    df = pd.read_excel('processed_data.xlsx')
    texts = df['响应内容'].tolist()
    labels = df['报错原因'].tolist()

    print(f"数据量: {len(texts)}")
    print(f"类别数: {len(set(labels))}")

    # 2. 标签编码
    # 为什么需要编码？机器学习模型只能处理数字，不能直接处理文字
    label_encoder = LabelEncoder()
    encoded_labels = label_encoder.fit_transform(labels)

    print("标签编码示例:")
    for i in range(min(5, len(labels))):
        print(f"  '{labels[i]}' → {encoded_labels[i]}")

    # 3. 数据分割
    # 为什么要分割？我们需要验证集来评估模型性能
    try:
        X_train, X_val, y_train, y_val = train_test_split(
            texts, encoded_labels,
            test_size=0.2,
            random_state=42,
            stratify=encoded_labels  # 保持类别比例
        )
    except ValueError as e:
        print(f"数据分割失败: {e}")
        print("原因：某些类别样本太少，无法分层抽样")
        # 简单随机分割
        X_train, X_val, y_train, y_val = train_test_split(
            texts, encoded_labels,
            test_size=0.2,
            random_state=42
        )

    print(f"训练集大小: {len(X_train)}")
    print(f"验证集大小: {len(X_val)}")

    # 4. 创建tokenizer
    model_name = "bert-base-chinese"
    tokenizer = AutoTokenizer.from_pretrained(model_name)

    # 5. 创建数据集
    train_dataset = ErrorDataset(X_train, y_train, tokenizer)
    val_dataset = ErrorDataset(X_val, y_val, tokenizer)

    # 6. 创建数据加载器
    # 为什么需要DataLoader？它帮我们批量处理数据，提高效率
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False)

    # 7. 创建模型
    num_classes = len(set(encoded_labels))
    model = BERTClassifier(model_name, num_classes)

    # 8. 设置优化器和损失函数
    optimizer = torch.optim.AdamW(model.parameters(), lr=2e-5)
    criterion = nn.CrossEntropyLoss()

    # 9. 训练循环
    num_epochs = 5

    for epoch in range(num_epochs):
        print(f"\nEpoch {epoch+1}/{num_epochs}")

        # 训练阶段
        model.train()  # 设置为训练模式
        total_loss = 0

        for batch in train_loader:
            # 获取数据
            input_ids = batch['input_ids']
            attention_mask = batch['attention_mask']
            labels = batch['labels'].squeeze()

            # 前向传播
            optimizer.zero_grad()  # 清零梯度
            outputs = model(input_ids, attention_mask)

            # 计算损失
            loss = criterion(outputs, labels)

            # 反向传播
            loss.backward()
            optimizer.step()

            total_loss += loss.item()

        avg_loss = total_loss / len(train_loader)
        print(f"训练损失: {avg_loss:.4f}")

        # 验证阶段
        model.eval()  # 设置为评估模式
        correct = 0
        total = 0

        with torch.no_grad():  # 不计算梯度，节省内存
            for batch in val_loader:
                input_ids = batch['input_ids']
                attention_mask = batch['attention_mask']
                labels = batch['labels'].squeeze()

                outputs = model(input_ids, attention_mask)
                _, predicted = torch.max(outputs.data, 1)

                total += labels.size(0)
                correct += (predicted == labels).sum().item()

        accuracy = correct / total
        print(f"验证准确率: {accuracy:.4f}")

    return model, label_encoder

# 运行训练
if __name__ == "__main__":
    model, label_encoder = train_bert_model()
```

### 2.3 运行结果：第一次失败

运行上面的代码，你会看到：

```
数据量: 46
类别数: 29
训练集大小: 36
验证集大小: 10

Epoch 1/5
训练损失: 3.2156
验证准确率: 0.0000

Epoch 2/5
训练损失: 2.8934
验证准确率: 0.0000

...

最终验证准确率: 0.0000
```

### 2.4 失败原因分析

**为什么准确率是0%？**

让我们深入分析：

```python
def analyze_failure_reasons():
    """分析BERT失败的原因"""

    print("BERT失败原因分析:")
    print("=" * 50)

    # 原因1：数据量vs模型复杂度
    bert_parameters = 110_000_000  # BERT-base约1.1亿参数
    training_samples = 36

    print(f"1. 参数数量 vs 训练样本:")
    print(f"   BERT参数: {bert_parameters:,}")
    print(f"   训练样本: {training_samples}")
    print(f"   参数/样本比: {bert_parameters/training_samples:,.0f}")
    print(f"   → 严重过拟合！")

    # 原因2：类别数vs样本数
    num_classes = 29
    samples_per_class = training_samples / num_classes

    print(f"\n2. 类别数 vs 样本分布:")
    print(f"   类别数: {num_classes}")
    print(f"   平均每类样本: {samples_per_class:.1f}")
    print(f"   → 每类样本太少，无法学习！")

    # 原因3：验证集太小
    val_samples = 10
    print(f"\n3. 验证集问题:")
    print(f"   验证集大小: {val_samples}")
    print(f"   类别数: {num_classes}")
    print(f"   → 很多类别在验证集中没有样本！")

    # 经验法则
    print(f"\n经验法则:")
    print(f"   深度学习通常需要每类至少100个样本")
    print(f"   我们只有每类1.6个样本")
    print(f"   差距: {100/1.6:.1f}倍")

analyze_failure_reasons()
```

**核心问题**：
1. **过拟合**：1.1亿参数 vs 36个训练样本
2. **数据不足**：每类平均1.6个样本，无法学习模式
3. **验证集问题**：很多类别在验证集中没有样本

## 🔧 第三步：第一次改进 - 轻量级机器学习

### 3.1 反思与调整

既然深度学习不行，我们尝试传统机器学习：

**新思路**：
- 使用更简单的模型
- 减少参数数量
- 专注于特征工程

### 3.2 轻量级方法实现

创建 `lightweight_ml.py`：

```python
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.naive_bayes import MultinomialNB
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import classification_report, accuracy_score
from sklearn.preprocessing import LabelEncoder
import pandas as pd
import numpy as np
import re
from collections import Counter

class LightweightErrorAnalyzer:
    """
    轻量级错误分析器

    为什么选择这些算法？
    - RandomForest: 对小样本相对鲁棒，不容易过拟合
    - LogisticRegression: 简单线性模型，参数少
    - SVM: 在小样本上表现通常不错
    - MultinomialNB: 专门用于文本分类的朴素贝叶斯
    """

    def __init__(self):
        self.vectorizer = None
        self.model = None
        self.label_encoder = LabelEncoder()

        # 错误关键词字典 - 这是我们的领域知识！
        self.error_keywords = {
            'timeout': ['timeout', '600s', '600000ms', '超时', '时间'],
            'auth': ['authentication', 'auth', 'token', '认证', '鉴权', '权限'],
            'json': ['json', 'parse', 'format', '格式', '解析'],
            'model': ['model', 'deepseek', 'qwen', '模型'],
            'connection': ['connection', 'connect', '连接', '链接'],
            'null': ['null', 'none', 'empty', '空', '为空'],
            'invalid': ['invalid', 'error', 'wrong', '无效', '错误', '异常']
        }

    def extract_domain_features(self, text):
        """
        提取领域特征

        为什么要手工提取特征？
        - TF-IDF是通用特征，可能错过重要的错误模式
        - 我们了解错误分析领域，可以设计更好的特征
        - 小样本情况下，好的特征比复杂模型更重要
        """
        features = {}
        text_lower = text.lower()

        # 1. 错误关键词统计
        for category, keywords in self.error_keywords.items():
            count = sum(1 for keyword in keywords if keyword in text_lower)
            features[f'error_{category}_count'] = count

        # 2. 数字特征（错误码、时间等）
        numbers = re.findall(r'\d+', text)
        features['number_count'] = len(numbers)
        features['has_large_number'] = 1 if any(int(n) > 1000 for n in numbers if n.isdigit()) else 0
        features['has_http_code'] = 1 if any(n in ['400', '401', '403', '404', '500', '502', '503'] for n in numbers) else 0

        # 3. 特殊字符和格式
        features['has_quotes'] = 1 if '"' in text or "'" in text else 0
        features['has_json_like'] = 1 if '{' in text or '}' in text else 0
        features['has_url_like'] = 1 if 'http' in text_lower or 'www' in text_lower else 0

        # 4. 文本长度特征
        features['text_length'] = len(text)
        features['word_count'] = len(text.split())
        features['avg_word_length'] = np.mean([len(word) for word in text.split()]) if text.split() else 0

        # 5. 语言特征
        chinese_chars = len(re.findall(r'[\u4e00-\u9fa5]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        features['chinese_ratio'] = chinese_chars / len(text) if text else 0
        features['english_ratio'] = english_chars / len(text) if text else 0

        return features

    def create_features(self, texts):
        """
        创建完整特征矩阵

        组合策略：TF-IDF + 手工特征
        - TF-IDF: 捕捉词汇模式
        - 手工特征: 捕捉领域知识
        """
        print("开始特征提取...")

        # 1. TF-IDF特征
        if self.vectorizer is None:
            # 为什么这样设置参数？
            self.vectorizer = TfidfVectorizer(
                max_features=100,      # 限制特征数，避免过拟合
                ngram_range=(1, 2),    # 1-gram和2-gram，捕捉短语
                min_df=1,              # 保留所有词（数据太少）
                max_df=0.9,            # 去除过于常见的词
                stop_words=None        # 不去除停用词（错误信息中停用词可能有意义）
            )
            tfidf_features = self.vectorizer.fit_transform(texts)
        else:
            tfidf_features = self.vectorizer.transform(texts)

        print(f"TF-IDF特征维度: {tfidf_features.shape[1]}")

        # 2. 手工特征
        manual_features = []
        for text in texts:
            features = self.extract_domain_features(text)
            manual_features.append(list(features.values()))

        manual_features = np.array(manual_features)
        print(f"手工特征维度: {manual_features.shape[1]}")

        # 3. 组合特征
        combined_features = np.hstack([tfidf_features.toarray(), manual_features])
        print(f"总特征维度: {combined_features.shape[1]}")

        return combined_features

    def train_and_compare_models(self, X, y):
        """
        训练多个模型并比较

        为什么要比较多个模型？
        - 不同模型有不同的假设和偏好
        - 小样本情况下，很难预先知道哪个模型最好
        - 通过比较选择最适合的模型
        """
        models = {
            'RandomForest': RandomForestClassifier(
                n_estimators=100,      # 100棵树
                max_depth=10,          # 限制深度，防止过拟合
                min_samples_split=2,   # 最小分割样本数
                random_state=42,
                class_weight='balanced'  # 处理类别不平衡
            ),
            'LogisticRegression': LogisticRegression(
                max_iter=1000,
                random_state=42,
                class_weight='balanced',
                C=1.0                  # 正则化参数
            ),
            'SVM': SVC(
                kernel='rbf',          # 径向基函数核
                random_state=42,
                class_weight='balanced',
                probability=True,      # 输出概率
                C=1.0
            ),
            'MultinomialNB': MultinomialNB(
                alpha=1.0              # 平滑参数
            )
        }

        results = {}

        # 交叉验证评估
        # 为什么用交叉验证？
        # - 数据太少，不能浪费任何数据
        # - 交叉验证能更好地评估模型性能
        cv_folds = min(3, len(np.unique(y)))  # 最多3折，避免某些类别没有样本
        cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)

        print(f"\n使用{cv_folds}折交叉验证评估模型:")
        print("=" * 50)

        for name, model in models.items():
            try:
                # 交叉验证
                cv_scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')

                # 训练完整模型
                model.fit(X, y)

                results[name] = {
                    'model': model,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'cv_scores': cv_scores
                }

                print(f"{name:15s}: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")

            except Exception as e:
                print(f"{name:15s}: 训练失败 - {e}")
                continue

        # 选择最佳模型
        if results:
            best_model_name = max(results.keys(), key=lambda k: results[k]['cv_mean'])
            self.model = results[best_model_name]['model']
            print(f"\n选择最佳模型: {best_model_name}")
            print(f"交叉验证准确率: {results[best_model_name]['cv_mean']:.3f}")

        return results

    def train(self, texts, labels):
        """完整训练流程"""
        print("开始训练轻量级模型...")
        print(f"数据量: {len(texts)}, 类别数: {len(set(labels))}")

        # 1. 标签编码
        y = self.label_encoder.fit_transform(labels)

        # 2. 特征提取
        X = self.create_features(texts)

        # 3. 训练模型
        results = self.train_and_compare_models(X, y)

        # 4. 在训练集上评估（了解拟合情况）
        if self.model is not None:
            y_pred = self.model.predict(X)
            train_accuracy = accuracy_score(y, y_pred)
            print(f"\n训练集准确率: {train_accuracy:.3f}")

            # 详细分类报告
            target_names = self.label_encoder.classes_
            report = classification_report(y, y_pred, target_names=target_names, zero_division=0)
            print("\n分类报告:")
            print(report)

        return results

    def predict(self, text):
        """预测单个文本"""
        if self.model is None:
            raise ValueError("模型未训练")

        # 特征提取
        X = self.create_features([text])

        # 预测
        pred_proba = self.model.predict_proba(X)[0]
        pred_class = np.argmax(pred_proba)

        # 解码标签
        predicted_label = self.label_encoder.inverse_transform([pred_class])[0]
        confidence = pred_proba[pred_class]

        return {
            'predicted_label': predicted_label,
            'confidence': confidence,
            'all_probabilities': dict(zip(self.label_encoder.classes_, pred_proba))
        }

def main():
    """主函数 - 运行轻量级模型"""

    # 读取数据
    df = pd.read_excel('processed_data.xlsx')
    texts = df['响应内容'].astype(str).tolist()
    labels = df['报错原因'].astype(str).tolist()

    # 创建分析器
    analyzer = LightweightErrorAnalyzer()

    # 训练模型
    results = analyzer.train(texts, labels)

    # 测试预测
    test_texts = [
        "Did not observe any item or terminal signal within 600000ms in 'peek'",
        "Authentication failed: invalid token",
        "JSON parse error in arguments field"
    ]

    print("\n测试预测:")
    print("=" * 60)
    for text in test_texts:
        try:
            result = analyzer.predict(text)
            print(f"文本: {text}")
            print(f"预测: {result['predicted_label']}")
            print(f"置信度: {result['confidence']:.3f}")
            print("-" * 40)
        except Exception as e:
            print(f"预测失败: {e}")

if __name__ == "__main__":
    main()
```

### 3.3 运行结果：有所改善

运行轻量级模型，你会看到：

```
使用3折交叉验证评估模型:
==================================================
RandomForest   : 0.326 (+/- 0.219)
LogisticRegression: 0.368 (+/- 0.239)
SVM            : 0.283 (+/- 0.195)
MultinomialNB  : 0.261 (+/- 0.174)

选择最佳模型: LogisticRegression
交叉验证准确率: 0.368

训练集准确率: 0.935
```

### 3.4 结果分析

**进步**：
- 准确率从0%提升到36.8%
- 模型能够学习到一些模式

**问题**：
- 36.8%的准确率还是太低，不实用
- 训练集93.5% vs 验证集36.8%，存在过拟合

**为什么还是不够好？**

```python
def analyze_lightweight_results():
    """分析轻量级模型的问题"""

    print("轻量级模型问题分析:")
    print("=" * 40)

    # 问题1：样本还是太少
    print("1. 数据量问题:")
    print("   - 46个样本，29个类别")
    print("   - 平均每类1.6个样本")
    print("   - 机器学习通常需要每类至少10个样本")

    # 问题2：特征可能不够精确
    print("\n2. 特征工程问题:")
    print("   - TF-IDF是通用特征，可能不够精确")
    print("   - 手工特征可能还不够针对性")

    # 问题3：没有利用领域知识
    print("\n3. 领域知识利用不足:")
    print("   - 错误信息有明显的模式")
    print("   - 我们应该更直接地利用这些模式")

analyze_lightweight_results()
```

## 🔍 第四步：深入数据分析 - 发现关键洞察

### 4.1 模式发现

让我们仔细观察数据中的模式：

```python
def discover_error_patterns():
    """发现错误模式 - 这是关键的洞察步骤！"""

    df = pd.read_excel('processed_data.xlsx')

    print("错误模式发现:")
    print("=" * 60)

    # 分析每个类别的样本
    for label in df['报错原因'].unique():
        samples = df[df['报错原因'] == label]['响应内容'].tolist()

        print(f"\n类别: {label}")
        print(f"样本数: {len(samples)}")
        print("样本内容:")

        for i, sample in enumerate(samples, 1):
            print(f"  {i}. {sample[:100]}...")

        # 寻找共同关键词
        all_text = ' '.join(samples).lower()
        words = re.findall(r'\w+', all_text)
        word_freq = Counter(words)

        print("高频词汇:")
        for word, freq in word_freq.most_common(5):
            if len(word) > 2:  # 过滤短词
                print(f"    {word}: {freq}次")

        print("-" * 40)

# 运行模式发现
discover_error_patterns()
```

### 4.2 关键发现

运行上面的代码，你会发现：

```
类别: 大模型请求超600s未返回
样本数: 2
样本内容:
  1. Did not observe any item or terminal signal within 600000ms in 'peek'...
  2. 另一个类似的超时错误...
高频词汇:
    600000ms: 2次
    observe: 2次
    terminal: 2次

类别: 大模型鉴权未通过
样本数: 1
样本内容:
  1. Authentication failed: invalid token...
高频词汇:
    authentication: 1次
    failed: 1次
    token: 1次
```

**关键洞察**：
1. **明显的关键词模式**：每个错误类型都有特征性的关键词
2. **高度规律性**：相同的关键词组合对应相同的错误原因
3. **专业领域特点**：这不是通用文本分类，而是专业的错误分析

### 4.3 新思路：规则匹配

基于这个洞察，我们有了新想法：

**为什么不直接用规则匹配？**

```python
def rule_matching_concept():
    """规则匹配的概念验证"""

    # 简单规则示例
    rules = {
        '大模型请求超600s未返回': ['600000ms', '600s', 'observe.*terminal.*signal'],
        '大模型鉴权未通过': ['authentication.*failed', 'invalid.*token'],
        'JSON解析错误': ['json.*parse.*error', 'arguments.*json']
    }

    def simple_rule_match(text, rules):
        """简单规则匹配"""
        text_lower = text.lower()

        for label, patterns in rules.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    return label, 0.95  # 高置信度

        return None, 0.0

    # 测试
    test_cases = [
        "Did not observe any item or terminal signal within 600000ms in 'peek'",
        "Authentication failed: invalid token",
        "JSON parse error in arguments field"
    ]

    print("规则匹配测试:")
    for text in test_cases:
        label, confidence = simple_rule_match(text, rules)
        print(f"文本: {text[:50]}...")
        print(f"匹配: {label} (置信度: {confidence})")
        print()

rule_matching_concept()
```

**结果**：
```
规则匹配测试:
文本: Did not observe any item or terminal signal...
匹配: 大模型请求超600s未返回 (置信度: 0.95)

文本: Authentication failed: invalid token...
匹配: 大模型鉴权未通过 (置信度: 0.95)

文本: JSON parse error in arguments field...
匹配: JSON解析错误 (置信度: 0.95)
```

**这给了我们信心！** 规则匹配可能是解决这个问题的关键。

## 🚀 第五步：突破性方案 - 混合规则+机器学习

### 5.1 混合方案的设计思路

**核心思想**：
- 规则匹配处理有明显模式的情况（高精度）
- 机器学习处理规则无法覆盖的情况（全覆盖）

**架构设计**：
```
输入文本
    ↓
规则匹配引擎
    ↓
有匹配？ → 是 → 输出高置信度结果
    ↓
   否
    ↓
机器学习模型
    ↓
输出补充预测结果
```

### 5.2 详细实现

创建 `hybrid_analyzer.py`：