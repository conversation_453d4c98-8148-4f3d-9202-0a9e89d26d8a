# 文档路径: 04-quantization > 05-int8-w8a8 > INT8 W8A8 > 依赖

vLLM 支持将权重和激活量化为 INT8，这不仅有助于节省内存，还能加快推理速度。这种量化方法可以有效地在减小模型大小的同时保持良好的性能。

请访问 Hugging Face 上[由流行的大语言模型的量化 INT8 检查点组成的集合](https://huggingface.co/collections/neuralmagic/int8-llms-for-vllm-668ec32c049dca0369816415)，这些检查点可随时与 vLLM 一起使用。

**注意：**

INT8 计算在计算能力大于 7.5 的 NVIDIA GPU (Turing、Ampere、Ada Lovelace、Hopper) 上得到支持。

## 依赖


如需将 INT8 量化与 vLLM 结合使用，您需要安装 [llm-compressor](https://github.com/vllm-project/llm-compressor/) 库：

```plain
pip install llmcompressor==0.1.0
```
