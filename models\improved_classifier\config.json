{"feature_names": ["tfidf_0", "tfidf_1", "tfidf_2", "tfidf_3", "tfidf_4", "tfidf_5", "tfidf_6", "tfidf_7", "tfidf_8", "tfidf_9", "tfidf_10", "tfidf_11", "tfidf_12", "tfidf_13", "tfidf_14", "tfidf_15", "tfidf_16", "tfidf_17", "tfidf_18", "tfidf_19", "tfidf_20", "tfidf_21", "tfidf_22", "tfidf_23", "tfidf_24", "tfidf_25", "tfidf_26", "tfidf_27", "tfidf_28", "tfidf_29", "tfidf_30", "tfidf_31", "tfidf_32", "tfidf_33", "tfidf_34", "tfidf_35", "tfidf_36", "tfidf_37", "tfidf_38", "tfidf_39", "tfidf_40", "tfidf_41", "tfidf_42", "tfidf_43", "tfidf_44", "tfidf_45", "tfidf_46", "tfidf_47", "tfidf_48", "tfidf_49", "tfidf_50", "tfidf_51", "tfidf_52", "tfidf_53", "tfidf_54", "tfidf_55", "tfidf_56", "tfidf_57", "tfidf_58", "tfidf_59", "tfidf_60", "tfidf_61", "tfidf_62", "tfidf_63", "tfidf_64", "tfidf_65", "tfidf_66", "tfidf_67", "tfidf_68", "tfidf_69", "tfidf_70", "tfidf_71", "tfidf_72", "tfidf_73", "tfidf_74", "tfidf_75", "tfidf_76", "tfidf_77", "tfidf_78", "tfidf_79", "tfidf_80", "tfidf_81", "tfidf_82", "tfidf_83", "tfidf_84", "tfidf_85", "tfidf_86", "tfidf_87", "tfidf_88", "tfidf_89", "tfidf_90", "tfidf_91", "tfidf_92", "tfidf_93", "tfidf_94", "tfidf_95", "tfidf_96", "tfidf_97", "tfidf_98", "tfidf_99", "tfidf_100", "tfidf_101", "tfidf_102", "tfidf_103", "tfidf_104", "tfidf_105", "tfidf_106", "tfidf_107", "tfidf_108", "tfidf_109", "tfidf_110", "tfidf_111", "tfidf_112", "tfidf_113", "tfidf_114", "tfidf_115", "tfidf_116", "tfidf_117", "tfidf_118", "tfidf_119", "tfidf_120", "tfidf_121", "tfidf_122", "tfidf_123", "tfidf_124", "tfidf_125", "tfidf_126", "tfidf_127", "tfidf_128", "tfidf_129", "tfidf_130", "tfidf_131", "tfidf_132", "tfidf_133", "tfidf_134", "tfidf_135", "tfidf_136", "tfidf_137", "tfidf_138", "tfidf_139", "tfidf_140", "tfidf_141", "tfidf_142", "tfidf_143", "tfidf_144", "tfidf_145", "tfidf_146", "tfidf_147", "tfidf_148", "tfidf_149", "tfidf_150", "tfidf_151", "tfidf_152", "tfidf_153", "tfidf_154", "tfidf_155", "tfidf_156", "tfidf_157", "tfidf_158", "tfidf_159", "tfidf_160", "tfidf_161", "tfidf_162", "tfidf_163", "tfidf_164", "tfidf_165", "tfidf_166", "tfidf_167", "tfidf_168", "tfidf_169", "tfidf_170", "tfidf_171", "tfidf_172", "tfidf_173", "tfidf_174", "tfidf_175", "tfidf_176", "tfidf_177", "tfidf_178", "tfidf_179", "tfidf_180", "tfidf_181", "tfidf_182", "tfidf_183", "tfidf_184", "tfidf_185", "tfidf_186", "tfidf_187", "tfidf_188", "tfidf_189", "tfidf_190", "tfidf_191", "tfidf_192", "tfidf_193", "tfidf_194", "tfidf_195", "tfidf_196", "tfidf_197", "tfidf_198", "tfidf_199", "pattern_超时", "pattern_连接", "pattern_认证", "pattern_格式", "pattern_模型", "pattern_请求", "pattern_响应", "pattern_参数", "pattern_多模态", "pattern_工具", "number_count", "has_large_number", "has_http_code", "has_quotes", "has_json_like", "has_url_like", "text_length", "word_count", "avg_word_length", "chinese_ratio", "english_ratio", "error_indicator_count"], "label_mapping": {"0": "tool_call中的arguments为非标准JSON体，大模型无法解析", "1": "入参中填写tools，但实际未配置工具", "2": "向量化模型不支持自定义dimensions参数", "3": "响应体数据超过默认的缓冲区限制", "4": "图片base64编码数据部分未进行正确填充", "5": "图片错误", "6": "多模态使用中type为text时，text为一个对象", "7": "多模态使用中type为text时，text为数字", "8": "大模型600S还没回答完，导致超时关闭连接", "9": "大模型回答是null", "10": "大模型要求回答输出tokens数超大，导致请求tokens+回答tokens超过上下文tokens数量。", "11": "大模型请求超600s未返回", "12": "大模型鉴权未通过", "13": "对话类大模型messages被错误嵌套", "14": "当前使用模型不支持多模态输入", "15": "模型不支持content中为list的数据格式", "16": "模型服务返回异常回答", "17": "用户主动切断大模型回复", "18": "请求体中arguments字段未进行转义", "19": "请求体中content为数组", "20": "请求体中content为空", "21": "请求体中top_p为0", "22": "请求体中模型名称错误", "23": "请求体中缺少必要的message", "24": "请求体使用function call时缺少arguments字段", "25": "请求体的message中无内容", "26": "请求体输入内容过长（前置判断，非大模型判断）", "27": "请求模型与请求地址未匹配", "28": "非标准JSON入参"}, "error_patterns": {"超时": ["timeout", "600s", "600000ms", "time", "超时", "时间"], "连接": ["connection", "connect", "连接", "链接"], "认证": ["auth", "authentication", "token", "认证", "鉴权", "权限"], "格式": ["json", "parse", "format", "格式", "解析", "arguments"], "模型": ["model", "embedding", "deepseek", "qwen", "模型"], "请求": ["request", "body", "content", "请求", "内容"], "响应": ["response", "answer", "null", "响应", "回答"], "参数": ["parameter", "param", "top_p", "dimensions", "参数"], "多模态": ["multimodal", "image", "text", "type", "多模态", "图片"], "工具": ["tool", "function", "call", "工具", "函数"]}}