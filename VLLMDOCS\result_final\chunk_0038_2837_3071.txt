# 文档路径: 02-serving > 03-distributed-inference-and-serving > 分布式推理和服务 > 如何决定分布式推理策略？

## 如何决定分布式推理策略？


在详细介绍分布式推理和服务之前，我们首先明确何时使用分布式推理以及有哪些可用的策略。以下是常见的做法：

- **单 GPU（无分布式推理）**: 如果您的模型可以在单个 GPU 中运行，那么您可能不需要使用分布式推理。只需使用单个 GPU 即可运行推理。
- **单节点多 GPU（张量并行推理）**: 如果您的模型太大而无法在单个 GPU 中运行，但可以在具有多个 GPU 的单个节点中运行，则可以使用张量并行。张量并行大小是您要使用的 GPU 数量。例如，如果单个节点中有 4 个 GPU，则可以将张量并行大小设置为 4。
- **多节点多 GPU（张量并行加管道并行推理）**: 如果您的模型太大而无法在单个节点中运行，您可以将张量并行与管道并行结合使用。张量并行大小是每个节点要使用的 GPU 数量，管道并行大小是要使用的节点数量。例如，如果 2 个节点中有 16 个 GPU（每个节点 8 个 GPU），则可以将张量并行大小设置为 8，将管道并行大小设置为 2。

简而言之，您应该增加 GPU 数量和节点数量，直到有足够的 GPU 内存来容纳模型。张量并行大小应为每个节点中的 GPU 数量，管道并行大小应为节点数量。

在添加了足够的 GPU 和节点来容纳模型后，您可以先运行 vLLM，它会打印一些日志，例如 `# GPU blocks: 790` 。将数字乘以 `16` （块大小），您可以大致得到当前配置上能够处理的最大 tokens 数量。如果这个数字不令人满意，例如你想要更高的吞吐量，可以进一步增加 GPU 或节点的数量，直到块的数量足够为止。

**注意：**

有一种特殊情况：如果模型适合在具有多个 GPU 的单个节点中运行，但 GPU 的数量无法均匀划分模型大小，则可以使用管道并行性，它将模型沿层分割并支持不均匀分割。在这种情况下，张量并行大小应为 1，管道并行大小应为 GPU 数量。
