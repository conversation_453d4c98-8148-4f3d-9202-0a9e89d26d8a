# 文档路径: 08-indices-and-tables > 01-index > 索引 > I

## I


| [image (vllm.multimodal.MultiModalDataBuiltins attribute)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalDataBuiltins.image)                                | [ImagePlugin (class in vllm.multimodal.image)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.image.ImagePlugin)              |
| :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [init_mm_limits_per_prompt() (vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.init_mm_limits_per_prompt) | [INPUT_REGISTRY (in module vllm.inputs)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.INPUT_REGISTRY)                   |
| [InputContext (class in vllm.inputs.registry)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputContext)                                               | [InputProcessor (in module vllm.inputs.registry)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputProcessor) |
| [InputRegistry (class in vllm.inputs.registry)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputRegistry)                                             |                                                                                                                                                                    |
