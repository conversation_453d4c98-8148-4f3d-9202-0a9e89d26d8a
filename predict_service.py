"""
应用服务报错根因分析模型 - 预测服务

提供单条和批量预测接口，支持命令行和API调用
"""

import argparse
import json
import os
import pandas as pd
import logging
from typing import List, Dict, Any, Optional
from core.predictor import Predictor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PredictionService:
    """
    预测服务类
    
    提供便捷的预测接口
    """
    
    def __init__(self, model_path: str):
        """
        初始化预测服务
        
        Args:
            model_path: 模型路径
        """
        self.model_path = model_path
        self.predictor = None
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        try:
            logger.info(f"加载模型: {self.model_path}")
            self.predictor = Predictor(self.model_path)
            
            # 加载标签映射
            processor_config_path = os.path.join(self.model_path, 'processor_config.json')
            if os.path.exists(processor_config_path):
                with open(processor_config_path, 'r', encoding='utf-8') as f:
                    processor_config = json.load(f)
                self.predictor.set_label_mapping(processor_config['label_mapping'])
                logger.info("成功加载标签映射")
            
            logger.info("模型加载完成")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def predict_single(self, text: str, explain: bool = False) -> Dict[str, Any]:
        """
        单条预测
        
        Args:
            text: 输入文本
            explain: 是否提供解释
            
        Returns:
            预测结果
        """
        if not self.predictor:
            raise RuntimeError("模型未加载")
        
        # 执行预测
        result = self.predictor.predict_single(text, return_probabilities=True)
        
        # 添加解释
        if explain and 'error' not in result:
            explanation = self.predictor.explain_prediction(text, result)
            result['explanation'] = explanation
        
        return result
    
    def predict_batch(self, 
                     texts: List[str], 
                     output_path: Optional[str] = None,
                     explain: bool = False) -> List[Dict[str, Any]]:
        """
        批量预测
        
        Args:
            texts: 文本列表
            output_path: 输出文件路径
            explain: 是否提供解释
            
        Returns:
            预测结果列表
        """
        if not self.predictor:
            raise RuntimeError("模型未加载")
        
        # 执行批量预测
        results = self.predictor.predict_batch(texts, return_probabilities=True)
        
        # 添加解释
        if explain:
            for i, (text, result) in enumerate(zip(texts, results)):
                if 'error' not in result:
                    explanation = self.predictor.explain_prediction(text, result)
                    results[i]['explanation'] = explanation
        
        # 保存结果
        if output_path:
            self.predictor.save_predictions(texts, results, output_path)
        
        return results
    
    def predict_from_file(self, 
                         input_path: str, 
                         output_path: str,
                         text_column: str = '响应内容',
                         explain: bool = False) -> Dict[str, Any]:
        """
        从文件读取数据进行预测
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            text_column: 文本列名
            explain: 是否提供解释
            
        Returns:
            预测统计信息
        """
        logger.info(f"从文件预测: {input_path}")
        
        # 读取数据
        if input_path.endswith('.xlsx'):
            df = pd.read_excel(input_path)
        elif input_path.endswith('.csv'):
            df = pd.read_csv(input_path)
        else:
            raise ValueError("不支持的文件格式")
        
        if text_column not in df.columns:
            raise ValueError(f"文件中不存在列: {text_column}")
        
        texts = df[text_column].astype(str).tolist()
        
        # 批量预测
        results = self.predict_batch(texts, explain=explain)
        
        # 构建输出数据
        output_data = []
        for i, (text, result) in enumerate(zip(texts, results)):
            row = df.iloc[i].to_dict()  # 保留原始数据
            row.update({
                '预测类别': result.get('predicted_label', '未知'),
                '置信度': result.get('confidence', 0),
                '是否可信': result.get('is_confident', False),
                '推理时间': result.get('inference_time', 0)
            })
            
            # 添加Top3预测
            if 'top_3_predictions' in result:
                for j, (label, prob) in enumerate(result['top_3_predictions'][:3], 1):
                    row[f'Top{j}_标签'] = label
                    row[f'Top{j}_概率'] = prob
            
            # 添加解释信息
            if explain and 'explanation' in result:
                exp = result['explanation']
                row['关键特征'] = '; '.join([f"{f['feature']}:{f['value']}" for f in exp['key_features']])
                row['错误指示词'] = '; '.join([f"{e['category']}:{','.join(e['keywords'])}" for e in exp['error_indicators']])
            
            output_data.append(row)
        
        # 保存结果
        output_df = pd.DataFrame(output_data)
        output_df.to_excel(output_path, index=False)
        
        # 统计信息
        confidence_analysis = self.predictor.analyze_prediction_confidence(results)
        
        stats = {
            'total_predictions': len(results),
            'input_file': input_path,
            'output_file': output_path,
            'confidence_analysis': confidence_analysis
        }
        
        logger.info(f"预测完成，结果保存到: {output_path}")
        logger.info(f"总预测数: {stats['total_predictions']}")
        logger.info(f"平均置信度: {confidence_analysis['avg_confidence']:.3f}")
        logger.info(f"可信预测率: {confidence_analysis['confidence_rate']:.3f}")
        
        return stats
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        if not self.predictor:
            raise RuntimeError("模型未加载")
        
        config_path = os.path.join(self.model_path, 'config.json')
        processor_config_path = os.path.join(self.model_path, 'processor_config.json')
        
        info = {
            'model_path': self.model_path,
            'device': str(self.predictor.device)
        }
        
        # 加载模型配置
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                model_config = json.load(f)
            info['model_config'] = model_config
        
        # 加载处理器配置
        if os.path.exists(processor_config_path):
            with open(processor_config_path, 'r', encoding='utf-8') as f:
                processor_config = json.load(f)
            info['processor_config'] = processor_config
            info['num_classes'] = len(processor_config.get('label_mapping', {}))
            info['class_labels'] = list(processor_config.get('label_mapping', {}).values())
        
        return info

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='应用服务报错根因分析预测服务')
    parser.add_argument('--model', type=str, required=True,
                       help='模型路径')
    parser.add_argument('--mode', type=str, choices=['single', 'batch', 'file'], default='single',
                       help='预测模式')
    parser.add_argument('--text', type=str, default=None,
                       help='单条预测的文本内容')
    parser.add_argument('--input', type=str, default=None,
                       help='输入文件路径（batch或file模式）')
    parser.add_argument('--output', type=str, default=None,
                       help='输出文件路径')
    parser.add_argument('--text-column', type=str, default='响应内容',
                       help='文本列名（file模式）')
    parser.add_argument('--explain', action='store_true',
                       help='是否提供预测解释')
    parser.add_argument('--info', action='store_true',
                       help='显示模型信息')
    
    args = parser.parse_args()
    
    try:
        # 创建预测服务
        service = PredictionService(args.model)
        
        # 显示模型信息
        if args.info:
            info = service.get_model_info()
            print("=" * 60)
            print("模型信息")
            print("=" * 60)
            print(f"模型路径: {info['model_path']}")
            print(f"设备: {info['device']}")
            if 'num_classes' in info:
                print(f"分类类别数: {info['num_classes']}")
                print(f"类别标签: {', '.join(info['class_labels'])}")
            print("=" * 60)
            return
        
        # 执行预测
        if args.mode == 'single':
            if not args.text:
                args.text = input("请输入要预测的文本: ")
            
            result = service.predict_single(args.text, explain=args.explain)
            
            print("=" * 60)
            print("预测结果")
            print("=" * 60)
            print(f"输入文本: {args.text}")
            print(f"预测类别: {result.get('predicted_label', '未知')}")
            print(f"置信度: {result.get('confidence', 0):.3f}")
            print(f"是否可信: {'是' if result.get('is_confident', False) else '否'}")
            
            if 'top_3_predictions' in result:
                print("\nTop 3 预测:")
                for i, (label, prob) in enumerate(result['top_3_predictions'][:3], 1):
                    print(f"  {i}. {label}: {prob:.3f}")
            
            if args.explain and 'explanation' in result:
                exp = result['explanation']
                print("\n预测解释:")
                if exp['key_features']:
                    print("  关键特征:")
                    for feature in exp['key_features']:
                        print(f"    - {feature['description']}: {feature['value']}")
                if exp['error_indicators']:
                    print("  错误指示词:")
                    for indicator in exp['error_indicators']:
                        print(f"    - {indicator['description']}: {', '.join(indicator['keywords'])}")
            
            print("=" * 60)
        
        elif args.mode == 'batch':
            if not args.input:
                raise ValueError("batch模式需要指定输入文件")
            
            # 从文件读取文本列表
            with open(args.input, 'r', encoding='utf-8') as f:
                texts = [line.strip() for line in f if line.strip()]
            
            results = service.predict_batch(texts, args.output, explain=args.explain)
            
            print(f"批量预测完成，处理了 {len(results)} 条文本")
            if args.output:
                print(f"结果已保存到: {args.output}")
        
        elif args.mode == 'file':
            if not args.input:
                raise ValueError("file模式需要指定输入文件")
            if not args.output:
                args.output = args.input.replace('.xlsx', '_predicted.xlsx').replace('.csv', '_predicted.csv')
            
            stats = service.predict_from_file(
                args.input, args.output, args.text_column, explain=args.explain
            )
            
            print("=" * 60)
            print("批量预测统计")
            print("=" * 60)
            print(f"输入文件: {stats['input_file']}")
            print(f"输出文件: {stats['output_file']}")
            print(f"预测总数: {stats['total_predictions']}")
            print(f"平均置信度: {stats['confidence_analysis']['avg_confidence']:.3f}")
            print(f"可信预测率: {stats['confidence_analysis']['confidence_rate']:.3f}")
            print("=" * 60)
        
    except Exception as e:
        logger.error(f"预测失败: {e}")
        raise

if __name__ == "__main__":
    main()
