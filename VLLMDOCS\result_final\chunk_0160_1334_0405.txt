# 文档路径: 10-tutorials > 04-vllm-langchain-tutorial > 将 LangChain 与 vLLM 结合使用：完整教程 > 4. 利用多 GPU 推理进行扩展

## 4. 利用多 GPU 推理进行扩展


如果您正在使用本地托管的大型模型，则可能需要利用多个 GPU 进行推理。特别是对于需要同时处理许多请求的高吞吐量系统。vLLM 允许这样做：分布式张量并行推理，以帮助扩展操作。

要运行多 GPU 推理，请 tensor_parallel_size 在初始化 VLLM 类时使用该参数。

```
del llm

clean_memory(deep=True)
```

```
from langchain_community.llms import VLLM

# Running inference on multiple GPUs
llm = VLLM(
    model="/input0/Qwen2.5-1.5B-Instruct",
    tensor_parallel_size=1,  # using 1 GPUs
    trust_remote_code=True,
)

print(llm.invoke("What is the future of AI?"))
```

对于较大的模型，强烈建议使用此方法，因为它的计算量很大，而且在单个 GPU 上运行速度太慢。
