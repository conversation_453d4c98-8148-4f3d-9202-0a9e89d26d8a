# 应用服务报错根因分析模型 - Docker镜像

FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 安装UV包管理器
RUN pip install --no-cache-dir uv

# 复制项目文件
COPY pyproject.toml uv.lock ./
COPY core/ ./core/
COPY config/ ./config/
COPY scripts/ ./scripts/
COPY *.py ./
COPY README.md ./

# 安装Python依赖
RUN uv sync --no-dev

# 创建必要的目录
RUN mkdir -p models data logs outputs

# 设置权限
RUN chmod +x scripts/*.py

# 暴露端口（如果需要Web服务）
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import torch; print('Health check passed')" || exit 1

# 默认命令
CMD ["uv", "run", "python", "predict_service.py", "--model", "models/error_classifier", "--mode", "single"]

# 构建信息
LABEL maintainer="LogModel Team <<EMAIL>>"
LABEL version="0.1.0"
LABEL description="应用服务报错根因分析模型"
