# 文档路径: 08-indices-and-tables > 01-index > 索引 > P

## P


| [process_input() (vllm.inputs.registry.InputRegistry method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputRegistry.process_input) | [prompt (vllm.inputs.LLMInputs attribute)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.LLMInputs.prompt)                     |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [(vllm.inputs.TextPrompt attribute)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TextPrompt.prompt)                                                    | [prompt_token_ids (vllm.inputs.LLMInputs attribute)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.LLMInputs.prompt_token_ids) |
| [(vllm.inputs.TokensPrompt attribute)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TokensPrompt.prompt_token_ids)                                      | [PromptType (in module vllm.inputs)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.PromptType)                                        |
