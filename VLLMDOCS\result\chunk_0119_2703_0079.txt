# 文档路径: 04-quantization > 06-fp8-w8a8 > FP8 W8A8 > 量化过程 > 2. 应用量化

## 量化过程

量化过程涉及 3 个主要步骤:

1. 加载模型

2. 应用量化

3. 评估 vLLM 的准确性


### 2. 应用量化

对于 FP8 量化，可以通过简单的 RTN 量化来恢复精度。我们建议使用 `FP8_DYNAMIC` 方案定位所有 `Linear` 层，该方案使用：

- 静态，权重上的每通道量化
- 动态，激活值上的每 token 量化

由于简单的 RTN 在权重量化时不需要数据，，并且激活值是动态量化的，因此我们不需要此量化流程中的任何校准数据。

```python
from llmcompressor.transformers import oneshot
from llmcompressor.modifiers.quantization import QuantizationModifier


# Configure the simple PTQ quantization
# 配置简单 PTQ 量化


recipe = QuantizationModifier(
  targets="Linear", scheme="FP8_DYNAMIC", ignore=["lm_head"])


# Apply the quantization algorithm.
# 应用量化算法。


oneshot(model=model, recipe=recipe)


# Save the model.
# 保存模型。


SAVE_DIR = MODEL_ID.split("/")[1] + "-FP8-Dynamic"
model.save_pretrained(SAVE_DIR)
tokenizer.save_pretrained(SAVE_DIR)
```
