# 文档路径: 10-tutorials > 02-infer-34b-with-vllm > 使用 vLLM 对 Qwen2.5 推理 > 目录

[在线运行此教程](https://openbayes.com/console/hyperai-tutorials/containers/wmGLO8o5IiV)

该教程详细展示了如何完成一个 3B 参数的大语言模型的推理任务，包括模型的加载、数据的准备、推理过程的优化，以及结果的提取和评估。

## 目录


- [1.安装 vllm](#1.安装vllm)
- [2.使用vLLM加载Qwen量化模型](#2.使用vLLM加载Qwen量化模型)
- [3.加载测试数据](#3.加载测试数据)
- [4.提示工程](#4.提示工程)
- [5.Infer测试](#5.Infer测试)
- [6.提取推理概率](#6.提取推理概率)
- [7.创建提交CSV](#7.创建提交CSV)
- [8.计算CV分数](#8.计算CV分数)
