# 文档路径: 07-developer-documentation > 04-vllm-paged-attention > vLLM  分页注意力 > 输入

- 目前，vLLM 使用自己的多头查询注意力内核 (multi-head query attention kernel) 实现（位于`csrc/attention/attention_kernels.cu`）。该内核旨在兼容 vLLM 的分页键值缓存，其中键和值缓存存储在不同的块中（注意，这里的块概念与 GPU 线程块不同。因此，在后续文档中，我们将把 vLLM 分页注意力块称为「块 (block)」，而把 GPU 线程块称为「线程块(thread block)」）。
- 为了实现高性能，该内核依赖于专门设计的内存布局和访问方法，特别是在线程从全局内存读取数据到共享内存时。本文档的目的是逐步提供内核实现的高层次解释，以帮助那些希望了解 vLLM 多头查询注意力内核的人。在阅读完本文档后，用户能够更好地理解，并更容易跟进实际的实现。
- 请注意，本文件可能不会涵盖所有细节，例如如何计算相应数据的正确索引或点乘实现。不过，在阅读完本文档并熟悉高层次的逻辑流程后，您应该能够更容易阅读实际代码并理解细节。

## 输入


- 内核函数接收当前线程的参数列表来执行其分配的工作。其中最重要的 3 个参数是输入指针 `q`、 `k_cache` 和 `v_cache` ，它们分别指向全局内存上需要读取和处理的查询、键和值数据。输出指针 `out` 指向应将结果写入的全局内存。这 4 个指针实际上指向的是多维数组，但每个线程只访问分配给它的那部分数据。为了简化说明，我们在这里省略了所有其他运行时参数。

```plain
    template<
    typename scalar_t,
    int HEAD_SIZE,
    int BLOCK_SIZE,
    int NUM_THREADS,
    int PARTITION_SIZE = 0>
    __device__ void paged_attention_kernel(
    ... // Other side args. // 其他副参数
    const scalar_t* __restrict__ out,       // [num_seqs, num_heads, max_num_partitions, head_size]
    const scalar_t* __restrict__ q,         // [num_seqs, num_heads, head_size]
    const scalar_t* __restrict__ k_cache,   // [num_blocks, num_kv_heads, head_size/x, block_size, x]
    const scalar_t* __restrict__ v_cache,   // [num_blocks, num_kv_heads, head_size, block_size]
    ... // Other side args.
    )
```

- 在编译时确定的函数签名上方还有一个模板参数列表。 `scalar_t` 表示查询、键和值数据元素的数据类型，例如 FP16。 `HEAD_SIZE` 表示每个头中的元素数量。 `BLOCK_SIZE` 是指每个块中的 token 数量。 `NUM_THREADS` 表示每个线程块中的线程数。 `PARTITION_SIZE` 表示张量并行 GPU 的数量（为简单起见，我们假设这个值为 0 且禁用张量并行）。
- 我们需要借助这些参数进行一系列的准备工作。包括计算当前的头索引、块索引和其他必要的变量。不过，目前我们可以跳过这些准备工作，直接进行实际的计算。一旦我们掌握了整个流程，就会更容易理解这些准备工作。
