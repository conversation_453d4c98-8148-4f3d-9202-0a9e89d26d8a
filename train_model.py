"""
应用服务报错根因分析模型 - 训练脚本

整合数据预处理、模型训练和评估的完整流程
"""

import pandas as pd
import numpy as np
import os
import json
import argparse
import logging
from typing import Dict, Any
import warnings
warnings.filterwarnings('ignore')

from core.data_processor import DataProcessor
from core.model_trainer import ModelTrainer
from core.predictor import Predictor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ErrorAnalysisTrainer:
    """
    错误分析训练器
    
    整合完整的训练流程
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化训练器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.data_processor = DataProcessor(
            tokenizer_name=config.get('model_name', 'bert-base-chinese'),
            max_length=config.get('max_length', 512)
        )
        self.model_trainer = ModelTrainer(
            model_name=config.get('model_name', 'bert-base-chinese'),
            max_length=config.get('max_length', 512),
            batch_size=config.get('batch_size', 8),
            learning_rate=config.get('learning_rate', 2e-5),
            num_epochs=config.get('num_epochs', 10),
            device=config.get('device', None)
        )
        
        logger.info("错误分析训练器初始化完成")
    
    def load_data(self, data_path: str) -> tuple:
        """
        加载训练数据
        
        Args:
            data_path: 数据文件路径
            
        Returns:
            (文本列表, 标签列表)
        """
        logger.info(f"加载数据: {data_path}")
        
        try:
            if data_path.endswith('.xlsx'):
                df = pd.read_excel(data_path)
            elif data_path.endswith('.csv'):
                df = pd.read_csv(data_path)
            else:
                raise ValueError("不支持的文件格式，请使用 .xlsx 或 .csv 文件")
            
            # 获取文本和标签列
            text_column = self.config.get('text_column', '响应内容')
            label_column = self.config.get('label_column', '报错原因')
            
            if text_column not in df.columns:
                raise ValueError(f"数据中不存在列: {text_column}")
            if label_column not in df.columns:
                raise ValueError(f"数据中不存在列: {label_column}")
            
            texts = df[text_column].astype(str).tolist()
            labels = df[label_column].astype(str).tolist()
            
            logger.info(f"成功加载 {len(texts)} 条数据")
            logger.info(f"唯一标签数量: {len(set(labels))}")
            
            return texts, labels
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise
    
    def train(self, data_path: str, output_dir: str = "models/error_classifier") -> Dict[str, Any]:
        """
        执行完整的训练流程
        
        Args:
            data_path: 训练数据路径
            output_dir: 模型输出目录
            
        Returns:
            训练结果
        """
        logger.info("=" * 60)
        logger.info("开始训练应用服务报错根因分析模型")
        logger.info("=" * 60)
        
        # 1. 加载数据
        texts, labels = self.load_data(data_path)
        
        # 2. 数据预处理
        logger.info("开始数据预处理...")
        processed_texts, feature_matrix, encoded_labels, valid_indices = self.data_processor.process_dataset(
            texts, labels, fit_encoders=True
        )
        
        if len(processed_texts) == 0:
            raise ValueError("预处理后没有有效数据，请检查数据质量")
        
        # 3. 数据增强（针对小样本场景）
        if len(processed_texts) < 100:
            logger.warning(f"数据量较少 ({len(processed_texts)} 条)，建议收集更多数据以提高模型性能")
        
        # 4. 模型训练
        logger.info("开始模型训练...")
        training_history = self.model_trainer.train(
            processed_texts, encoded_labels, feature_matrix, output_dir
        )
        
        # 5. 保存数据处理器配置
        processor_config = {
            'label_mapping': self.data_processor.get_label_mapping(),
            'error_keywords': self.data_processor.error_keywords,
            'text_column': self.config.get('text_column', '响应内容'),
            'label_column': self.config.get('label_column', '报错原因')
        }
        
        os.makedirs(output_dir, exist_ok=True)
        with open(os.path.join(output_dir, 'processor_config.json'), 'w', encoding='utf-8') as f:
            json.dump(processor_config, f, ensure_ascii=False, indent=2)
        
        # 6. 训练结果总结
        result = {
            'total_samples': len(texts),
            'valid_samples': len(processed_texts),
            'num_classes': len(self.data_processor.label_encoder.classes_),
            'feature_dim': feature_matrix.shape[1] if len(feature_matrix) > 0 else 0,
            'training_history': training_history,
            'model_path': output_dir,
            'label_mapping': processor_config['label_mapping']
        }
        
        logger.info("=" * 60)
        logger.info("训练完成！")
        logger.info(f"模型保存路径: {output_dir}")
        logger.info(f"有效样本数: {result['valid_samples']}")
        logger.info(f"分类类别数: {result['num_classes']}")
        logger.info(f"最终验证准确率: {training_history['val_accuracy'][-1]:.4f}")
        logger.info("=" * 60)
        
        return result
    
    def evaluate_model(self, model_path: str, test_data_path: str = None) -> Dict[str, Any]:
        """
        评估训练好的模型
        
        Args:
            model_path: 模型路径
            test_data_path: 测试数据路径（可选）
            
        Returns:
            评估结果
        """
        logger.info("开始模型评估...")
        
        # 加载预测器
        predictor = Predictor(model_path)
        
        # 加载标签映射
        processor_config_path = os.path.join(model_path, 'processor_config.json')
        if os.path.exists(processor_config_path):
            with open(processor_config_path, 'r', encoding='utf-8') as f:
                processor_config = json.load(f)
            predictor.set_label_mapping(processor_config['label_mapping'])
        
        evaluation_result = {
            'model_path': model_path,
            'evaluation_time': pd.Timestamp.now().isoformat()
        }
        
        # 如果提供了测试数据，进行详细评估
        if test_data_path and os.path.exists(test_data_path):
            test_texts, test_labels = self.load_data(test_data_path)
            
            # 批量预测
            predictions = predictor.predict_batch(test_texts, return_probabilities=True)
            
            # 计算准确率
            predicted_labels = [p['predicted_label'] for p in predictions]
            accuracy = sum(1 for true, pred in zip(test_labels, predicted_labels) if true == pred) / len(test_labels)
            
            # 置信度分析
            confidence_analysis = predictor.analyze_prediction_confidence(predictions)
            
            evaluation_result.update({
                'test_samples': len(test_texts),
                'accuracy': accuracy,
                'confidence_analysis': confidence_analysis
            })
            
            logger.info(f"测试准确率: {accuracy:.4f}")
            logger.info(f"平均置信度: {confidence_analysis['avg_confidence']:.4f}")
        
        # 示例预测
        sample_texts = [
            "HTTP 500 Internal Server Error",
            "Connection timeout after 30 seconds",
            "Authentication failed: invalid token",
            "JSON parse error: unexpected character",
            "Database connection refused"
        ]
        
        logger.info("示例预测:")
        sample_predictions = []
        for text in sample_texts:
            pred = predictor.predict_single(text, return_probabilities=True)
            sample_predictions.append({
                'text': text,
                'prediction': pred['predicted_label'],
                'confidence': pred['confidence']
            })
            logger.info(f"  文本: {text}")
            logger.info(f"  预测: {pred['predicted_label']} (置信度: {pred['confidence']:.3f})")
        
        evaluation_result['sample_predictions'] = sample_predictions
        
        return evaluation_result

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='应用服务报错根因分析模型训练')
    parser.add_argument('--data', type=str, default='processed_data.xlsx', 
                       help='训练数据文件路径')
    parser.add_argument('--output', type=str, default='models/error_classifier',
                       help='模型输出目录')
    parser.add_argument('--config', type=str, default=None,
                       help='配置文件路径')
    parser.add_argument('--evaluate', action='store_true',
                       help='是否在训练后进行评估')
    parser.add_argument('--test-data', type=str, default=None,
                       help='测试数据文件路径')
    
    args = parser.parse_args()
    
    # 加载配置
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
    else:
        # 默认配置
        config = {
            'model_name': 'bert-base-chinese',
            'max_length': 512,
            'batch_size': 8,
            'learning_rate': 2e-5,
            'num_epochs': 10,
            'text_column': '响应内容',
            'label_column': '报错原因'
        }
    
    # 创建训练器
    trainer = ErrorAnalysisTrainer(config)
    
    try:
        # 训练模型
        result = trainer.train(args.data, args.output)
        
        # 保存训练结果
        result_path = os.path.join(args.output, 'training_result.json')
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        # 评估模型
        if args.evaluate:
            eval_result = trainer.evaluate_model(args.output, args.test_data)
            
            eval_path = os.path.join(args.output, 'evaluation_result.json')
            with open(eval_path, 'w', encoding='utf-8') as f:
                json.dump(eval_result, f, ensure_ascii=False, indent=2)
        
        logger.info("训练流程完成！")
        
    except Exception as e:
        logger.error(f"训练失败: {e}")
        raise

if __name__ == "__main__":
    main()
