# 文档路径: 07-developer-documentation > 04-vllm-paged-attention > vLLM  分页注意力 > 输出

## 输出


- 我们可以将从局部寄存器内存中计算出的所有结果写入最终的输出全局内存。

```plain
    scalar_t* out_ptr = out + seq_idx * num_heads * max_num_partitions * HEAD_SIZE
                    + head_idx * max_num_partitions * HEAD_SIZE
                    + partition_idx * HEAD_SIZE;
```

- 首先，我们需要定义 `out_ptr` 变量，它指向分配序列和分配头的起始地址。

```plain
    for (int i = 0; i < NUM_ROWS_PER_THREAD; i++) {
    const int row_idx = lane / NUM_V_VECS_PER_ROW + i * NUM_ROWS_PER_ITER;
    if (row_idx < HEAD_SIZE && lane % NUM_V_VECS_PER_ROW == 0) {
        from_float(*(out_ptr + row_idx), accs[i]);
    }
    }
```

- 最后，我们需要遍历不同分配的头位置，并根据 `out_ptr` 写出相应的累积结果。
