# 文档路径: 02-serving > 07-integrations > 01-deploying&scaling-up-with-skypilot > 使用 SkyPilot 进行部署和扩充 > 在单个实例上运行

## 在单个实例上运行


请参阅用于服务的 vLLM SkyPilot YAML 文件，即 [serving.yaml](https://github.com/skypilot-org/skypilot/blob/master/llm/vllm/serve.yaml)。

```yaml
resources:
  accelerators: {L4, A10g, A10, L40, A40, A100, A100-80GB} # We can use cheaper accelerators for 8B model.


  accelerators: {L4, A10g, A10, L40, A40, A100, A100-80GB} # 我们可以为 8B 型号使用更便宜的加速器。


  use_spot: True
  disk_size: 512  # Ensure model checkpoints can fit.


  disk_size: 512 # 确保模型检查点足够容纳。


  disk_tier: best
  ports: 8081  # Expose to internet traffic.


  ports: 8081 # 公开于互联网的端口。




envs:
  MODEL_NAME: meta-llama/Meta-Llama-3-8B-Instruct
  HF_TOKEN: <your-huggingface-token>  # Change to your own huggingface token, or use --env to pass.


  HF_TOKEN: <your-huggingface-token> # 更改为你自己的 huggingface token，或者使用 --env 来传递。




setup: |
  conda create -n vllm python=3.10 -y
  conda activate vllm


  pip install vllm==0.4.0.post1
  # Install Gradio for web UI.


  # 安装 Gradio for web UI。


  pip install gradio openai
  pip install flash-attn==2.5.7


run: |
  conda activate vllm
  echo 'Starting vllm api server...'
  python -u -m vllm.entrypoints.openai.api_server \
    --port 8081 \
    --model $MODEL_NAME \
    --trust-remote-code \
    --tensor-parallel-size $SKYPILOT_NUM_GPUS_PER_NODE \
    2>&1 | tee api_server.log &


  echo 'Waiting for vllm api server to start...'
  while ! `cat api_server.log | grep -q 'Uvicorn running on'`; do sleep 1; done


  echo 'Starting gradio server...'
  git clone https://github.com/vllm-project/vllm.git || true
  python vllm/examples/gradio_openai_chatbot_webserver.py \
    -m $MODEL_NAME \
    --port 8811 \
    --model-url http://localhost:8081/v1 \
    --stop-token-ids 128009,128001
```

在列表中的任何一个候选 GPU (L4、A10g 等) 上启动服务以运行 Llama-3 8B 模型：

```plain
HF_TOKEN="your-huggingface-token" sky launch serving.yaml --env HF_TOKEN
```

检查命令的输出结果。将会有一个可分享的 gradio 链接（如下面的最后一行），在浏览器中打开此链接，便可使用 LLaMA 模型进行文本补全任务。

```plain
(task, pid=7431) Running on public URL: https://<gradio-hash>.gradio.live
```

**可选\*\***操作\***\*：**使用 70B 模型代替默认的 8B 模型，并使用更多的 GPU：

```plain
HF_TOKEN="your-huggingface-token" sky launch serving.yaml --gpus A100:8 --env HF_TOKEN --env MODEL_NAME=meta-llama/Meta-Llama-3-70B-Instruct
```
