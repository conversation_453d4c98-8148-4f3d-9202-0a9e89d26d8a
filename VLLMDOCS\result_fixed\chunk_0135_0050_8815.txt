# 文档路径: 10-tutorials > 01-vLLM-stepbysteb > vLLM 入门教程：零基础分步指南 > 三、启动 vLLM 服务器 > 3.1 主要参数设置

## 三、启动 vLLM 服务器

### 3.1 主要参数设置


以下是启动 vLLM 服务器时常用的一些参数：

- `--model`：要使用的 HuggingFace 模型名称或路径（默认值：`facebook/opt-125m`）。
- `--host` 和 `--port`：指定服务器地址和端口。
- `--dtype`：模型权重和激活的精度类型。可能的值：`auto`、`half`、`float16`、`bfloat16`、`float`、`float32`。默认值：`auto`。
- `--tokenizer`：要使用的 HuggingFace 标记器名称或路径。如果未指定，默认使用模型名称或路径。
- `--max-num-seqs`：每次迭代的最大序列数。
- `--max-model-len`：模型的上下文长度，默认值自动从模型配置中获取。
- `--tensor-parallel-size`、`-tp`：张量并行副本数量（对于 GPU）。默认值：`1`。
- `--distributed-executor-backend=ray`：指定分布式服务的后端，可能的值：`ray`、`mp`。默认值：`ray`（当使用超过一个 GPU 时，自动设置为 `ray`）。
