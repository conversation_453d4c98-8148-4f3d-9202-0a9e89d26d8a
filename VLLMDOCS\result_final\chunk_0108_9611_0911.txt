# 文档路径: 07-developer-documentation > 04-vllm-paged-attention > vLLM  分页注意力 > LV

## LV


- 现在，我们需要在每个 warp 内对 `accs` 进行归约。这一过程允许每个线程为一个块中所有 token 的指定头位置累积 `accs` 。

```plain
    for (int i = 0; i < NUM_ROWS_PER_THREAD; i++) {
       float acc = accs[i];
       for (int mask = NUM_V_VECS_PER_ROW / 2; mask >= 1; mask /= 2) {
          acc += VLLM_SHFL_XOR_SYNC(acc, mask);
       }
       accs[i] = acc;
    }
```

- 接下来，我们对所有 warp 执行 `accs` 归约，允许每个线程为所有上下文 token 的指定头位置积累 `accs` 。请注意，每个线程中的每个 `accs` 仅存储所有上下文 token 的整个头的部分元素的累加。然而，总体而言，所有输出结果都已经计算完毕，只是存储在不同的线程寄存器内存中。

```plain
    float* out_smem = reinterpret_cast<float*>(shared_mem);
    for (int i = NUM_WARPS; i > 1; i /= 2) {
        // Upper warps write to shared memory.
        // 上面的 warp 写入共享内存
        ...
            float* dst = &out_smem[(warp_idx - mid) * HEAD_SIZE];
            for (int i = 0; i < NUM_ROWS_PER_THREAD; i++) {
                    ...
            dst[row_idx] = accs[i];
        }


        // Lower warps update the output.
        // 底下的 warp 更新输出
            const float* src = &out_smem[warp_idx * HEAD_SIZE];
        for (int i = 0; i < NUM_ROWS_PER_THREAD; i++) {
                    ...
            accs[i] += src[row_idx];
        }


            // Write out the accs.
            // 写出 accs
    }
```
