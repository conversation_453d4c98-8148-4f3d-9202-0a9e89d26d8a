# 文档路径: 01-getting-started > 01-installation > 安装 > 使用 pip 安装

## 使用 pip 安装

您可以使用 pip 安装 vLLM:

```plain
# (Recommended) Create a new conda environment.
#（推荐）创建一个新的 conda 环境。

conda create -n myenv python=3.10 -y
conda activate myenv

# Install vLLM with CUDA 12.1.
# 安装带有 CUDA 12.1 的 vLLM。

pip install vllm
```

**注意**

截至目前，vLLM 的二进制文件默认使用 CUDA 12.1 和公共 PyTorch 发行版本进行编译。我们还提供使用 CUDA 11.8 和公共 PyTorch 发行版本编译的 vLLM 二进制文件：

```plain
# Install vLLM with CUDA 11.8.
# 安装带有 CUDA 11.8 的 vLLM。

export VLLM_VERSION=0.4.0
export PYTHON_VERSION=310
pip install https://github.com/vllm-project/vllm/releases/download/v${VLLM_VERSION}/vllm-${VLLM_VERSION}+cu118-cp${PYTHON_VERSION}-cp${PYTHON_VERSION}-manylinux1_x86_64.whl --extra-index-url https://download.pytorch.org/whl/cu118
```

为了提高性能，vLLM 必须编译多个 cuda 内核。遗憾的是，这种编译会引入其他 CUDA 版本和 PyTorch 版本的二进制不兼容性，即使对于具有不同构建配置的相同 PyTorch 版本也是如此。

因此，建议使用**全新的** conda 环境安装 vLLM。如果您有不同的 CUDA 版本或者想要使用现有的 PyTorch 安装，则需要从源代码构建 vLLM。请参阅以下的说明。

**注意**

自 v0.5.3 版本以来，vLLM 还为每次提交发布一个 wheel 子集（Python 3.10、3.11 和 CUDA 12）。您可以使用以下命令下载它们：

```plain
export VLLM_VERSION=0.5.4 # vLLM's main branch version is currently set to latest released tag

export VLLM_VERSION=0.5.4 # vLLM 的主分支版本当前设置为最新发布的标签

pip install https://vllm-wheels.s3.us-west-2.amazonaws.com/nightly/vllm-${VLLM_VERSION}-cp38-abi3-manylinux1_x86_64.whl
# You can also access a specific commit

# 你还可以访问特定的提交

# export VLLM_COMMIT=...

# 导出 VLLM_COMMIT=...

# pip install https://vllm-wheels.s3.us-west-2.amazonaws.com/${VLLM_COMMIT}/vllm-${VLLM_VERSION}-cp38-abi3-manylinux1_x86_64.whl
```
