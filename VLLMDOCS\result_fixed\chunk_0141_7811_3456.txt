# 文档路径: 10-tutorials > 02-infer-34b-with-vllm > 使用 vLLM 对 Qwen2.5 推理 > 2. 使用 vLLM 加载 Qwen 量化模型

## 2. 使用 vLLM 加载 Qwen 量化模型


```
import os, math, numpy as np
os.environ["CUDA_VISIBLE_DEVICES"]="0"
```

```
# 我们将在此处加载并使用 Qwen2.5-3B-Instruct-AWQ

import vllm

llm = vllm.LLM(
    "/input0/Qwen2.5-3B-Instruct-AWQ",
    quantization="awq",
    tensor_parallel_size=1,
    gpu_memory_utilization=0.95,
    trust_remote_code=True,
    dtype="half",
    enforce_eager=True,
    max_model_len=512,
    #distributed_executor_backend="ray",
)
tokenizer = llm.get_tokenizer()
```
