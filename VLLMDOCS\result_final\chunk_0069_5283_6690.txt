# 文档路径: 03-models > 06-using-vlms > 使用 VLM > 离线推理 > 单图像输入 > 多图像输入

vLLM 为视觉语言模型 (VLM) 提供实验性支持，可以参阅「支持的 VLM 列表」。本文档将向您展示如何使用 vLLM 运行并提供这些模型的服务。

**注意：**

我们正在积极改进对 VLM 的支持。预计在即将发布的版本中，VLM 的使用和开发会发生重大变化，但无需事先弃用。

We are continuously improving user & developer experience for VLMs. Please [open an issue on GitHub](https://github.com/vllm-project/vllm/issues/new/choose) if you have any feedback or feature requests.

我们不断改善 VLMs 的用户和开发人员体验。如果您有任何反馈或功能请求，请[访问 GitHub 并提出 issue](https://github.com/vllm-project/vllm/issues/new/choose)。

## 离线推理



### 单图像输入


`LLM` 类的实例化过程与语言模型的实例化方式大致相同。

```python
llm = LLM(model="llava-hf/llava-1.5-7b-hf")
```

要将图像传递给模型，请注意 `vllm.inputs.PromptInputs` 中的以下内容:

- `prompt`: 提示应遵循 HuggingFace 中记录的格式。
- `multi_modal_data`: 这是一个字典，它遵循 `vllm.multimodal.MultiModalDataDict` 中定义的模式。

```python
# Refer to the HuggingFace repo for the correct format to use
# 请参阅 HuggingFace 存储库以了解要使用的正确格式


prompt = "USER: <image>\nWhat is the content of this image?\nASSISTANT:"


# Load the image using PIL.Image
# 使用 PIL.Image 加载图像


image = PIL.Image.open(...)


# Single prompt inference
# 单提示推理


outputs = llm.generate({
    "prompt": prompt,
    "multi_modal_data": {"image": image},
})


for o in outputs:
    generated_text = o.outputs[0].text
    print(generated_text)


# Inference with image embeddings as input
# 以图像嵌入作为输入进行推理


image_embeds = torch.load(...) # torch.Tensor of shape (1, image_feature_size, hidden_size of LM)


image_embeds = torch.load(...) # torch.Tensor 形状为 (1, image_feature_size, hide_size of LM)


outputs = llm.generate({
    "prompt": prompt,
    "multi_modal_data": {"image": image_embeds},
})


for o in outputs:
    generated_text = o.outputs[0].text
    print(generated_text)


# Batch inference
# 批量推理


image_1 = PIL.Image.open(...)
image_2 = PIL.Image.open(...)
outputs = llm.generate(
    [
        {
            "prompt": "USER: <image>\nWhat is the content of this image?\nASSISTANT:",
            "multi_modal_data": {"image": image_1},
        },
        {
            "prompt": "USER: <image>\nWhat's the color of this image?\nASSISTANT:",
            "multi_modal_data": {"image": image_2},
        }
    ]
)


for o in outputs:
    generated_text = o.outputs[0].text
    print(generated_text)
```

代码示例可以在 [examples/offline_inference_vision_language.py](https://github.com/vllm-project/vllm/blob/main/examples/offline_inference_vision_language.py) 中找到。


### 多图像输入


多图像输入仅被一部分视觉语言模型 (VLMs) 支持，如[此处](https://docs.vllm.ai/en/latest/models/supported_models.html#supported-vlms)所示。

若要在单个文本提示中启用多个多模态项目，您需要为 `LLM`类设置 `limit_mm_per_prompt` 参数。

```python
llm = LLM(
    model="microsoft/Phi-3.5-vision-instruct",
    trust_remote_code=True,  # Required to load Phi-3.5-vision 需要加载 Phi-3.5-vision 模型


    max_model_len=4096,  # Otherwise, it may not fit in smaller GPUs 否则，可能无法适配较小的 GPU


    limit_mm_per_prompt={"image": 2},  # The maximum number to accept 每个文本提示允许的最大多模态项数量
)
```

您可以传入一个图像列表，而不是传入一张单独的图像。

```python
# Refer to the HuggingFace repo for the correct format to use
# 参考 HuggingFace 仓库中的正确格式来使用


prompt = "<|user|>\n<|image_1|>\n<|image_2|>\nWhat is the content of each image?<|end|>\n<|assistant|>\n"


# Load the images using PIL.Image
# 使用 PIL.Image 加载图片
image1 = PIL.Image.open(...)
image2 = PIL.Image.open(...)


outputs = llm.generate({
    "prompt": prompt,
    "multi_modal_data": {
        "image": [image1, image2]
    },
})


for o in outputs:
    generated_text = o.outputs[0].text
    print(generated_text)
```

代码示例可以在 [examples/offline_inference_vision_language_multi_image.py](https://github.com/vllm-project/vllm/blob/main/examples/offline_inference_vision_language_multi_image.py) 中找到。

多图像输入功能可以扩展应用于视频描述任务。以下展示了如何使用 Qwen2-VL 模型来实现这一点，因为该模型支持视频处理：

```python
# Specify the maximum number of frames per video to be 4. This can be changed.
# 指定每个视频的最大帧数为 4，这个数值可以根据需要调整。
llm = LLM("Qwen/Qwen2-VL-2B-Instruct", limit_mm_per_prompt={"image": 4})


# Create the request payload.
# 创建请求数据载荷。
video_frames = ... # load your video making sure it only has the number of frames specified earlier.
message = {
    "role": "user",
    "content": [
        {"type": "text", "text": "Describe this set of frames. Consider the frames to be a part of the same video."},
    ],
}
for i in range(len(video_frames)):
    base64_image = encode_image(video_frames[i]) # base64 encoding.
    new_image = {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
    message["content"].append(new_image)


# Perform inference and log output.
# 执行推理并记录输出。
outputs = llm.chat([message])


for o in outputs:
    generated_text = o.outputs[0].text
    print(generated_text)
```
