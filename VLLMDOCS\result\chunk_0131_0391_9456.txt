# 文档路径: 07-developer-documentation > 01-sampling-parameters > 采样参数

*class vllm.SamplingParams(n: int = 1, *best_of*: int | None = None, presence_penalty: float = 0.0, frequency_penalty: float = 0.0, repetition_penalty: float = 1.0, temperature: float = 1.0, top_p: float = 1.0, top_k: int = -1, min_p: float = 0.0, seed: int | None = None, *use_beam_search*: bool = False, length_penalty: float = 1.0, early_stopping: bool | str = False, stop: str | ~typing.List[str] | None = None, stop_token_ids: ~typing.List[int] | None = None, ignore_eos: bool = False, max_tokens: int | None = 16, min_tokens: int = 0, logprobs: int | None = None, prompt_logprobs: int | None = None, detokenize: bool = True, skip_special_tokens: bool = True, spaces_between_special_tokens: bool = True, logits_processors: ~typing.Any | None = None, include_stop_str_in_output: bool = False, truncate_prompt_tokens: int[int] | None = None, output_kind: ~vllm.sampling_params.RequestOutputKind = RequestOutputKind.CUMULATIVE, output_text_buffer_length: int = 0, _all_stop_token_ids: ~typing.Set[int] = <factory>)*

[[source]](https://docs.vllm.ai/en/latest/_modules/vllm/sampling_params.html#SamplingParams)

用于文本生成的采样参数。

总体而言，我们遵循 OpenAI 文本 completion API ([https://platform.openai.com/docs/api-reference/completions/create)](https://platform.openai.com/docs/api-reference/completions/create)) 中的采样参数。另外，我们还支持 OpenAI 不支持的束搜索。