# 文档路径: 01-getting-started > 08-quickstart > 快速入门 > 兼容 OpenAI 服务器 > 在 vLLM 中使用 OpenAI Chat API

## 兼容 OpenAI 服务器

vLLM 可以作为一个实现了 OpenAI API 协议的服务器进行部署。这使得 vLLM 可以直接替代使用 OpenAI API 的应用程序。默认情况下，它在 [http://localhost:8000](http://localhost:8000) 启动服务器。您可以使用 `--host` 和`--port` 参数指定地址。当前，该服务器一次仅托管一个模型（在下面的命令中为 OPT-125M），并实现了[模型列表 (list models)](https://platform.openai.com/docs/api-reference/models/list)、[创建聊天补全 (create chat completion)](https://platform.openai.com/docs/api-reference/chat/completions/create) 和[创建完成 (create completion)](https://platform.openai.com/docs/api-reference/completions/create) 端点。我们正在积极添加对更多端点的支持。

启动服务器：

```plain
vllm serve facebook/opt-125m
```

默认情况下，服务器使用存储在 tokenizer 中的预定义聊天模板。您可以使用 `--chat-template` 参数覆盖此模板：

```plain
vllm serve facebook/opt-125m --chat-template ./examples/template_chatml.jinja
```

该服务器可以按照与 OpenAI API 相同的格式进行查询。例如，列出模型：

```plain
curl http://localhost:8000/v1/models
```

您可以传入参数`--api-key`或设置环境变量`VLLM_API_KEY`，以使服务器能够检查标头中的 API 密钥。


### 在 vLLM 中使用 OpenAI Chat API

vLLM 服务器在设计上支持 OpenAI Chat API，允许您与模型进行动态对话。聊天界面是一种与模型交流更具交互性的方式，可以进行来回交流，并将对话历史存储下来。这对于需要上下文或更详细解释的任务非常有用。

使用 OpenAI Chat API 查询模型：

您可以使用[创建聊天补全 (create chat completion)](https://platform.openai.com/docs/api-reference/chat/completions/create) 端点在类似聊天的界面中与模型进行交流：

```plain
curl http://localhost:8000/v1/chat/completions \
    -H "Content-Type: application/json" \
    -d '{
        "model": "facebook/opt-125m",
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Who won the world series in 2020?"}
        ]
    }'
```

Python 客户端示例：

使用 _openai_ 的 python 包，您还可以以类似聊天的方式与模型进行交流:

```python
from openai import OpenAI
# Set OpenAI's API key and API base to use vLLM's API server.

# 使用 vLLM 的 API 服务器需要设置 OpenAI 的 API 密钥和 API 库。

openai_api_key = "EMPTY"
openai_api_base = "http://localhost:8000/v1"

client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)

chat_response = client.chat.completions.create(
    model="facebook/opt-125m",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Tell me a joke."},
    ]
)
print("Chat response:", chat_response)
```

有关 chat API 的更深入示例和高级功能，您可以参考 OpenAI 官方文档。
