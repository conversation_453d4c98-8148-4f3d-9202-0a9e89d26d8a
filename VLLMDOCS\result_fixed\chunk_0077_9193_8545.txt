# 文档路径: 03-models > 05-using-lora-adapters > 使用 LoRA 适配器 > 模型卡中的 LoRA 模型谱系

## 模型卡中的 LoRA 模型谱系


新的 –lora-modules 格式主要用于支持在模型卡中显示父模型信息。以下是你当前响应如何支持此功能的说明：

- LoRA 模型 sql-lora 的父字段现在链接到其基础模型 _meta-llama/Llama-2-7b-hf_。这正确地反映了基础模型与 LoRA 适配器之间的层次关系。
- _root_ 字段指向 lora 适配器的工件位置。

```plain
curl http://localhost:8000/v1/models


{
    "object": "list",
    "data": [
        {
        "id": "meta-llama/Llama-2-7b-hf",
        "object": "model",
        "created": 1715644056,
        "owned_by": "vllm",
        "root": "~/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/",
        "parent": null,
        "permission": [
            {
            .....
            }
        ]
        },
        {
        "id": "sql-lora",
        "object": "model",
        "created": 1715644056,
        "owned_by": "vllm",
        "root": "~/.cache/huggingface/hub/models--yard1--llama-2-7b-sql-lora-test/snapshots/0dfa347e8877a4d4ed19ee56c140fa518470028c/",
        "parent": meta-llama/Llama-2-7b-hf,
        "permission": [
            {
            ....
            }
        ]
        }
    ]
}
```
