# 文档路径: 01-getting-started > 08-quickstart > 快速入门 > 离线批量推理

本指南将说明如何使用 vLLM 进行以下操作：

- 对数据集进行离线批量推理；
- 为大语言模型构建 API 服务器；
- 启动与 OpenAI 兼容的 API 服务器。

在继续进行本指南之前，请务必完成[安装说明](https://docs.vllm.ai/en/latest/getting_started/installation.html#installation)。

**注意**

默认情况下，vLLM 从 [HuggingFace](https://huggingface.co/) 下载模型。如果您想在以下示例中使用 [ModelScope](https://www.modelscope.cn) 中的模型，请设置环境变量：

```shell
export VLLM_USE_MODELSCOPE=True
```

## 离线批量推理


我们首先演示一个使用 vLLM 对数据集进行离线批处理推理的案例。也就是说，我们使用 vLLM 生成输入提示列表的文本。

从 vLLM 导入 `LLM` 和 `SamplingParams`。`LLM`类是使用 vLLM 引擎运行离线推理的主要类。`SamplingParams`类指定了采样过程的参数。

```python
from vllm import LLM, SamplingParams
```

定义输入提示列表和生成的采样参数。采样温度设置为 0.8，核采样概率 (nucleus sampling probability) 设置为 0.95。有关采样参数的更多信息，请参阅[类定义](https://github.com/vllm-project/vllm/blob/main/vllm/sampling_params.py)。

```python
prompts = [
    "Hello, my name is",
    "The president of the United States is",
    "The capital of France is",
    "The future of AI is",
]
sampling_params = SamplingParams(temperature=0.8, top_p=0.95)
```

使用`LLM`类和 [OPT-125M 模型](https://arxiv.org/abs/2205.01068)初始化 vLLM 引擎以进行离线推理。支持的模型列表可以在[支持的模型](https://docs.vllm.ai/en/latest/models/supported_models.html#supported-models)中找到。

```python
llm = LLM(model="facebook/opt-125m")
```

调用`llm.generate`生成输出。它将输入提示添加到 vLLM 引擎的等待队列中，并执行 vLLM 引擎来生成高吞吐量的输出。输出作为`RequestOutput`对象列表返回，其中包括所有输出的 tokens。

```python
outputs = llm.generate(prompts, sampling_params)

# Print the outputs.
# 打印输出

for output in outputs:
    prompt = output.prompt
    generated_text = output.outputs[0].text
    print(f"Prompt: {prompt!r}, Generated text: {generated_text!r}")
```

这个代码示例也可以在 [examples/offline_inference.py](https://github.com/vllm-project/vllm/blob/main/examples/offline_inference.py) 中找到。
