# 文档路径: index > 欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 开发者文档

# 欢迎来到 vLLM！

## 文档

### 开发者文档


[采样参数](https://vllm.hyper.ai/docs/developer-documentation/sampling-parameters)

[离线推理](https://vllm.hyper.ai/docs/developer-documentation/offline-inference/)

- [LLM 类](https://vllm.hyper.ai/docs/developer-documentation/offline-inference/llm-class)

- [LLM 输入](https://vllm.hyper.ai/docs/developer-documentation/offline-inference/llm-inputs)

[vLLM 引擎](https://vllm.hyper.ai/docs/developer-documentation/vllm-engine/)

[LLM 引擎](https://vllm.hyper.ai/docs/developer-documentation/vllm-engine/)

- [LLMEngine](https://vllm.hyper.ai/docs/developer-documentation/vllm-engine/llmengine)

- [AsyncLLMEngine](https://vllm.hyper.ai/docs/developer-documentation/vllm-engine/asyncllmengine)

[vLLM 分页注意力](https://vllm.hyper.ai/docs/developer-documentation/vllm-paged-attention)

- [输入处理](https://vllm.hyper.ai/docs/developer-documentation/vllm-paged-attention#%E8%BE%93%E5%85%A5)

- [概念](https://vllm.hyper.ai/docs/developer-documentation/vllm-paged-attention#%E6%A6%82%E5%BF%B5)

- [查询](https://vllm.hyper.ai/docs/developer-documentation/vllm-paged-attention#%E8%AF%A2%E9%97%AE-query)

- [键](https://vllm.hyper.ai/docs/developer-documentation/vllm-paged-attention#%E9%94%AE-key)

- [QK](https://vllm.hyper.ai/docs/developer-documentation/vllm-paged-attention#qk)

- [Softmax](https://vllm.hyper.ai/docs/developer-documentation/vllm-paged-attention#softmax)

- [值](https://vllm.hyper.ai/docs/developer-documentation/vllm-paged-attention#%E5%80%BC)

- [LV](https://vllm.hyper.ai/docs/developer-documentation/vllm-paged-attention#lv)

- [输出](https://vllm.hyper.ai/docs/developer-documentation/vllm-paged-attention#%E8%BE%93%E5%87%BA)

[输入处理](https://vllm.hyper.ai/docs/developer-documentation/input-processing/model_inputs_index)

- [指南](https://vllm.hyper.ai/docs/developer-documentation/input-processing/model_inputs_index#%E6%8C%87%E5%8D%97)

- [模块内容](https://vllm.hyper.ai/docs/developer-documentation/input-processing/model_inputs_index#%E6%A8%A1%E5%9D%97%E5%86%85%E5%AE%B9)

[多模态](https://vllm.hyper.ai/docs/developer-documentation/multi-modality/)

- [指南](https://vllm.hyper.ai/docs/developer-documentation/multi-modality/#%E6%8C%87%E5%8D%97)

- [模块内容](https://vllm.hyper.ai/docs/developer-documentation/multi-modality/#%E6%A8%A1%E5%9D%97%E5%86%85%E5%AE%B9)

[Docker 文件](https://vllm.hyper.ai/docs/developer-documentation/dockerfile)

[vLLM 性能分析](https://vllm.hyper.ai/docs/developer-documentation/profiling-vllm)

- [示例命令和用法](https://vllm.hyper.ai/docs/developer-documentation/profiling-vllm#%E5%91%BD%E4%BB%A4%E5%92%8C%E4%BD%BF%E7%94%A8%E7%A4%BA%E4%BE%8B)

- [离线推理](https://vllm.hyper.ai/docs/developer-documentation/profiling-vllm#%E7%A6%BB%E7%BA%BF%E6%8E%A8%E7%90%86)

- [OpenAI 服务器](https://vllm.hyper.ai/docs/developer-documentation/profiling-vllm#openai-%E6%9C%8D%E5%8A%A1%E5%99%A8)
