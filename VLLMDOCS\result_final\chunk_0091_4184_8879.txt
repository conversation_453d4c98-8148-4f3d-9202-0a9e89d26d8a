# 文档路径: 04-quantization > 06-fp8-w8a8 > FP8 W8A8 > 使用静态激活缩放因子进行离线量化

## 使用静态激活缩放因子进行离线量化


您可以使用带有校准数据的 AutoFP8，通过启用 `activation_scheme="static"` 参数为权重和激活值生成每个张量的静态缩放。

```python
from datasets import load_dataset
from transformers import AutoTokenizer
from auto_fp8 import AutoFP8ForCausalLM, BaseQuantizeConfig


pretrained_model_dir = "meta-llama/Meta-Llama-3-8B-Instruct"
quantized_model_dir = "Meta-Llama-3-8B-Instruct-FP8"


tokenizer = AutoTokenizer.from_pretrained(pretrained_model_dir, use_fast=True)
tokenizer.pad_token = tokenizer.eos_token


# Load and tokenize 512 dataset samples for calibration of activation scales
# 加载并标记 512 个数据集样本以校准激活量表


ds = load_dataset("mgoin/ultrachat_2k", split="train_sft").select(range(512))
examples = [tokenizer.apply_chat_template(batch["messages"], tokenize=False) for batch in ds]
examples = tokenizer(examples, padding=True, truncation=True, return_tensors="pt").to("cuda")


# Define quantization config with static activation scales
# 使用静态激活尺度定义量化配置


quantize_config = BaseQuantizeConfig(quant_method="fp8", activation_scheme="static")


# Load the model, quantize, and save checkpoint
# 加载模型、量化并保存 checkpoint


model = AutoFP8ForCausalLM.from_pretrained(pretrained_model_dir, quantize_config)
model.quantize(examples)
model.save_quantized(quantized_model_dir)
```

具有量化权重和激活值的模型 checkpoint 应当可以在 `Meta-Llama-3-8B-Instruct-FP8/` 处获取。最后，您可以直接在 vLLM 中加载量化后的模型 checkpoint。

```python
from vllm import LLM
model = LLM(model="Meta-Llama-3-8B-Instruct-FP8/")
# INFO 06-10 21:15:41 model_runner.py:159] Loading model weights took 8.4596 GB
# INFO 06-10 21:15:41 model_runner.py:159] 加载模型权重占用了 8.4596 GB


result = model.generate("Hello, my name is")
```
