# 文档路径: 07-developer-documentation > 04-vllm-paged-attention > vLLM  分页注意力 > 键 (Key)

## 键 (Key)


- 与「查询 (Query)」部分类似，本部分将介绍内存布局和键的分配。虽然每个线程组在一个内核运行时仅处理一个查询 token，但它可以在多次迭代中处理多个键 token。同时，每个 warp 将在多次迭代中处理多个键 token 块，确保内核运行结束后所有上下文 token 都被整个线程组处理。在本文中，「处理 (handle)」是指执行查询数据和键数据之间的点乘运算。

```plain
    const scalar_t* k_ptr = k_cache + physical_block_number * kv_block_stride
                        + kv_head_idx * kv_head_stride
                        + physical_block_offset * x;
```

- 与 `q_ptr` 不同，每个线程中的 `k_ptr` 将在不同迭代中指向不同的键 token 。如上所示，`k_ptr` 基于 `k_cache` 的键 token 数据指向分配块、分配头和分配 token 处。

![图片](/img/docs/07-04/key.png)
1 个头中的所有上下文 token 的键数据

- 上图的图表展示了键数据的内存布局。假设 `BLOCK_SIZE` 为16，`HEAD_SIZE` 为128，`x` 为8，`THREAD_GROUP_SIZE` 为 2，总共有 4 个 warp。每个矩形代表一个头部的一个键 token 的所有元素，这些元素将由 1 个线程组处理。左半部分显示了 warp 0 的总共 16 个块的键 token 数据，而右半部分表示其他 warp 或迭代的剩余键 token 数据。每个矩形内部共有 32 个 vec（一个 token 的 128 个元素），将分别由 2 个线程（一个线程组） 处理。

![图片](/img/docs/07-04/k_vecs.png)
1 个线程组中的 `k_vecs`

```plain
    K_vec k_vecs[NUM_VECS_PER_THREAD]
```

- 接下来，我们需要从 `k_ptr` 读取键 token 数据并将它们存储在寄存器内存中作为 `k_vecs`。我们使用寄存器内存来存储 `k_vecs` ，因为它只能被 1 个线程访问 1 次，而 `q_vecs` 将被多个线程多次访问。每个 `k_vecs` 将包含多个向量以供后续计算。每个 vec 将在每次内部迭代时设置。 vec 的分配允许 warp 中的相邻线程一起读取相邻内存，这再次促进了内存合并。例如，线程 0 将读取 vec 0，而线程 1 将读取 vec 1。在下一个内部循环中，线程 0 将读取 vec 2，而线程 1 将读取 vec 3，依此类推。
- 您可能对整体流程仍然有点困惑。别担心，请继续阅读下一节「QK」部分。它将以更清晰、更高级的方式说明查询和键计算流程。
