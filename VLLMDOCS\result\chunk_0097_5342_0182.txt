# 文档路径: 03-models > 06-using-vlms > 使用 VLM > 离线推理 > 多图像输入

## 离线推理


### 多图像输入

多图像输入仅被一部分视觉语言模型 (VLMs) 支持，如[此处](https://docs.vllm.ai/en/latest/models/supported_models.html#supported-vlms)所示。

若要在单个文本提示中启用多个多模态项目，您需要为 `LLM`类设置 `limit_mm_per_prompt` 参数。

```python
llm = LLM(
    model="microsoft/Phi-3.5-vision-instruct",
    trust_remote_code=True,  # Required to load Phi-3.5-vision 需要加载 Phi-3.5-vision 模型


    max_model_len=4096,  # Otherwise, it may not fit in smaller GPUs 否则，可能无法适配较小的 GPU


    limit_mm_per_prompt={"image": 2},  # The maximum number to accept 每个文本提示允许的最大多模态项数量
)
```

您可以传入一个图像列表，而不是传入一张单独的图像。

```python
# Refer to the HuggingFace repo for the correct format to use
# 参考 HuggingFace 仓库中的正确格式来使用


prompt = "<|user|>\n<|image_1|>\n<|image_2|>\nWhat is the content of each image?<|end|>\n<|assistant|>\n"


# Load the images using PIL.Image
# 使用 PIL.Image 加载图片
image1 = PIL.Image.open(...)
image2 = PIL.Image.open(...)


outputs = llm.generate({
    "prompt": prompt,
    "multi_modal_data": {
        "image": [image1, image2]
    },
})


for o in outputs:
    generated_text = o.outputs[0].text
    print(generated_text)
```

代码示例可以在 [examples/offline_inference_vision_language_multi_image.py](https://github.com/vllm-project/vllm/blob/main/examples/offline_inference_vision_language_multi_image.py) 中找到。

多图像输入功能可以扩展应用于视频描述任务。以下展示了如何使用 Qwen2-VL 模型来实现这一点，因为该模型支持视频处理：

```python
# Specify the maximum number of frames per video to be 4. This can be changed.
# 指定每个视频的最大帧数为 4，这个数值可以根据需要调整。
llm = LLM("Qwen/Qwen2-VL-2B-Instruct", limit_mm_per_prompt={"image": 4})


# Create the request payload.
# 创建请求数据载荷。
video_frames = ... # load your video making sure it only has the number of frames specified earlier.
message = {
    "role": "user",
    "content": [
        {"type": "text", "text": "Describe this set of frames. Consider the frames to be a part of the same video."},
    ],
}
for i in range(len(video_frames)):
    base64_image = encode_image(video_frames[i]) # base64 encoding.
    new_image = {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
    message["content"].append(new_image)


# Perform inference and log output.
# 执行推理并记录输出。
outputs = llm.chat([message])


for o in outputs:
    generated_text = o.outputs[0].text
    print(generated_text)
```
