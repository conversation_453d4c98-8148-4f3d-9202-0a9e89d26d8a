# 文档路径: 05-automatic-prefix-caching > 01-introduction-apc > 简介 > 工作负载示例

## 工作负载示例

我们描述了 2 个示例工作负载，其中 APC 可以提供巨大的性能提升：

- 长文档查询，用户多次使用不同的查询内容对同一长文档（例如软件手册或年度报告）进行查询。在这种情况下，APC 允许 vLLM *仅处理一次*这个长文档，而不是一次又一次地处理这个长文档，并且所有后续请求都可以通过重用其 KV 缓存来避免重新计算这个长文档。这使得 vLLM 能够以更高的吞吐量和更低的延迟来服务未来的请求。
- 多轮对话，用户可以在同一个聊天会话中与应用程序多次进行对话。在这种情况下，APC 允许 vLLM 在所有后续的对话轮次中重用聊天历史的处理结果，而无需一次又一次地处理整个聊天历史。从而允许 vLLM 以更高的吞吐量和更低的延迟来服务未来的请求。
