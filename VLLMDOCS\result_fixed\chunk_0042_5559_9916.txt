# 文档路径: 02-serving > 01-openai-compatible-server > OpenAI 兼容服务器 > chat completion API 中的工具调用 > 配置文件

## chat completion API 中的工具调用

### 配置文件


`serve` 模块也可以接受来自 `yaml` 格式配置文件的参数。yaml 中的参数必须使用[这里](https://docs.vllm.ai/en/latest/serving/openai_compatible_server.html#command-line-arguments-for-the-server)概述的参数长格式来指定。

例如：

```yaml
# config.yaml


host: "127.0.0.1"
port: 6379
uvicorn-log-level: "info"
$ vllm serve SOME_MODEL --config config.yaml

---
```

**注意**

如果通过命令行和配置文件提供了参数，则命令行中的值将优先。优先级顺序为 `command line > config file values > defaults`。

---
