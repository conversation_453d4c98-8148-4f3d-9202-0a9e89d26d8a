# 文档路径: 04-quantization > 06-fp8-w8a8 > FP8 W8A8 > 量化过程 > 3. 评估准确性

## 量化过程

### 3. 评估准确性


安装 `vllm` 和 `lm-evaluation-harness`：

```plain
pip install vllm lm_eval==0.4.3
```

在 `vllm` 中加载并运行模型：

```python
from vllm import LLM
model = LLM("./Meta-Llama-3-8B-Instruct-FP8-Dynamic")
model.generate("Hello my name is")
```

使用 `lm_eval` 评估准确性（例如，在 `gsm8k` 的 250 个样本上）:

**注意：**

量化模型可能对 `bos` token 都存在很敏感。默认情况下，`lm_eval` 不会添加 `bos` 标记，因此请确保在运行评估时包含 `add_bos_token=True` 参数。

```plain
MODEL=$PWD/Meta-Llama-3-8B-Instruct-FP8-Dynamic
lm_eval \
  --model vllm \
  --model_args pretrained=$MODEL,add_bos_token=True \
  --tasks gsm8k  --num_fewshot 5 --batch_size auto --limit 250
```

以下是所得分数的示例：

```plain
|Tasks|Version|     Filter     |n-shot|  Metric   |   |Value|   |Stderr|
|-----|------:|----------------|-----:|-----------|---|----:|---|-----:|
|gsm8k|      3|flexible-extract|     5|exact_match|↑  |0.768|±  |0.0268|
|     |       |strict-match    |     5|exact_match|↑  |0.768|±  |0.0268|
```
