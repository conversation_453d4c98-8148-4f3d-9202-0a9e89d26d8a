# 文档路径: 03-models > 02-adding-a-new-model > 添加新模型 > 2. 重写 `forward` 的方法

## 2. 重写 `forward` 的方法

接下来，您需要按照以下步骤重写模型的 `forward()` 方法：

1. 删除任何不必要的代码，例如仅用于训练的代码。

2. 更改输入参数:

```diff
def forward(
    self,
    input_ids: torch.Tensor,
-     attention_mask: Optional[torch.Tensor] = None,
-     position_ids: Optional[torch.LongTensor] = None,
-     past_key_values: Optional[List[torch.FloatTensor]] = None,
-     inputs_embeds: Optional[torch.FloatTensor] = None,
-     labels: Optional[torch.LongTensor] = None,
-     use_cache: Optional[bool] = None,
-     output_attentions: Optional[bool] = None,
-     output_hidden_states: Optional[bool] = None,
-     return_dict: Optional[bool] = None,
- ) -> Union[Tuple, CausalLMOutputWithPast]:
+     positions: torch.Tensor,
+     kv_caches: List[torch.Tensor],
+     attn_metadata: AttentionMetadata,
+ ) -> Optional[SamplerOutput]:
```

1. 更新代码，考虑到 `input_ids` 和 `positions` 现在是扁平化的张量。

2. 根据模型的架构，用 `PagedAttention`、`PagedAttentionWithRoPE` 或 `PagedAttentionWithALiBi` 替换原有的注意力操作。

**注意：**

目前，vLLM 支持基础的多头注意力机制及其带有旋转位置嵌入的变体。如果你的模型使用了不同的注意力机制，则需要在 vLLM 中实现一个新的注意力层。
