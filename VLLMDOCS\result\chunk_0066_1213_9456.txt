# 文档路径: 02-serving > 10-frequently-asked-questions > 常见问题

> 问：如何使用 OpenAI API 在单个端口上提供多个模型的服务？

答: 假设您指的是使用与 OpenAI 兼容的服务器同时提供多个模型的服务，目前这是不支持的。您可以同时运行多个服务器实例（每个实例服务于不同的模型），并让另一层相应地将传入的请求路由到正确的服务器。

> 问：哪种模型可以用于离线推理嵌入？

答: 如果您想使用一个嵌入模型，可以试试：[https://huggingface.co/intfloat/e5-mistral-7b-instruct](https://huggingface.co/intfloat/e5-mistral-7b-instruct)。与之相反，Llama-3-8b、Mistral-7B-Instruct-v0.3 等模型是生成模型而不是嵌入模型。

> 问：在 vLLM 中，提示的输出是否会在不同运行中有所变化？

答: 是的，这种情况是存在的。vLLM 不能保证输出 tokens 的对数概率 (logprobs) 稳定。由于 Torch 操作中的数值不稳定性，或在批处理变化时 Torch 操作的非确定性行为，logprobs 可能会有所变化。有关更多详细信息，请参见数值准确性部分。

在 vLLM 中，由于其他并发请求、批大小的变化或在推测解码中的批扩展等因素，相同的请求可能会以不同的方式进行批处理。这些批处理的变化，加上 Torch 操作的数值不稳定性，可能导致每一步的 logit/logprob 值略有不同。这些差异可能会累积，最终导致采样到不同的 tokens。一旦采样到不同的 tokens，就很可能会发生进一步的偏差。