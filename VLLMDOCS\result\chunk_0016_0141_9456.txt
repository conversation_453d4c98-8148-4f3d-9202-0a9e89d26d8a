# 文档路径: 01-getting-started > 03-installation-with-openvino > 使用 OpenVINO 安装

由 OpenVINO 驱动的 vLLM 支持来自 `vLLM 支持的模型列表 <../models/supported_models>` 中的所有 LLM 模型，并且可以在所有 x86-64 CPU 上（至少需要 AVX2 支持）进行最佳的模型服务。OpenVINO 的 vLLM 后端支持以下高级 vLLM 特性：

- 前缀缓存 （`--enable-prefix-caching`）
- 分块预填充 （`--enable-chunked-prefill`）

**目录\*\***：\*\*

- [依赖环境](#依赖环境)
- [使用 dockerfile 快速开始](#使用-dockerfile-快速开始)
- [从源代码安装](#从源代码安装)
- [性能提示](#性能提示)
- [局限性](#局限性)