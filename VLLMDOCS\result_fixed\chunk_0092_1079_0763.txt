# 文档路径: 04-quantization > 05-int8-w8a8 > INT8 W8A8 > 量化过程 > 2. 准备校准数据

## 量化过程

### 2. 准备校准数据


将激活值量化为 INT8 时，您需要样本数据来估计激活尺度。最好使用与您部署数据高度匹配的校准数据。对于通用的指令调整模型，您可以使用像 `ultrachat` 这样的数据集：

```python
from datasets import load_dataset


NUM_CALIBRATION_SAMPLES = 512
MAX_SEQUENCE_LENGTH = 2048


# Load and preprocess the dataset
# 加载并预处理数据集


ds = load_dataset("HuggingFaceH4/ultrachat_200k", split="train_sft")
ds = ds.shuffle(seed=42).select(range(NUM_CALIBRATION_SAMPLES))


def preprocess(example):
    return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
ds = ds.map(preprocess)


def tokenize(sample):
    return tokenizer(sample["text"], padding=False, max_length=MAX_SEQUENCE_LENGTH, truncation=True, add_special_tokens=False)
ds = ds.map(tokenize, remove_columns=ds.column_names)
```
