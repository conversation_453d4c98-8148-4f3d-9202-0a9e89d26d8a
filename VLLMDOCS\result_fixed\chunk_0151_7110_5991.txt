# 文档路径: 10-tutorials > 04-vllm-langchain-tutorial > 将 LangChain 与 vLLM 结合使用：完整教程 > 1. 安装和设置 vLLM > Docker 安装

[在线运行此教程](https://openbayes.com/console/hyperai-tutorials/containers/ODfeIHjjXbW)

LangChain 是提供构建复杂操作链的工具，而 vLLM 专注于高效的模型推理。两者结合应用可以简化并加速智能 LLM 应用程序的开发。

在本教程中，我们将介绍如何将 LangChain 与 vLLM 结合使用，从设置到分布式推理和量化的所有内容。

## 1. 安装和设置 vLLM

### Docker 安装


对于那些在构建 vLLM 或处理 CUDA 兼容性时遇到问题的人，建议使用 NVIDIA PyTorch Docker 映像。它提供了一个预配置的环境，其中包含正确版本的 CUDA 和其他依赖项：

```python
# Use `--ipc=host` to ensure the shared memory is sufficient
docker run --gpus all -it --rm --ipc=host nvcr.io/nvidia/pytorch:23.10-py3
```

集成过程最终从安装所需的软件包开始。我们建议将 vLLM 升级到最新版本，以避免兼容性问题并受益于最新的改进和功能。

```python
pip install --upgrade --quiet vllm -q
pip install langchain langchain_community -q
```

本教程已经安装 vllm==0.6.4，只需将 langchain 相关包安装完毕。

```
!pip install -U langchain langchain_community -q
```
