# 文档路径: 03-models > 05-using-lora-adapters > 使用 LoRA 适配器 > –lora-modules 的新格式

## –lora-modules 的新格式


在以前的版本中，用户会通过以下格式提供 LoRA 模块，要么以键值对的形式，要么以 JSON 格式。例如：

```plain
--lora-modules sql-lora=$HOME/.cache/huggingface/hub/models--yard1--llama-2-7b-sql-lora-test/snapshots/0dfa347e8877a4d4ed19ee56c140fa518470028c/
```

此格式只包含每个 LoRA 模块的名称和路径，但未提供指定 _base_model_name_ 的方法。现在，你可以通过 JSON 格式同时指定 base_model_name 以及名称和路径。例如：

```plain
--lora-modules '{"name": "sql-lora", "path": "/path/to/lora", "base_model_name": "meta-llama/Llama-2-7b"}'
```

为了确保向后兼容性，你仍然可以使用旧的键值对格式 (name=path)，但在这种情况下 _base_model_name_ 将保持未指定状态。
