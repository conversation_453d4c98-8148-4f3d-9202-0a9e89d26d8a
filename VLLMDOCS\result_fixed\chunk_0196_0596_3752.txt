# 文档路径: 02-serving > 07-integrations > readme > 整合 > 文档内容

# 文档内容


- [使用 SkyPilot 部署和扩展](https://docs.vllm.ai/en/latest/serving/run_on_sky.html)
- [使用 KServe 部署](https://docs.vllm.ai/en/latest/serving/deploying_with_kserve.html)
- [使用 NVIDIA Triton 部署](https://docs.vllm.ai/en/latest/serving/deploying_with_triton.html)
- [使用 BentoML 部署](https://docs.vllm.ai/en/latest/serving/deploying_with_bentoml.html)
- [使用 Cerebrium 部署](https://docs.vllm.ai/en/latest/serving/deploying_with_cerebrium.html)
- [使用 LWS 部署](https://docs.vllm.ai/en/latest/serving/deploying_with_lws.html)
- [使用 dstack 部署](https://docs.vllm.ai/en/latest/serving/deploying_with_dstack.html)
- [使用 Langchain 服务](https://docs.vllm.ai/en/latest/serving/serving_with_langchain.html)
- [使用 llama_index 服务](https://docs.vllm.ai/en/latest/serving/serving_with_llamaindex.html)
