# 文档路径: 03-models > 03-enabling-multimodal-inputs > 启用多模态输入 > 3. 注册多模态 token 最大数量

## 3. 注册多模态 token 最大数量


对于模型作为输入所接受的每种模态类型，您需要计算每个数据实例中可能的最大 token 数，并通过 `INPUT_REGISTRY.register_dummy_data` 进行注册。

```diff
from vllm.inputs import INPUT_REGISTRY
from vllm.model_executor.models.interfaces import SupportsMultiModal
from vllm.multimodal import MULTIMODAL_REGISTRY


@MULTIMODAL_REGISTRY.register_image_input_mapper()
+ @MULTIMODAL_REGISTRY.register_max_image_tokens(<your_calculation>)
@INPUT_REGISTRY.register_dummy_data(<your_dummy_data_factory>)
class YourModelForImage2Seq(nn.Module, SupportsMultiModal):
```

以下是一些示例：

- 图像输入（静态特征尺寸）: [LLaVA-1.5 模型](https://github.com/vllm-project/vllm/blob/main/vllm/model_executor/models/llava.py)
- 图像输入（动态特征尺寸）: [LLaVA-NeXT 模型](https://github.com/vllm-project/vllm/blob/main/vllm/model_executor/models/llava_next.py)

**另见**

`输入处理管道`
