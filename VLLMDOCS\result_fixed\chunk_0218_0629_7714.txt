# 文档路径: 07-developer-documentation > 06-multi-modality > readme > 多模态 > 模块内容 > 图像类

## 模块内容

### 图像类


> class vllm.multimodal.image.ImagePlugin
> [[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/multimodal/image.html#ImagePlugin)

基类 (Bases)：[MultiModalPlugin](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin)

图像数据插件。

> get_data_key() → [str](https://docs.python.org/3/library/stdtypes.html#str) > [[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/multimodal/image.html#ImagePlugin.get_data_key)

获取与模态对应的数据键。
