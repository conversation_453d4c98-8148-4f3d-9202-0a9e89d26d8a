# 文档路径: 01-getting-started > 05-installation-with-neuron > 使用 Neuron 安装 > 从源代码构建 > 步骤 3. 从源代码安装 vLLM

## 从源代码构建

以下说明适用于 Neuron SDK 2.16 及更高版本。


### 步骤 3. 从源代码安装 vLLM

一旦安装了 neuronx-cc 和 transformers-neuronx 软件包，我们就能安装 vllm 了，如下所示：

```plain
git clone https://github.com/vllm-project/vllm.git
cd vllm
pip install -U -r requirements-neuron.txt
VLLM_TARGET_DEVICE="neuron" pip install .
```

如果在安装过程中正确检测到 neuron 包，则会安装 `vllm-0.3.0+neuron212`。
