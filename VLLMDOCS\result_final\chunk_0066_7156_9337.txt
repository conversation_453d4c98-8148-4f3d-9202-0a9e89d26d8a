# 文档路径: 03-models > 05-using-lora-adapters > 使用 LoRA 适配器 > 动态提供 LoRA 适配器

## 动态提供 LoRA 适配器


除了在服务器启动时提供 LoRA 适配器，vLLM 服务器现在还支持通过专门的 API 端点在运行时动态加载和卸载 LoRA 适配器。当需要灵活地在运行中更改模型时，这个功能特别有用。

请注意：在生产环境中启用这一特性可能带来风险，因为用户可能会参与到模型适配器的管理工作中。

如需启用 LoRA 适配器的动态加载和卸载，请确保环境变量 _VLLM_ALLOW_RUNTIME_LORA_UPDATING_ 已被设置为 True。启用此选项后，API 服务器会在日志中记录一条警告，以示动态加载功能已经开启。

```python
export VLLM_ALLOW_RUNTIME_LORA_UPDATING=True
```

加载 LoRA 适配器：

如需动态加载一个 LoRA 适配器，需要向 _/v1/load_lora_adapter_ 端点发送一个 POST 请求，并提供要加载的适配器的详细信息。请求的数据负载应包括 LoRA 适配器的名称和路径。

加载 LoRA 适配器的请求示例：

```python
curl -X POST http://localhost:8000/v1/load_lora_adapter \
-H "Content-Type: application/json" \
-d '{
    "lora_name": "sql_adapter",
    "lora_path": "/path/to/sql-lora-adapter"
}'
```

如果请求成功，API 将返回一个 200 OK 的状态码。如果发生错误，比如适配器无法找到或加载，将返回一个适当的错误信息。

卸载 LoRA 适配器：

如需卸载之前已加载的 LoRA 适配器，需要向 _/v1/unload_lora_adapter_ 端点发送一个 POST 请求，并提供要卸载的适配器的名称或 ID。

卸载 LoRA 适配器的请求示例：

```plain
curl -X POST http://localhost:8000/v1/unload_lora_adapter \
-H "Content-Type: application/json" \
-d '{
    "lora_name": "sql_adapter"
}'
```
