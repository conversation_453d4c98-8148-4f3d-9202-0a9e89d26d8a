# 文档路径: 03-models > 01-supported-models > 支持的模型 > 仅文本语言模型 > 文本 Embedding

## 仅文本语言模型

### 文本 Embedding


| 架构         | 模型          | HF 模型案例                           | [LoRA](https://docs.vllm.ai/en/latest/models/lora.html#lora) | [PP](https://docs.vllm.ai/en/latest/serving/distributed_serving.html#distributed-serving) |
| :----------- | :------------ | :------------------------------------ | :----------------------------------------------------------- | :---------------------------------------------------------------------------------------- |
| Gemma2Model  | Gemma2-based  | BAAI/bge-multilingual-gemma2, etc.    |                                                              | ✅︎                                                                                       |
| MistralModel | Mistral-based | intfloat/e5-mistral-7b-instruct, etc. |                                                              | ✅︎                                                                                       |

**注意：**

有些模型架构同时支持生成和嵌入任务。在这种情况下，你需要传入 `--task embedding` 参数，才能以嵌入模式运行该模型。
