# 文档路径: 10-tutorials > 02-infer-34b-with-vllm > 使用 vLLM 对 Qwen2.5 推理 > 6. 提取推理概率

## 6. 提取推理概率


```
results = []
errors = 0

for i,response in enumerate(responses):
    try:
        x = response.outputs[0].logprobs[0]
        logprobs = []
        for k in KEEP:
            if k in x:
                logprobs.append( math.exp(x[k].logprob) )
            else:
                logprobs.append( 0 )
                print(f"bad logits {i}")
        logprobs = np.array( logprobs )
        logprobs /= logprobs.sum()
        results.append( logprobs )
    except:
        #print(f"error {i}")
        results.append( np.array([1/3., 1/3., 1/3.]) )
        errors += 1

print(f"There were {errors} inference errors out of {i+1} inferences")
results = np.vstack(results)
```
