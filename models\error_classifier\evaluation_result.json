{"model_path": "models/error_classifier", "evaluation_time": "2025-07-07T22:13:12.258907", "sample_predictions": [{"text": "HTTP 500 Internal Server Error", "prediction": "类别_26", "confidence": 0.04886086657643318}, {"text": "Connection timeout after 30 seconds", "prediction": "类别_26", "confidence": 0.05296693369746208}, {"text": "Authentication failed: invalid token", "prediction": "类别_26", "confidence": 0.05336807668209076}, {"text": "JSON parse error: unexpected character", "prediction": "类别_26", "confidence": 0.05289730057120323}, {"text": "Database connection refused", "prediction": "类别_26", "confidence": 0.048363152891397476}]}