# 文档路径: 08-indices-and-tables > 01-index > 索引 > C

## C


| [chat() (vllm.LLM method)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm.html#vllm.LLM.chat)                                                                                                       | [check_health() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.check_health)                                                 |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [clone() (vllm.SamplingParams method)](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams.clone)                                                                                     | [create_input_mapper() (vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.create_input_mapper) |
| [create_input_processor() (vllm.inputs.registry.InputRegistry method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputRegistry.create_input_processor) |                                                                                                                                                                                                 |
