# 文档路径: 07-developer-documentation > 04-vllm-paged-attention > vLLM  分页注意力

- 目前，vLLM 使用自己的多头查询注意力内核 (multi-head query attention kernel) 实现（位于`csrc/attention/attention_kernels.cu`）。该内核旨在兼容 vLLM 的分页键值缓存，其中键和值缓存存储在不同的块中（注意，这里的块概念与 GPU 线程块不同。因此，在后续文档中，我们将把 vLLM 分页注意力块称为「块 (block)」，而把 GPU 线程块称为「线程块(thread block)」）。
- 为了实现高性能，该内核依赖于专门设计的内存布局和访问方法，特别是在线程从全局内存读取数据到共享内存时。本文档的目的是逐步提供内核实现的高层次解释，以帮助那些希望了解 vLLM 多头查询注意力内核的人。在阅读完本文档后，用户能够更好地理解，并更容易跟进实际的实现。
- 请注意，本文件可能不会涵盖所有细节，例如如何计算相应数据的正确索引或点乘实现。不过，在阅读完本文档并熟悉高层次的逻辑流程后，您应该能够更容易阅读实际代码并理解细节。