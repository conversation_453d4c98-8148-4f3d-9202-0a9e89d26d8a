# 文档路径: 10-tutorials > 01-vLLM-stepbysteb > vLLM 入门教程：零基础分步指南 > 二、开始使用 > 2.2 离线推理

## 二、开始使用

### 2.2 离线推理


vLLM 作为一个开源项目，可以通过其 Python API 执行 LLM 推理。以下是一个简单的示例，请将代码保存为 `offline_infer.py` 文件:

```python
from vllm import LLM, SamplingParams

# 输入几个问题
prompts = [
    "你好，你是谁？",
    "法国的首都在哪里？",
]

# 设置初始化采样参数
sampling_params = SamplingParams(temperature=0.8, top_p=0.95, max_tokens=100)

# 加载模型，确保路径正确
llm = LLM(model="/input0/Qwen-1_8B-Chat/", trust_remote_code=True, max_model_len=4096)

# 展示输出结果
outputs = llm.generate(prompts, sampling_params)

# 打印输出结果
for output in outputs:
    prompt = output.prompt
    generated_text = output.outputs[0].text
    print(f"Prompt: {prompt!r}, Generated text: {generated_text!r}")
```

然后运行脚本:

```bash
python offline_infer.py
```

模型加载后，您将看到以下输出：

```python
Processed prompts: 100%|██████████| 2/2 [00:00<00:00, 10.23it/s, est. speed input: 56.29 toks/s, output: 225.14 toks/s]
Prompt: '你好，你是谁？', Generated text: '我是来自阿里云的大规模语言模型，我叫通义千问。'
Prompt: '法国的首都在哪里？', Generated text: '法国的首都是巴黎。'
```
