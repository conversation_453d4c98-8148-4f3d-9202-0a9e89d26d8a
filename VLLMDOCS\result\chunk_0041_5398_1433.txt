# 文档路径: 01-getting-started > 07-installation-with-xpu > 使用 XPU 安装 > 从源代码构建

## 从源代码构建

- 首先，安装所需的驱动程序和 intel OneAPI 2024.1 (或更高版本)。
- 其次，安装用于 vLLM XPU 后端构建的 Python 包:

```plain
source /opt/intel/oneapi/setvars.sh
pip install --upgrade pip
pip install -v -r requirements-xpu.txt
```

- 最后，构建并安装 vLLM XPU 后端:

```plain
VLLM_TARGET_DEVICE=xpu python setup.py install
```

**注意**

- FP16 是当前 XPU 后端的默认数据类型，未来将支持 BF16 数据类型。
