# 文档路径: 03-models > 02-adding-a-new-model > 添加新模型

本文档提供将 [HuggingFace Transformers](https://github.com/huggingface/transformers) 模型集成到 vLLM 的高级指南。

**注意：**

添加新模型的复杂性在很大程度上取决于模型的架构。如果模型与 vLLM 中的现有模型具有相似的架构，则该过程相当简单。然而，对于包含新运算符（例如，新的注意力机制）的模型，该过程可能会更复杂一些。

**注意：**

默认情况下，vLLM 模型不支持多模态输入。要启用多模式支持，请在此处实现模型后遵循 `本指南 <enabling_multimodal_inputs>` 。

**提示：**

如果您在将模型集成到 vLLM 时遇到问题，请随时在我们的 [GitHub](https://github.com/vllm-project/vllm/issues) 存储库上提出问题。我们很乐意为您提供帮助！