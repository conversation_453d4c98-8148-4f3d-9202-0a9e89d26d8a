# 文档路径: 01-getting-started > 08-quickstart > 快速入门 > 兼容 OpenAI 服务器 > 在 vLLM 中使用 OpenAI Completions API

## 兼容 OpenAI 服务器

vLLM 可以作为一个实现了 OpenAI API 协议的服务器进行部署。这使得 vLLM 可以直接替代使用 OpenAI API 的应用程序。默认情况下，它在 [http://localhost:8000](http://localhost:8000) 启动服务器。您可以使用 `--host` 和`--port` 参数指定地址。当前，该服务器一次仅托管一个模型（在下面的命令中为 OPT-125M），并实现了[模型列表 (list models)](https://platform.openai.com/docs/api-reference/models/list)、[创建聊天补全 (create chat completion)](https://platform.openai.com/docs/api-reference/chat/completions/create) 和[创建完成 (create completion)](https://platform.openai.com/docs/api-reference/completions/create) 端点。我们正在积极添加对更多端点的支持。

启动服务器：

```plain
vllm serve facebook/opt-125m
```

默认情况下，服务器使用存储在 tokenizer 中的预定义聊天模板。您可以使用 `--chat-template` 参数覆盖此模板：

```plain
vllm serve facebook/opt-125m --chat-template ./examples/template_chatml.jinja
```

该服务器可以按照与 OpenAI API 相同的格式进行查询。例如，列出模型：

```plain
curl http://localhost:8000/v1/models
```

您可以传入参数`--api-key`或设置环境变量`VLLM_API_KEY`，以使服务器能够检查标头中的 API 密钥。


### 在 vLLM 中使用 OpenAI Completions API

使用输入提示查询模型：

```plain
curl http://localhost:8000/v1/completions \
    -H "Content-Type: application/json" \
    -d '{
        "model": "facebook/opt-125m",
        "prompt": "San Francisco is a",
        "max_tokens": 7,
        "temperature": 0
    }'
```

由于该服务器与 OpenAI API 兼容，因此您可以把它作为使用 OpenAI API 的任意应用程序的直接替代品。例如，另一种查询服务器的方法是通过 `openai`的 python 包：

```python
from openai import OpenAI

# Modify OpenAI's API key and API base to use vLLM's API server.
# 使用 vLLM 的 API 服务器需要修改 OpenAI 的 API 密钥和 API 库。

openai_api_key = "EMPTY"
openai_api_base = "http://localhost:8000/v1"
client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)
completion = client.completions.create(model="facebook/opt-125m",
                                      prompt="San Francisco is a")
print("Completion result:", completion)
```

有关更详细的客户端示例，请参阅 [examples/openai_completion_client.py](https://github.com/vllm-project/vllm/blob/main/examples/openai_completion_client.py)。
