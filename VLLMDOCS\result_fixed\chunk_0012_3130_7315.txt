# 文档路径: 01-getting-started > 02-installation-with-rocm > 使用 ROCm 安装 > 选项 1：使用 docker 从源代码构建 （推荐）

## 选项 1：使用 docker 从源代码构建 （推荐）


您可以从源代码构建并安装 vLLM。

首先，从 [Dockerfile.rocm](https://github.com/vllm-project/vllm/blob/main/Dockerfile.rocm) 构建一个 docker 镜像，并从该镜像启动一个 docker 容器。

[Dockerfile.rocm](https://github.com/vllm-project/vllm/blob/main/Dockerfile.rocm) 默认使用 ROCm 6.1，但在较旧的 vLLM 分支中也支持 ROCm 5.7 和 6.0。方法非常灵活，可以使用以下参数自定义 Docker 镜像的构建：

- _BASE_IMAGE_：指定运行 `docker build` 时使用的基础镜像，特别是 ROCm 基础镜像上的 PyTorch。
- _BUILD_FA_：指定是否构建 CK flash-attention。默认值为 1。对于 [Radeon RX 7900 系列 (gfx1100)](https://rocm.docs.amd.com/projects/radeon/en/latest/index.html)，在 flash-attention 支持该目标前应将其设置为 0。
- _FX_GFX_ARCHS_：指定用于构建 CK flash-attention 的 GFX 架构，例如 MI200 和 MI300 的 _gfx90a;gfx942_。默认为 _gfx90a;gfx942\*\*。_
- _FA_BRANCH_：指定用于在 [ROCm's flash-attention repo](https://github.com/ROCmSoftwarePlatform/flash-attention) 中构建 CK flash-attention 的分支。默认为 _ae7928c\*\*。_
- _BUILD_TRITON_: 指定是否构建 triton flash-attention。默认值为 1。

这些值可以在使用 `--build-arg` 选项运行 `docker build` 时传入。

要在 ROCm 6.1 上为 MI200 和 MI300 系列构建 vllm，您可以使用默认值：

```plain
DOCKER_BUILDKIT=1 docker build -f Dockerfile.rocm -t vllm-rocm .
```

要在 ROCm 6.1 上为 Radeon RX7900 系列 (gfx1100) 构建 vllm，您应该指定 `BUILD_FA` ，如下所示：

```plain
DOCKER_BUILDKIT=1 docker build --build-arg BUILD_FA="0" -f Dockerfile.rocm -t vllm-rocm .
```

要运行上面的 docker 镜像 `vllm-rocm`，请使用以下命令：

```plain
docker run -it \
   --network=host \
   --group-add=video \
   --ipc=host \
   --cap-add=SYS_PTRACE \
   --security-opt seccomp=unconfined \
   --device /dev/kfd \
   --device /dev/dri \
   -v <path/to/model>:/app/model \
   vllm-rocm \
   bash
```

其中 `<path/to/model>` 是存储模型的位置，例如 llama2 或 llama3 模型的权重。
