[{"chunk_id": "5625_9836", "file_name": "chunk_0001_5625_9836.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 入门 > 部署 > 模型 > 量化 > 自动前缀缓存 > 性能基准测试 > 开发者文档 > 社区", "main_title": "社区", "section_level": 2, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "5625_0293", "file_name": "chunk_0002_5625_0293.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > [索引和表格](https://vllm.hyper.ai/docs/indices-and-tables/index)", "main_title": "[索引和表格](https://vllm.hyper.ai/docs/indices-and-tables/index)", "section_level": 1, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "3739_6690", "file_name": "chunk_0003_3739_6690.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\01-installation.md", "directory_path": "01-getting-started", "filename": "01-installation", "title_hierarchy": "安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "安装"}}, {"chunk_id": "3739_4820", "file_name": "chunk_0004_3739_4820.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\01-installation.md", "directory_path": "01-getting-started", "filename": "01-installation", "title_hierarchy": "安装 > 使用 pip 安装", "main_title": "使用 pip 安装", "section_level": 2, "document_title": "安装"}}, {"chunk_id": "3739_7769", "file_name": "chunk_0005_3739_7769.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\01-installation.md", "directory_path": "01-getting-started", "filename": "01-installation", "title_hierarchy": "安装 > 从源代码构建", "main_title": "从源代码构建", "section_level": 2, "document_title": "安装"}}, {"chunk_id": "5466_6690", "file_name": "chunk_0006_5466_6690.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\02-installation-with-rocm.md", "directory_path": "01-getting-started", "filename": "02-installation-with-rocm", "title_hierarchy": "使用 ROCm 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 ROCm 安装"}}, {"chunk_id": "5466_9718", "file_name": "chunk_0007_5466_9718.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\02-installation-with-rocm.md", "directory_path": "01-getting-started", "filename": "02-installation-with-rocm", "title_hierarchy": "使用 ROCm 安装 > 选项 1：使用 docker 从源代码构建 （推荐）", "main_title": "选项 1：使用 docker 从源代码构建 （推荐）", "section_level": 2, "document_title": "使用 ROCm 安装"}}, {"chunk_id": "5466_2544", "file_name": "chunk_0008_5466_2544.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\02-installation-with-rocm.md", "directory_path": "01-getting-started", "filename": "02-installation-with-rocm", "title_hierarchy": "使用 ROCm 安装 > 选项 2：从源代码构建", "main_title": "选项 2：从源代码构建", "section_level": 2, "document_title": "使用 ROCm 安装"}}, {"chunk_id": "9046_6690", "file_name": "chunk_0009_9046_6690.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "9046_1374", "file_name": "chunk_0010_9046_1374.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 使用 Dockerfile 快速开始", "main_title": "使用 Dockerfile 快速开始", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "9046_5283", "file_name": "chunk_0011_9046_5283.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 从源代码安装", "main_title": "从源代码安装", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "9046_5389", "file_name": "chunk_0012_9046_5389.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 性能提示", "main_title": "性能提示", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "9046_0970", "file_name": "chunk_0013_9046_0970.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 局限性", "main_title": "局限性", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "1712_6690", "file_name": "chunk_0014_1712_6690.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "1712_1374", "file_name": "chunk_0015_1712_1374.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 使用 Dockerfile 快速开始", "main_title": "使用 Dockerfile 快速开始", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "1712_7769", "file_name": "chunk_0016_1712_7769.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 从源代码构建", "main_title": "从源代码构建", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "1712_5869", "file_name": "chunk_0017_1712_5869.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 相关运行时环境变量", "main_title": "相关运行时环境变量", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "1712_7036", "file_name": "chunk_0018_1712_7036.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > PyTorch 的英特尔扩展", "main_title": "PyTorch 的英特尔扩展", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "1712_5389", "file_name": "chunk_0019_1712_5389.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 性能提示", "main_title": "性能提示", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "1183_6690", "file_name": "chunk_0020_1183_6690.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\05-installation-with-neuron.md", "directory_path": "01-getting-started", "filename": "05-installation-with-neuron", "title_hierarchy": "使用 Neuron 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 <PERSON><PERSON>on 安装"}}, {"chunk_id": "1183_7769", "file_name": "chunk_0021_1183_7769.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\05-installation-with-neuron.md", "directory_path": "01-getting-started", "filename": "05-installation-with-neuron", "title_hierarchy": "使用 Neuron 安装 > 从源代码构建 > 步骤 0. 启动 Trn1/Inf2 实例 > 步骤 1. 安装驱动程序和工具 > 步骤 2. 安装 Transformers-neuronx 及其依赖 > 步骤 3. 从源代码安装 vLLM", "main_title": "步骤 3. 从源代码安装 vLLM", "section_level": 3, "document_title": "使用 <PERSON><PERSON>on 安装"}}, {"chunk_id": "4192_6690", "file_name": "chunk_0022_4192_6690.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\06-installation-with-tpu.md", "directory_path": "01-getting-started", "filename": "06-installation-with-tpu", "title_hierarchy": "使用 TPU 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 TPU 安装"}}, {"chunk_id": "4192_2081", "file_name": "chunk_0023_4192_2081.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\06-installation-with-tpu.md", "directory_path": "01-getting-started", "filename": "06-installation-with-tpu", "title_hierarchy": "使用 TPU 安装 > 使用`Dockerfile.tpu` 构建 Docker 镜像", "main_title": "使用`Dockerfile.tpu` 构建 Docker 镜像", "section_level": 2, "document_title": "使用 TPU 安装"}}, {"chunk_id": "4192_7769", "file_name": "chunk_0024_4192_7769.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\06-installation-with-tpu.md", "directory_path": "01-getting-started", "filename": "06-installation-with-tpu", "title_hierarchy": "使用 TPU 安装 > 从源代码构建", "main_title": "从源代码构建", "section_level": 2, "document_title": "使用 TPU 安装"}}, {"chunk_id": "5858_6690", "file_name": "chunk_0025_5858_6690.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\07-installation-with-xpu.md", "directory_path": "01-getting-started", "filename": "07-installation-with-xpu", "title_hierarchy": "使用 XPU 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 XPU 安装"}}, {"chunk_id": "5858_1374", "file_name": "chunk_0026_5858_1374.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\07-installation-with-xpu.md", "directory_path": "01-getting-started", "filename": "07-installation-with-xpu", "title_hierarchy": "使用 XPU 安装 > 使用 Dockerfile 快速开始", "main_title": "使用 Dockerfile 快速开始", "section_level": 2, "document_title": "使用 XPU 安装"}}, {"chunk_id": "5858_7769", "file_name": "chunk_0027_5858_7769.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\07-installation-with-xpu.md", "directory_path": "01-getting-started", "filename": "07-installation-with-xpu", "title_hierarchy": "使用 XPU 安装 > 从源代码构建", "main_title": "从源代码构建", "section_level": 2, "document_title": "使用 XPU 安装"}}, {"chunk_id": "9615_6690", "file_name": "chunk_0028_9615_6690.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\08-quickstart.md", "directory_path": "01-getting-started", "filename": "08-quickstart", "title_hierarchy": "快速入门 > 离线批量推理", "main_title": "离线批量推理", "section_level": 2, "document_title": "快速入门"}}, {"chunk_id": "9615_5021", "file_name": "chunk_0029_9615_5021.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\08-quickstart.md", "directory_path": "01-getting-started", "filename": "08-quickstart", "title_hierarchy": "快速入门 > 兼容 OpenAI 服务器 > 在 vLLM 中使用 OpenAI Completions API > 在 vLLM 中使用 OpenAI Chat API", "main_title": "在 vLLM 中使用 OpenAI Chat API", "section_level": 3, "document_title": "快速入门"}}, {"chunk_id": "2405_5387", "file_name": "chunk_0030_2405_5387.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\09-debugging-tips.md", "directory_path": "01-getting-started", "filename": "09-debugging-tips", "title_hierarchy": "调试技巧 > 调试挂起与崩溃问题", "main_title": "调试挂起与崩溃问题", "section_level": 2, "document_title": "调试技巧"}}, {"chunk_id": "6757_6690", "file_name": "chunk_0031_6757_6690.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > API 参考", "main_title": "API 参考", "section_level": 2, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "6757_5247", "file_name": "chunk_0032_6757_5247.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > 附加参数 > Chat API 的附加参数 > Completions API 的附加参数", "main_title": "Completions API 的附加参数", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "6757_6505", "file_name": "chunk_0033_6757_6505.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > 聊天模板", "main_title": "聊天模板", "section_level": 2, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "6757_4648", "file_name": "chunk_0034_6757_4648.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > 服务器的命令行参数 > 命名参数", "main_title": "命名参数", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "6757_1732", "file_name": "chunk_0035_6757_1732.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > chat completion API 中的工具调用 > 命名函数调用 > 配置文件", "main_title": "配置文件", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "6757_1732", "file_name": "chunk_0036_6757_1732.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > chat completion API 中的工具调用 > 自动函数调用", "main_title": "自动函数调用", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "2624_7222", "file_name": "chunk_0037_2624_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\02-deploying-with-docker.md", "directory_path": "02-serving", "filename": "02-deploying-with-docker", "title_hierarchy": "使用 Docker 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 Docker 进行部署"}}, {"chunk_id": "2837_3071", "file_name": "chunk_0038_2837_3071.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\03-distributed-inference-and-serving.md", "directory_path": "02-serving", "filename": "03-distributed-inference-and-serving", "title_hierarchy": "分布式推理和服务 > 如何决定分布式推理策略？", "main_title": "如何决定分布式推理策略？", "section_level": 2, "document_title": "分布式推理和服务"}}, {"chunk_id": "2837_3007", "file_name": "chunk_0039_2837_3007.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\03-distributed-inference-and-serving.md", "directory_path": "02-serving", "filename": "03-distributed-inference-and-serving", "title_hierarchy": "分布式推理和服务 > 分布式推理和服务的详细信息", "main_title": "分布式推理和服务的详细信息", "section_level": 2, "document_title": "分布式推理和服务"}}, {"chunk_id": "2837_7508", "file_name": "chunk_0040_2837_7508.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\03-distributed-inference-and-serving.md", "directory_path": "02-serving", "filename": "03-distributed-inference-and-serving", "title_hierarchy": "分布式推理和服务 > 多节点推理和服务", "main_title": "多节点推理和服务", "section_level": 2, "document_title": "分布式推理和服务"}}, {"chunk_id": "8033_7222", "file_name": "chunk_0041_8033_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\04-production-metrics.md", "directory_path": "02-serving", "filename": "04-production-metrics", "title_hierarchy": "生产指标 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "生产指标"}}, {"chunk_id": "3834_7222", "file_name": "chunk_0042_3834_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\05-environment-variables.md", "directory_path": "02-serving", "filename": "05-environment-variables", "title_hierarchy": "环境变量 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "环境变量"}}, {"chunk_id": "9550_6690", "file_name": "chunk_0043_9550_6690.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\06-usage-stats-collection.md", "directory_path": "02-serving", "filename": "06-usage-stats-collection", "title_hierarchy": "使用统计数据收集 > 收集了哪些数据？", "main_title": "收集了哪些数据？", "section_level": 2, "document_title": "使用统计数据收集"}}, {"chunk_id": "9550_8378", "file_name": "chunk_0044_9550_8378.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\06-usage-stats-collection.md", "directory_path": "02-serving", "filename": "06-usage-stats-collection", "title_hierarchy": "使用统计数据收集 > 退出使用统计数据收集", "main_title": "退出使用统计数据收集", "section_level": 2, "document_title": "使用统计数据收集"}}, {"chunk_id": "1725_7222", "file_name": "chunk_0045_1725_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\08-tensorizer.md", "directory_path": "02-serving", "filename": "08-tensorizer", "title_hierarchy": "使用 CoreWeave 的张量器加载模型 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 CoreWeave 的张量器加载模型"}}, {"chunk_id": "5543_6690", "file_name": "chunk_0046_5543_6690.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\09-compatibility matrix.md", "directory_path": "02-serving", "filename": "09-compatibility matrix", "title_hierarchy": "兼容性矩阵 > Feature x Feature", "main_title": "Feature x Feature", "section_level": 2, "document_title": "兼容性矩阵"}}, {"chunk_id": "5543_5387", "file_name": "chunk_0047_5543_5387.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\09-compatibility matrix.md", "directory_path": "02-serving", "filename": "09-compatibility matrix", "title_hierarchy": "兼容性矩阵 > Feature x Hardware", "main_title": "Feature x Hardware", "section_level": 2, "document_title": "兼容性矩阵"}}, {"chunk_id": "0500_6690", "file_name": "chunk_0048_0500_6690.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\10-frequently-asked-questions.md", "directory_path": "02-serving", "filename": "10-frequently-asked-questions", "title_hierarchy": "常见问题 > 缓解策略", "main_title": "缓解策略", "section_level": 2, "document_title": "常见问题"}}, {"chunk_id": "8009_6690", "file_name": "chunk_0049_8009_6690.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 仅文本语言模型 > 文本生成 > 文本 Embedding > 获奖模型", "main_title": "获奖模型", "section_level": 3, "document_title": "支持的模型"}}, {"chunk_id": "8009_0529", "file_name": "chunk_0050_8009_0529.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 多模态语言模型 > 文本生成 > 多模态 Embedding", "main_title": "多模态 Embedding", "section_level": 3, "document_title": "支持的模型"}}, {"chunk_id": "8009_7183", "file_name": "chunk_0051_8009_7183.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 模型支持政策", "main_title": "模型支持政策", "section_level": 1, "document_title": "支持的模型"}}, {"chunk_id": "4867_6690", "file_name": "chunk_0052_4867_6690.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 0. Fork vLLM 存储库", "main_title": "0. Fork vLLM 存储库", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "4867_4045", "file_name": "chunk_0053_4867_4045.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 1. 引入你的模型代码", "main_title": "1. 引入你的模型代码", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "4867_8625", "file_name": "chunk_0054_4867_8625.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 2. 重写 `forward` 的方法", "main_title": "2. 重写 `forward` 的方法", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "4867_0224", "file_name": "chunk_0055_4867_0224.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 3.（可选）实现张量并行和量化支持", "main_title": "3.（可选）实现张量并行和量化支持", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "4867_5327", "file_name": "chunk_0056_4867_5327.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 5. 注册模型", "main_title": "5. 注册模型", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "4867_7626", "file_name": "chunk_0057_4867_7626.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 6. 树外模型集成", "main_title": "6. 树外模型集成", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "5898_6690", "file_name": "chunk_0058_5898_6690.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 1. 更新基础 vLLM 模型", "main_title": "1. 更新基础 vLLM 模型", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "5898_8948", "file_name": "chunk_0059_5898_8948.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 2. 注册输入映射器", "main_title": "2. 注册输入映射器", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "5898_6197", "file_name": "chunk_0060_5898_6197.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 3. 注册多模态 token 最大数量", "main_title": "3. 注册多模态 token 最大数量", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "5898_6499", "file_name": "chunk_0061_5898_6499.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 4.（可选）注册虚拟数据", "main_title": "4.（可选）注册虚拟数据", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "5898_9594", "file_name": "chunk_0062_5898_9594.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 5.（可选）注册输入处理器", "main_title": "5.（可选）注册输入处理器", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "8843_6690", "file_name": "chunk_0063_8843_6690.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\04-engine-arguments.md", "directory_path": "03-models", "filename": "04-engine-arguments", "title_hierarchy": "引擎参数 > 命名参数", "main_title": "命名参数", "section_level": 3, "document_title": "引擎参数"}}, {"chunk_id": "8843_9011", "file_name": "chunk_0064_8843_9011.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\04-engine-arguments.md", "directory_path": "03-models", "filename": "04-engine-arguments", "title_hierarchy": "引擎参数 > 异步引擎参数 > 命名参数", "main_title": "命名参数", "section_level": 3, "document_title": "引擎参数"}}, {"chunk_id": "7156_6690", "file_name": "chunk_0065_7156_6690.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器 > LoRA 适配器服务", "main_title": "LoRA 适配器服务", "section_level": 2, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "7156_9337", "file_name": "chunk_0066_7156_9337.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器 > 动态提供 LoRA 适配器", "main_title": "动态提供 LoRA 适配器", "section_level": 2, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "7156_4497", "file_name": "chunk_0067_7156_4497.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器 > –lora-modules 的新格式", "main_title": "–lora-modules 的新格式", "section_level": 2, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "7156_6831", "file_name": "chunk_0068_7156_6831.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器 > 模型卡中的 LoRA 模型谱系", "main_title": "模型卡中的 LoRA 模型谱系", "section_level": 2, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "5283_6690", "file_name": "chunk_0069_5283_6690.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\06-using-vlms.md", "directory_path": "03-models", "filename": "06-using-vlms", "title_hierarchy": "使用 VLM > 离线推理 > 单图像输入 > 多图像输入", "main_title": "多图像输入", "section_level": 3, "document_title": "使用 VLM"}}, {"chunk_id": "5283_7073", "file_name": "chunk_0070_5283_7073.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\06-using-vlms.md", "directory_path": "03-models", "filename": "06-using-vlms", "title_hierarchy": "使用 VLM > 在线推理", "main_title": "在线推理", "section_level": 2, "document_title": "使用 VLM"}}, {"chunk_id": "4499_6690", "file_name": "chunk_0071_4499_6690.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码 > 用草稿模型进行推测", "main_title": "用草稿模型进行推测", "section_level": 2, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "4499_2561", "file_name": "chunk_0072_4499_2561.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码 > 通过在提示符中匹配 n-grams 进行推测", "main_title": "通过在提示符中匹配 n-grams 进行推测", "section_level": 2, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "4499_5702", "file_name": "chunk_0073_4499_5702.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码 > 使用 MLP 推测器进行推测", "main_title": "使用 MLP 推测器进行推测", "section_level": 2, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "4499_0317", "file_name": "chunk_0074_4499_0317.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码 > 相关 vLLM 贡献者的资源", "main_title": "相关 vLLM 贡献者的资源", "section_level": 2, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "7116_6937", "file_name": "chunk_0075_7116_6937.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\08-performance-and-tuning.md", "directory_path": "03-models", "filename": "08-performance-and-tuning", "title_hierarchy": "性能与调优 > 抢占", "main_title": "抢占", "section_level": 2, "document_title": "性能与调优"}}, {"chunk_id": "7116_9117", "file_name": "chunk_0076_7116_9117.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\08-performance-and-tuning.md", "directory_path": "03-models", "filename": "08-performance-and-tuning", "title_hierarchy": "性能与调优 > 分块预填充", "main_title": "分块预填充", "section_level": 2, "document_title": "性能与调优"}}, {"chunk_id": "1073_6690", "file_name": "chunk_0077_1073_6690.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\01-supported_hardware.md", "directory_path": "04-quantization", "filename": "01-supported_hardware", "title_hierarchy": "量化内核支持的硬件 > 注意:", "main_title": "注意:", "section_level": 2, "document_title": "量化内核支持的硬件"}}, {"chunk_id": "5038_7222", "file_name": "chunk_0078_5038_7222.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\02-autoawq.md", "directory_path": "04-quantization", "filename": "02-<PERSON><PERSON><PERSON>", "title_hierarchy": "AutoAWQ > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "AutoAWQ"}}, {"chunk_id": "0637_6690", "file_name": "chunk_0079_0637_6690.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\03-bitsandbytes.md", "directory_path": "04-quantization", "filename": "03-bitsandbytes", "title_hierarchy": "BitsAndBytes > 读取量化 checkpoint", "main_title": "读取量化 checkpoint", "section_level": 2, "document_title": "BitsAndBytes"}}, {"chunk_id": "0637_1165", "file_name": "chunk_0080_0637_1165.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\03-bitsandbytes.md", "directory_path": "04-quantization", "filename": "03-bitsandbytes", "title_hierarchy": "BitsAndBytes > 过程中量化：加载为 4 位量化", "main_title": "过程中量化：加载为 4 位量化", "section_level": 2, "document_title": "BitsAndBytes"}}, {"chunk_id": "1715_7222", "file_name": "chunk_0081_1715_7222.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\04-gguf.md", "directory_path": "04-quantization", "filename": "04-gguf", "title_hierarchy": "GGUF > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "GGUF"}}, {"chunk_id": "3084_6690", "file_name": "chunk_0082_3084_6690.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8 > 依赖", "main_title": "依赖", "section_level": 2, "document_title": "INT8 W8A8"}}, {"chunk_id": "3084_3654", "file_name": "chunk_0083_3084_3654.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8 > 量化过程 > 1. 加载模型 > 2. 准备校准数据 > 3. 应用量化 > 4. 评估准确性", "main_title": "4. 评估准确性", "section_level": 3, "document_title": "INT8 W8A8"}}, {"chunk_id": "3084_7188", "file_name": "chunk_0084_3084_7188.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8 > 最佳实践", "main_title": "最佳实践", "section_level": 2, "document_title": "INT8 W8A8"}}, {"chunk_id": "3084_7106", "file_name": "chunk_0085_3084_7106.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8 > 故障排除和支持", "main_title": "故障排除和支持", "section_level": 2, "document_title": "INT8 W8A8"}}, {"chunk_id": "2968_6690", "file_name": "chunk_0086_2968_6690.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8 > 使用在线动态量化快速入门", "main_title": "使用在线动态量化快速入门", "section_level": 2, "document_title": "FP8 W8A8"}}, {"chunk_id": "2968_4066", "file_name": "chunk_0087_2968_4066.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8 > 安装", "main_title": "安装", "section_level": 2, "document_title": "FP8 W8A8"}}, {"chunk_id": "2968_3654", "file_name": "chunk_0088_2968_3654.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8 > 量化过程 > 1. 加载模型 > 2. 应用量化 > 3. 评估准确性", "main_title": "3. 评估准确性", "section_level": 3, "document_title": "FP8 W8A8"}}, {"chunk_id": "2968_7106", "file_name": "chunk_0089_2968_7106.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8 > 故障排除和支持", "main_title": "故障排除和支持", "section_level": 2, "document_title": "FP8 W8A8"}}, {"chunk_id": "2968_9648", "file_name": "chunk_0090_2968_9648.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8 > 已弃用的流程", "main_title": "已弃用的流程", "section_level": 2, "document_title": "FP8 W8A8"}}, {"chunk_id": "2968_5344", "file_name": "chunk_0091_2968_5344.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8 > 使用静态激活缩放因子进行离线量化", "main_title": "使用静态激活缩放因子进行离线量化", "section_level": 2, "document_title": "FP8 W8A8"}}, {"chunk_id": "6874_7222", "file_name": "chunk_0092_6874_7222.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\07-fp8-e5m2-kv-cache.md", "directory_path": "04-quantization", "filename": "07-fp8-e5m2-kv-cache", "title_hierarchy": "FP8 E5M2 KV 缓存 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "FP8 E5M2 KV 缓存"}}, {"chunk_id": "7311_7222", "file_name": "chunk_0093_7311_7222.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\08-fp8-e4m3-kv-cache.md", "directory_path": "04-quantization", "filename": "08-fp8-e4m3-kv-cache", "title_hierarchy": "FP8 E4M3 KV 缓存 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "FP8 E4M3 KV 缓存"}}, {"chunk_id": "4861_0549", "file_name": "chunk_0094_4861_0549.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\01-introduction-apc.md", "directory_path": "05-automatic-prefix-caching", "filename": "01-introduction-apc", "title_hierarchy": "简介 > 什么是自动前缀缓存 (Automatic Prefix Caching)", "main_title": "什么是自动前缀缓存 (Automatic Prefix Caching)", "section_level": 2, "document_title": "简介"}}, {"chunk_id": "4861_1694", "file_name": "chunk_0095_4861_1694.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\01-introduction-apc.md", "directory_path": "05-automatic-prefix-caching", "filename": "01-introduction-apc", "title_hierarchy": "简介 > 在 vLLM 中启用 APC", "main_title": "在 vLLM 中启用 APC", "section_level": 2, "document_title": "简介"}}, {"chunk_id": "4861_8662", "file_name": "chunk_0096_4861_8662.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\01-introduction-apc.md", "directory_path": "05-automatic-prefix-caching", "filename": "01-introduction-apc", "title_hierarchy": "简介 > 工作负载示例", "main_title": "工作负载示例", "section_level": 2, "document_title": "简介"}}, {"chunk_id": "4861_0970", "file_name": "chunk_0097_4861_0970.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\01-introduction-apc.md", "directory_path": "05-automatic-prefix-caching", "filename": "01-introduction-apc", "title_hierarchy": "简介 > 局限性", "main_title": "局限性", "section_level": 2, "document_title": "简介"}}, {"chunk_id": "3593_6690", "file_name": "chunk_0098_3593_6690.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\02-implementation.md", "directory_path": "05-automatic-prefix-caching", "filename": "02-implementation", "title_hierarchy": "实现 (Implementation) > 通用缓存策略", "main_title": "通用缓存策略", "section_level": 1, "document_title": "实现 (Implementation)"}}, {"chunk_id": "1422_6690", "file_name": "chunk_0099_1422_6690.txt", "metadata": {"source_file": "version-0.8.x\\06-performance-benchmarks\\06-benchmark-suites-of-vllm.md", "directory_path": "06-performance-benchmarks", "filename": "06-benchmark-suites-of-vllm", "title_hierarchy": "vLLM 基准套件 > 触发基准测试", "main_title": "触发基准测试", "section_level": 2, "document_title": "vLLM 基准套件"}}, {"chunk_id": "2017_6690", "file_name": "chunk_0100_2017_6690.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\01-sampling-parameters.md", "directory_path": "07-developer-documentation", "filename": "01-sampling-parameters", "title_hierarchy": "采样参数 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "采样参数"}}, {"chunk_id": "9280_6690", "file_name": "chunk_0101_9280_6690.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > 输入", "main_title": "输入", "section_level": 2, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "9280_3878", "file_name": "chunk_0102_9280_3878.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > 概念", "main_title": "概念", "section_level": 2, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "9280_0291", "file_name": "chunk_0103_9280_0291.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > 询问 (Query)", "main_title": "询问 (Query)", "section_level": 2, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "9280_3519", "file_name": "chunk_0104_9280_3519.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > 键 (Key)", "main_title": "键 (Key)", "section_level": 2, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "9280_1447", "file_name": "chunk_0105_9280_1447.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > QK", "main_title": "QK", "section_level": 2, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "9280_2064", "file_name": "chunk_0106_9280_2064.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > Softmax > `qk_max` 和 `logits` > `exp_sum`", "main_title": "`exp_sum`", "section_level": 3, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "9280_5763", "file_name": "chunk_0107_9280_5763.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > 值", "main_title": "值", "section_level": 2, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "9280_3829", "file_name": "chunk_0108_9280_3829.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > LV", "main_title": "LV", "section_level": 2, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "9280_4388", "file_name": "chunk_0109_9280_4388.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > 输出", "main_title": "输出", "section_level": 2, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "9624_7222", "file_name": "chunk_0110_9624_7222.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\07-dockerfile.md", "directory_path": "07-developer-documentation", "filename": "07-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "Dockerfile > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Dockerfile"}}, {"chunk_id": "9914_6690", "file_name": "chunk_0111_9914_6690.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\08-profiling-vllm.md", "directory_path": "07-developer-documentation", "filename": "08-profiling-vllm", "title_hierarchy": "分析 vLLM > 命令和使用示例： > 离线推理 > OpenAI 服务器：", "main_title": "OpenAI 服务器：", "section_level": 3, "document_title": "分析 vLLM"}}, {"chunk_id": "3278_1171", "file_name": "chunk_0112_3278_1171.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > A", "main_title": "A", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_2324", "file_name": "chunk_0113_3278_2324.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > B", "main_title": "B", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_0003", "file_name": "chunk_0114_3278_0003.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > C", "main_title": "C", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_2835", "file_name": "chunk_0115_3278_2835.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > D", "main_title": "D", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_8970", "file_name": "chunk_0116_3278_8970.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > E", "main_title": "E", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_4469", "file_name": "chunk_0117_3278_4469.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > F", "main_title": "F", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_9539", "file_name": "chunk_0118_3278_9539.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > G", "main_title": "G", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_7631", "file_name": "chunk_0119_3278_7631.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > H", "main_title": "H", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_5458", "file_name": "chunk_0120_3278_5458.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > I", "main_title": "I", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_0001", "file_name": "chunk_0121_3278_0001.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > L", "main_title": "L", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_9981", "file_name": "chunk_0122_3278_9981.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > M", "main_title": "M", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_8999", "file_name": "chunk_0123_3278_8999.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > N", "main_title": "N", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_9177", "file_name": "chunk_0124_3278_9177.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > P", "main_title": "P", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_4379", "file_name": "chunk_0125_3278_4379.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > R", "main_title": "R", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_5815", "file_name": "chunk_0126_3278_5815.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > S", "main_title": "S", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_8975", "file_name": "chunk_0127_3278_8975.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > T", "main_title": "T", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_3746", "file_name": "chunk_0128_3278_3746.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > U", "main_title": "U", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3278_8871", "file_name": "chunk_0129_3278_8871.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > V", "main_title": "V", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "1371_7222", "file_name": "chunk_0130_1371_7222.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\02-python-module-index.md", "directory_path": "08-indices-and-tables", "filename": "02-python-module-index", "title_hierarchy": "Python 模块索引 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Python 模块索引"}}, {"chunk_id": "8096_7222", "file_name": "chunk_0131_8096_7222.txt", "metadata": {"source_file": "version-0.8.x\\09-community\\01-vllm-meetups.md", "directory_path": "09-community", "filename": "01-vllm-meetups", "title_hierarchy": "vLLM 交流会 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "vLLM 交流会"}}, {"chunk_id": "1974_7222", "file_name": "chunk_0132_1974_7222.txt", "metadata": {"source_file": "version-0.8.x\\09-community\\02-sponsors.md", "directory_path": "09-community", "filename": "02-sponsors", "title_hierarchy": "赞助商 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "赞助商"}}, {"chunk_id": "3961_6690", "file_name": "chunk_0133_3961_6690.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 目录", "main_title": "目录", "section_level": 2, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "3961_4594", "file_name": "chunk_0134_3961_4594.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 一、安装 vLLM", "main_title": "一、安装 vLLM", "section_level": 2, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "3961_4326", "file_name": "chunk_0135_3961_4326.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 二、开始使用 > 2.1 模型准备 > 2.2 离线推理", "main_title": "2.2 离线推理", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "3961_4771", "file_name": "chunk_0136_3961_4771.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 三、启动 vLLM 服务器 > 3.1 主要参数设置 > 3.2 启动命令行", "main_title": "3.2 启动命令行", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "3961_2221", "file_name": "chunk_0137_3961_2221.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 四、发出请求 > 4.1 使用 OpenAI 客户端 > 4.2 使用 Curl 命令请求", "main_title": "4.2 使用 Curl 命令请求", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "4364_6690", "file_name": "chunk_0138_4364_6690.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 目录", "main_title": "目录", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "4364_4729", "file_name": "chunk_0139_4364_4729.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 1. 安装 vLLM", "main_title": "1. 安装 vLLM", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "4364_8937", "file_name": "chunk_0140_4364_8937.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 2. 使用 vLLM 加载 Qwen 量化模型", "main_title": "2. 使用 vLLM 加载 Qwen 量化模型", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "4364_8385", "file_name": "chunk_0141_4364_8385.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 3. 加载测试数据", "main_title": "3. 加载测试数据", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "4364_8817", "file_name": "chunk_0142_4364_8817.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 4. 提示工程", "main_title": "4. 提示工程", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "4364_4481", "file_name": "chunk_0143_4364_4481.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 5. Infer 测试", "main_title": "5. <PERSON><PERSON> 测试", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "4364_9976", "file_name": "chunk_0144_4364_9976.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 6. 提取推理概率", "main_title": "6. 提取推理概率", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "4364_7259", "file_name": "chunk_0145_4364_7259.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 7. 创建提交 CSV", "main_title": "7. 创建提交 CSV", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "4364_0989", "file_name": "chunk_0146_4364_0989.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 8. 计算 CV 分数", "main_title": "8. 计算 CV 分数", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "3768_6690", "file_name": "chunk_0147_3768_6690.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 目录", "main_title": "目录", "section_level": 2, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "3768_9499", "file_name": "chunk_0148_3768_9499.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 1. 导入相关的库", "main_title": "1. 导入相关的库", "section_level": 2, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "3768_2929", "file_name": "chunk_0149_3768_2929.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 2. 加载数据", "main_title": "2. 加载数据", "section_level": 2, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "3768_2356", "file_name": "chunk_0150_3768_2356.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 3. 使用 vLLM 启动 Qwen2.5-3B-Instruct-AWQ", "main_title": "3. 使用 vLLM 启动 Qwen2.5-3B-Instruct-AWQ", "section_level": 2, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "3768_7082", "file_name": "chunk_0151_3768_7082.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 4. 后处理数据", "main_title": "4. 后处理数据", "section_level": 2, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "3768_5672", "file_name": "chunk_0152_3768_5672.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 5. 辅助函数 > 在给定 subject 和 construct 的情况下获取最相似的 question_ids' > 获取每个问题的聊天对话", "main_title": "获取每个问题的聊天对话", "section_level": 3, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "3768_2299", "file_name": "chunk_0153_3768_2299.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 6. 使用 llm.chat", "main_title": "6. 使用 llm.chat", "section_level": 2, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "3768_2032", "file_name": "chunk_0154_3768_2032.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 7. 找到最相似的误解 > 合并每个问题的每个生成输出的排名", "main_title": "合并每个问题的每个生成输出的排名", "section_level": 3, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "3768_0181", "file_name": "chunk_0155_3768_0181.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 8. 提交", "main_title": "8. 提交", "section_level": 2, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "1334_6690", "file_name": "chunk_0156_1334_6690.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\04-vllm-langchain-tutorial.md", "directory_path": "10-tutorials", "filename": "04-vllm-langchain-tutorial", "title_hierarchy": "将 LangChain 与 vLLM 结合使用：完整教程 > 目录", "main_title": "目录", "section_level": 2, "document_title": "将 LangChain 与 vLLM 结合使用：完整教程"}}, {"chunk_id": "1334_2300", "file_name": "chunk_0157_1334_2300.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\04-vllm-langchain-tutorial.md", "directory_path": "10-tutorials", "filename": "04-vllm-langchain-tutorial", "title_hierarchy": "将 LangChain 与 vLLM 结合使用：完整教程 > 1. 安装和设置 vLLM > Docker 安装", "main_title": "Docker 安装", "section_level": 3, "document_title": "将 LangChain 与 vLLM 结合使用：完整教程"}}, {"chunk_id": "1334_5638", "file_name": "chunk_0158_1334_5638.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\04-vllm-langchain-tutorial.md", "directory_path": "10-tutorials", "filename": "04-vllm-langchain-tutorial", "title_hierarchy": "将 LangChain 与 vLLM 结合使用：完整教程 > 2. 配置 vLLM 以与 LangChain 配合使用", "main_title": "2. 配置 vLLM 以与 LangChain 配合使用", "section_level": 2, "document_title": "将 LangChain 与 vLLM 结合使用：完整教程"}}, {"chunk_id": "1334_1212", "file_name": "chunk_0159_1334_1212.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\04-vllm-langchain-tutorial.md", "directory_path": "10-tutorials", "filename": "04-vllm-langchain-tutorial", "title_hierarchy": "将 LangChain 与 vLLM 结合使用：完整教程 > 3. 使用 LangChain 和 vLLM 创建链", "main_title": "3. 使用 <PERSON><PERSON><PERSON><PERSON> 和 vLLM 创建链", "section_level": 2, "document_title": "将 LangChain 与 vLLM 结合使用：完整教程"}}, {"chunk_id": "1334_0405", "file_name": "chunk_0160_1334_0405.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\04-vllm-langchain-tutorial.md", "directory_path": "10-tutorials", "filename": "04-vllm-langchain-tutorial", "title_hierarchy": "将 LangChain 与 vLLM 结合使用：完整教程 > 4. 利用多 GPU 推理进行扩展", "main_title": "4. 利用多 GPU 推理进行扩展", "section_level": 2, "document_title": "将 LangChain 与 vLLM 结合使用：完整教程"}}, {"chunk_id": "1334_9606", "file_name": "chunk_0161_1334_9606.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\04-vllm-langchain-tutorial.md", "directory_path": "10-tutorials", "filename": "04-vllm-langchain-tutorial", "title_hierarchy": "将 LangChain 与 vLLM 结合使用：完整教程 > 5. 利用量化提高效率", "main_title": "5. 利用量化提高效率", "section_level": 2, "document_title": "将 LangChain 与 vLLM 结合使用：完整教程"}}, {"chunk_id": "1334_1363", "file_name": "chunk_0162_1334_1363.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\04-vllm-langchain-tutorial.md", "directory_path": "10-tutorials", "filename": "04-vllm-langchain-tutorial", "title_hierarchy": "将 LangChain 与 vLLM 结合使用：完整教程 > 6. 结论", "main_title": "6. 结论", "section_level": 2, "document_title": "将 LangChain 与 vLLM 结合使用：完整教程"}}, {"chunk_id": "3084_6569", "file_name": "chunk_0163_3084_6569.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\10-examples.md", "directory_path": "01-getting-started > 10-examples", "filename": "10-examples", "title_hierarchy": "示例 > 脚本", "main_title": "脚本", "section_level": 2, "document_title": "示例"}}, {"chunk_id": "9992_7222", "file_name": "chunk_0164_9992_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\01-api_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "01-api_client", "title_hierarchy": "API 客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "API 客户端"}}, {"chunk_id": "6788_7222", "file_name": "chunk_0165_6788_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\02-aqlm_example.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "02-aqlm_example", "title_hierarchy": "Aqlm 示例 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Aqlm 示例"}}, {"chunk_id": "1958_7222", "file_name": "chunk_0166_1958_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\03-cpu_offload.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "03-cpu_offload", "title_hierarchy": "CPU 离线处理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "CPU 离线处理"}}, {"chunk_id": "1429_7222", "file_name": "chunk_0167_1429_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\04-gguf_inference.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "04-gguf_inference", "title_hierarchy": "GGUF 推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "GGUF 推理"}}, {"chunk_id": "8632_7222", "file_name": "chunk_0168_8632_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\05-gradio_openai_chatbot_webserver.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "05-gradio_openai_chatbot_webserver", "title_hierarchy": "Gradio OpenAI 聊天机器人 Web 服务器 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Gradio OpenAI 聊天机器人 Web 服务器"}}, {"chunk_id": "1927_7222", "file_name": "chunk_0169_1927_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\06-gradio_webserver.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "06-gradio_webserver", "title_hierarchy": "Gradio Web 服务器 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Gradio Web 服务器"}}, {"chunk_id": "2820_7222", "file_name": "chunk_0170_2820_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\07-llm_engine_example.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "07-llm_engine_example", "title_hierarchy": "LLM 引擎示例 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "LLM 引擎示例"}}, {"chunk_id": "2405_7222", "file_name": "chunk_0171_2405_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\08-lora_with_quantization_inference.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "08-lora_with_quantization_inference", "title_hierarchy": "带量化的 LoRA 推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "带量化的 LoRA 推理"}}, {"chunk_id": "2245_7222", "file_name": "chunk_0172_2245_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\09-multilora_inference.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "09-multilora_inference", "title_hierarchy": "MultiLoRA 推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "MultiLoRA 推理"}}, {"chunk_id": "4311_7222", "file_name": "chunk_0173_4311_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\10-offline_chat_with_tools.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "10-offline_chat_with_tools", "title_hierarchy": "离线聊天工具 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线聊天工具"}}, {"chunk_id": "6451_7222", "file_name": "chunk_0174_6451_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\11-offline_inference.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "11-offline_inference", "title_hierarchy": "API Client > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "API Client"}}, {"chunk_id": "8406_7222", "file_name": "chunk_0175_8406_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\12-offline_inference_arctic.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "12-offline_inference_arctic", "title_hierarchy": "离线推理 Arctic > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 Arctic"}}, {"chunk_id": "2842_7222", "file_name": "chunk_0176_2842_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\13-offline_inference_audio_language.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "13-offline_inference_audio_language", "title_hierarchy": "离线推理音频语言 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理音频语言"}}, {"chunk_id": "3720_7222", "file_name": "chunk_0177_3720_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\14-offline_inference_chat.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "14-offline_inference_chat", "title_hierarchy": "离线推理聊天 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理聊天"}}, {"chunk_id": "2220_7222", "file_name": "chunk_0178_2220_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\15-offline_inference_distributed.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "15-offline_inference_distributed", "title_hierarchy": "离线推理分布式 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理分布式"}}, {"chunk_id": "3372_7222", "file_name": "chunk_0179_3372_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\16-offline_inference_embedding.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "16-offline_inference_embedding", "title_hierarchy": "离线推理嵌入 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理嵌入"}}, {"chunk_id": "6168_7222", "file_name": "chunk_0180_6168_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\17-offline_inference_encoder_decoder.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "17-offline_inference_encoder_decoder", "title_hierarchy": "离线推理编码器-解码器 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理编码器-解码器"}}, {"chunk_id": "6965_7222", "file_name": "chunk_0181_6965_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\18-offline_inference_mlpspeculator.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "18-offline_inference_mlpspeculator", "title_hierarchy": "离线推理 MlpSpeculator > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 MlpSpeculator"}}, {"chunk_id": "6547_7222", "file_name": "chunk_0182_6547_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\19-offline_inference_neuron.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "19-offline_inference_neuron", "title_hierarchy": "离线推理 Neuron > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 Neuron"}}, {"chunk_id": "7014_7222", "file_name": "chunk_0183_7014_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\20-offline_inference_neuron_int8_quantization.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "20-offline_inference_neuron_int8_quantization", "title_hierarchy": "离线推理 Neuron Int8 量化 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 Neuron Int8 量化"}}, {"chunk_id": "5877_7222", "file_name": "chunk_0184_5877_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\21-offline_inference_pixtral.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "21-offline_inference_pixtral", "title_hierarchy": "使用 Pixtral 进行离线推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 Pixtral 进行离线推理"}}, {"chunk_id": "9316_7222", "file_name": "chunk_0185_9316_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\22-offline_inference_tpu.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "22-offline_inference_tpu", "title_hierarchy": "离线推理 TPU > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 TPU"}}, {"chunk_id": "2808_7222", "file_name": "chunk_0186_2808_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\23-offline_inference_vision_language.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "23-offline_inference_vision_language", "title_hierarchy": "离线推理视觉语言 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理视觉语言"}}, {"chunk_id": "7731_7222", "file_name": "chunk_0187_7731_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\24-offline_inference_vision_language_multi_image.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "24-offline_inference_vision_language_multi_image", "title_hierarchy": "离线推理视觉语言多图像 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理视觉语言多图像"}}, {"chunk_id": "1431_7222", "file_name": "chunk_0188_1431_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\25-offline_inference_with_prefix.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "25-offline_inference_with_prefix", "title_hierarchy": "带前缀的离线推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "带前缀的离线推理"}}, {"chunk_id": "3658_7222", "file_name": "chunk_0189_3658_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\26-offline_inference_with_profiler.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "26-offline_inference_with_profiler", "title_hierarchy": "启用分析器的离线推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "启用分析器的离线推理"}}, {"chunk_id": "4336_7222", "file_name": "chunk_0190_4336_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\27-openai_audio_api_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "27-openai_audio_api_client", "title_hierarchy": "OpenAI 音频 API 客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 音频 API 客户端"}}, {"chunk_id": "6368_7222", "file_name": "chunk_0191_6368_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\28-openai_chat_completion_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "28-openai_chat_completion_client", "title_hierarchy": "OpenAI 聊天补全客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 聊天补全客户端"}}, {"chunk_id": "2409_7222", "file_name": "chunk_0192_2409_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\29-openai_chat_completion_client_with_tools.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "29-openai_chat_completion_client_with_tools", "title_hierarchy": "带有工具的 OpenAI 聊天补全客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "带有工具的 OpenAI 聊天补全客户端"}}, {"chunk_id": "0937_7222", "file_name": "chunk_0193_0937_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\30-openai_completion_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "30-openai_completion_client", "title_hierarchy": "OpenAI 补全客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 补全客户端"}}, {"chunk_id": "9744_7222", "file_name": "chunk_0194_9744_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\31-openai_embedding_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "31-openai_embedding_client", "title_hierarchy": "OpenAI 嵌入客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 嵌入客户端"}}, {"chunk_id": "0462_7222", "file_name": "chunk_0195_0462_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\32-openai_vision_api_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "32-openai_vision_api_client", "title_hierarchy": "OpenAI 视觉 API 客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 视觉 API 客户端"}}, {"chunk_id": "6234_7222", "file_name": "chunk_0196_6234_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\33-save_sharded_state.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "33-save_sharded_state", "title_hierarchy": "保存分片状态 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "保存分片状态"}}, {"chunk_id": "2641_7222", "file_name": "chunk_0197_2641_7222.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\34-tensorize_vllm_model.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "34-tensorize_vllm_model", "title_hierarchy": "Tensorize vLLM 模型 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Tensorize vLLM 模型"}}, {"chunk_id": "2083_6690", "file_name": "chunk_0198_2083_6690.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\01-deploying&scaling-up-with-skypilot.md", "directory_path": "02-serving > 07-integrations", "filename": "01-deploying&scaling-up-with-skypilot", "title_hierarchy": "使用 SkyPilot 进行部署和扩充 > 依赖", "main_title": "依赖", "section_level": 2, "document_title": "使用 SkyPilot 进行部署和扩充"}}, {"chunk_id": "2083_8893", "file_name": "chunk_0199_2083_8893.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\01-deploying&scaling-up-with-skypilot.md", "directory_path": "02-serving > 07-integrations", "filename": "01-deploying&scaling-up-with-skypilot", "title_hierarchy": "使用 SkyPilot 进行部署和扩充 > 在单个实例上运行", "main_title": "在单个实例上运行", "section_level": 2, "document_title": "使用 SkyPilot 进行部署和扩充"}}, {"chunk_id": "2083_7365", "file_name": "chunk_0200_2083_7365.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\01-deploying&scaling-up-with-skypilot.md", "directory_path": "02-serving > 07-integrations", "filename": "01-deploying&scaling-up-with-skypilot", "title_hierarchy": "使用 SkyPilot 进行部署和扩充 > 扩充到多个副本 > **可选**: 将 GUI 连接到端点", "main_title": "**可选**: 将 GUI 连接到端点", "section_level": 3, "document_title": "使用 SkyPilot 进行部署和扩充"}}, {"chunk_id": "3252_7222", "file_name": "chunk_0201_3252_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\02-deploying-with-kserve.md", "directory_path": "02-serving > 07-integrations", "filename": "02-deploying-with-kserve", "title_hierarchy": "使用 KServe 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 KServe 进行部署"}}, {"chunk_id": "9007_7222", "file_name": "chunk_0202_9007_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\03-deploying-with-nvidia-triton.md", "directory_path": "02-serving > 07-integrations", "filename": "03-deploying-with-nvidia-triton", "title_hierarchy": "使用 NVIDIA Triton 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 NVIDIA Triton 进行部署"}}, {"chunk_id": "5377_7222", "file_name": "chunk_0203_5377_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\04-deploying-with-bentoml.md", "directory_path": "02-serving > 07-integrations", "filename": "04-deploying-with-bentoml", "title_hierarchy": "使用 BentoML 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 BentoML 进行部署"}}, {"chunk_id": "7652_7222", "file_name": "chunk_0204_7652_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\05-deploying-with-cerebrium.md", "directory_path": "02-serving > 07-integrations", "filename": "05-deploying-with-cerebrium", "title_hierarchy": "使用 Cerebrium 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 Cerebrium 进行部署"}}, {"chunk_id": "8918_7222", "file_name": "chunk_0205_8918_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\06-deploying-with-lws.md", "directory_path": "02-serving > 07-integrations", "filename": "06-deploying-with-lws", "title_hierarchy": "使用 LWS 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 LWS 进行部署"}}, {"chunk_id": "5345_7222", "file_name": "chunk_0206_5345_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\07-deploying-with-dstack.md", "directory_path": "02-serving > 07-integrations", "filename": "07-deploying-with-dstack", "title_hierarchy": "使用 dstack 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 dstack 进行部署"}}, {"chunk_id": "1089_7222", "file_name": "chunk_0207_1089_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\08-serving-with-langchain.md", "directory_path": "02-serving > 07-integrations", "filename": "08-serving-with-langchain", "title_hierarchy": "使用 Langchain 提供服务 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 Langchain 提供服务"}}, {"chunk_id": "9170_7222", "file_name": "chunk_0208_9170_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\09-serving-with-llama_index.md", "directory_path": "02-serving > 07-integrations", "filename": "09-serving-with-llama_index", "title_hierarchy": "使用 llama_index 服务 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 llama_index 服务"}}, {"chunk_id": "1096_7222", "file_name": "chunk_0209_1096_7222.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\readme.md", "directory_path": "02-serving > 07-integrations", "filename": "readme", "title_hierarchy": "整合 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "整合"}}, {"chunk_id": "9895_6690", "file_name": "chunk_0210_9895_6690.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "9895_9092", "file_name": "chunk_0211_9895_9092.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "9895_6649", "file_name": "chunk_0212_9895_6649.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 返回", "main_title": "返回", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "9895_9092", "file_name": "chunk_0213_9895_9092.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "9895_6649", "file_name": "chunk_0214_9895_6649.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 返回", "main_title": "返回", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "9895_9092", "file_name": "chunk_0215_9895_9092.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "9895_6649", "file_name": "chunk_0216_9895_6649.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 返回", "main_title": "返回", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "0912_0442", "file_name": "chunk_0217_0912_0442.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\02-llm-inputs.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "02-llm-inputs", "title_hierarchy": "LLM Inputs > vllm.inputs.PromptType", "main_title": "vllm.inputs.PromptType", "section_level": 2, "document_title": "LLM Inputs"}}, {"chunk_id": "0912_9411", "file_name": "chunk_0218_0912_9411.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\02-llm-inputs.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "02-llm-inputs", "title_hierarchy": "LLM Inputs > class vllm.inputs.TextPrompt", "main_title": "class vllm.inputs.TextPrompt", "section_level": 1, "document_title": "LLM Inputs"}}, {"chunk_id": "0912_3468", "file_name": "chunk_0219_0912_3468.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\02-llm-inputs.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "02-llm-inputs", "title_hierarchy": "LLM Inputs > class vllm.inputs.TokensPrompt", "main_title": "class vllm.inputs.TokensPrompt", "section_level": 1, "document_title": "LLM Inputs"}}, {"chunk_id": "4002_7222", "file_name": "chunk_0220_4002_7222.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\readme.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "readme", "title_hierarchy": "离线推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理"}}, {"chunk_id": "8122_6690", "file_name": "chunk_0221_8122_6690.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine > Example", "main_title": "Example", "section_level": 2, "document_title": "LLMEngine"}}, {"chunk_id": "8122_4513", "file_name": "chunk_0222_8122_4513.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine > 案例", "main_title": "案例", "section_level": 2, "document_title": "LLMEngine"}}, {"chunk_id": "8122_3713", "file_name": "chunk_0223_8122_3713.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine > 详细信息：", "main_title": "详细信息：", "section_level": 2, "document_title": "LLMEngine"}}, {"chunk_id": "8122_0888", "file_name": "chunk_0224_8122_0888.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine > 示例", "main_title": "示例", "section_level": 2, "document_title": "LLMEngine"}}, {"chunk_id": "0954_7222", "file_name": "chunk_0225_0954_7222.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\02-asyncllmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "02-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "AsyncLLMEngine > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "AsyncLLMEngine"}}, {"chunk_id": "3991_7222", "file_name": "chunk_0226_3991_7222.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\readme.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "readme", "title_hierarchy": "vLLM Engine > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "vLLM Engine"}}, {"chunk_id": "0197_6690", "file_name": "chunk_0227_0197_6690.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\05-input-processing\\01-model_inputs_index.md", "directory_path": "07-developer-documentation > 05-input-processing", "filename": "01-model_inputs_index", "title_hierarchy": "输入处理 > 指南", "main_title": "指南", "section_level": 2, "document_title": "输入处理"}}, {"chunk_id": "0197_2366", "file_name": "chunk_0228_0197_2366.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\05-input-processing\\01-model_inputs_index.md", "directory_path": "07-developer-documentation > 05-input-processing", "filename": "01-model_inputs_index", "title_hierarchy": "输入处理 > 模块内容 > LLM 引擎输入 > 注册", "main_title": "注册", "section_level": 3, "document_title": "输入处理"}}, {"chunk_id": "6150_7222", "file_name": "chunk_0229_6150_7222.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\05-input-processing\\02-input-processing-pipeline.md", "directory_path": "07-developer-documentation > 05-input-processing", "filename": "02-input-processing-pipeline", "title_hierarchy": "输入处理管道 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "输入处理管道"}}, {"chunk_id": "5856_7222", "file_name": "chunk_0230_5856_7222.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\06-multi-modality\\01-adding-a-multimodal-plugin.md", "directory_path": "07-developer-documentation > 06-multi-modality", "filename": "01-adding-a-multimodal-plugin", "title_hierarchy": "添加多模态插件 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "添加多模态插件"}}, {"chunk_id": "1156_6690", "file_name": "chunk_0231_1156_6690.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\06-multi-modality\\readme.md", "directory_path": "07-developer-documentation > 06-multi-modality", "filename": "readme", "title_hierarchy": "多模态 > 指南", "main_title": "指南", "section_level": 2, "document_title": "多模态"}}, {"chunk_id": "1156_2366", "file_name": "chunk_0232_1156_2366.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\06-multi-modality\\readme.md", "directory_path": "07-developer-documentation > 06-multi-modality", "filename": "readme", "title_hierarchy": "多模态 > 模块内容 > 注册 > 基础类 (Base Classes) > 图像类", "main_title": "图像类", "section_level": 3, "document_title": "多模态"}}]