# 文档路径: 01-getting-started > 05-installation-with-neuron > 使用 Neuron 安装 > 从源代码构建 > 步骤 2. 安装 Transformers-neuronx 及其依赖

## 从源代码构建

以下说明适用于 Neuron SDK 2.16 及更高版本。


### 步骤 2. 安装 Transformers-neuronx 及其依赖

[transformers-neuronx](https://github.com/aws-neuron/transformers-neuronx) 将作为后端来支持在 trn1/inf2 实例上进行推理。请按照以下步骤安装 Transformer-neuronx 包及其依赖。

```plain
# Install Python venv
# 安装 Python venv

sudo apt-get install -y python3.10-venv g++

# Create Python venv
# 创建 Python venv

python3.10 -m venv aws_neuron_venv_pytorch

# Activate Python venv
# 激活 Python venv

source aws_neuron_venv_pytorch/bin/activate

# Install Jupyter notebook kernel
# 安装 Jupyter Notebook 内核

pip install ipykernel
python3.10 -m ipykernel install --user --name aws_neuron_venv_pytorch --display-name "Python (torch-neuronx)"
pip install jupyter notebook
pip install environment_kernels

# Set pip repository pointing to the Neuron repository
# 设置 pip 存储库指向 Neuron 存储库

python -m pip config set global.extra-index-url https://pip.repos.neuron.amazonaws.com

# Install wget, awscli
# 安装 wget、awscli

python -m pip install wget
python -m pip install awscli

# Update Neuron Compiler and Framework
# 更新 Neuron 编译器和框架

python -m pip install --upgrade neuronx-cc==2.* --pre torch-neuronx==2.1.* torchvision transformers-neuronx
```
