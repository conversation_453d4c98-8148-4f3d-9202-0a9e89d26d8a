"""
预测推理模块

负责加载训练好的模型，对新的应用服务报错进行根因分析预测。
支持单条和批量预测，提供置信度评估和解释性分析。
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any, Union
import logging
import json
import os
from .model_trainer import HybridErrorClassifier
from .data_processor import DataProcessor
from transformers import AutoTokenizer
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Predictor:
    """
    预测器
    
    负责模型推理、结果解释和置信度评估
    """
    
    def __init__(self, 
                 model_path: str,
                 device: str = None,
                 confidence_threshold: float = 0.5):
        """
        初始化预测器
        
        Args:
            model_path: 模型路径
            device: 设备
            confidence_threshold: 置信度阈值
        """
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        
        # 设备配置
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        # 初始化组件
        self.model = None
        self.tokenizer = None
        self.data_processor = None
        self.config = None
        self.label_mapping = {}
        
        # 加载模型
        self.load_model()
        
        logger.info(f"预测器初始化完成，使用设备: {self.device}")
    
    def load_model(self):
        """加载模型和相关组件"""
        try:
            # 加载配置
            config_path = os.path.join(self.model_path, "config.json")
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            # 加载模型
            self.model = HybridErrorClassifier(
                model_name=self.config['model_name'],
                num_classes=self.config['num_classes'],
                feature_dim=self.config['feature_dim']
            ).to(self.device)
            
            model_weights_path = os.path.join(self.model_path, "model.pth")
            self.model.load_state_dict(torch.load(model_weights_path, map_location=self.device))
            self.model.eval()
            
            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            
            # 初始化数据处理器
            self.data_processor = DataProcessor(
                tokenizer_name=self.config['model_name'],
                max_length=self.config['max_length']
            )
            
            logger.info(f"成功加载模型: {self.config['num_classes']} 类分类器")
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def set_label_mapping(self, label_mapping: Dict[int, str]):
        """
        设置标签映射
        
        Args:
            label_mapping: {编码: 标签} 映射字典
        """
        self.label_mapping = label_mapping
        logger.info(f"设置标签映射: {len(label_mapping)} 个类别")
    
    def preprocess_single_text(self, text: str) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        预处理单条文本
        
        Args:
            text: 输入文本
            
        Returns:
            (input_ids, attention_mask, features)
        """
        # 文本清洗
        cleaned_text = self.data_processor.clean_text(text)
        
        # 提取特征
        error_features = self.data_processor.extract_error_features(cleaned_text)
        feature_vector = np.array(list(error_features.values()))
        
        # 文本编码
        encoding = self.tokenizer(
            cleaned_text,
            truncation=True,
            padding='max_length',
            max_length=self.config['max_length'],
            return_tensors='pt'
        )
        
        input_ids = encoding['input_ids'].to(self.device)
        attention_mask = encoding['attention_mask'].to(self.device)
        features = torch.FloatTensor(feature_vector).unsqueeze(0).to(self.device)
        
        return input_ids, attention_mask, features
    
    def predict_single(self, text: str, return_probabilities: bool = True) -> Dict[str, Any]:
        """
        预测单条文本
        
        Args:
            text: 输入文本
            return_probabilities: 是否返回概率分布
            
        Returns:
            预测结果字典
        """
        start_time = time.time()
        
        try:
            # 预处理
            input_ids, attention_mask, features = self.preprocess_single_text(text)
            
            # 模型推理
            with torch.no_grad():
                logits = self.model(input_ids, attention_mask, features)
                probabilities = F.softmax(logits, dim=1)
                predicted_class = torch.argmax(logits, dim=1).item()
                confidence = probabilities[0][predicted_class].item()
            
            # 构建结果
            result = {
                'predicted_class': predicted_class,
                'predicted_label': self.label_mapping.get(predicted_class, f"类别_{predicted_class}"),
                'confidence': confidence,
                'is_confident': confidence >= self.confidence_threshold,
                'inference_time': time.time() - start_time
            }
            
            if return_probabilities:
                # 获取所有类别的概率
                all_probs = probabilities[0].cpu().numpy()
                prob_dict = {}
                for i, prob in enumerate(all_probs):
                    label = self.label_mapping.get(i, f"类别_{i}")
                    prob_dict[label] = float(prob)
                
                # 按概率排序
                sorted_probs = sorted(prob_dict.items(), key=lambda x: x[1], reverse=True)
                result['all_probabilities'] = prob_dict
                result['top_3_predictions'] = sorted_probs[:3]
            
            return result
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return {
                'error': str(e),
                'predicted_class': -1,
                'predicted_label': "预测失败",
                'confidence': 0.0,
                'is_confident': False,
                'inference_time': time.time() - start_time
            }
    
    def predict_batch(self, 
                     texts: List[str], 
                     batch_size: int = 32,
                     return_probabilities: bool = False) -> List[Dict[str, Any]]:
        """
        批量预测
        
        Args:
            texts: 文本列表
            batch_size: 批次大小
            return_probabilities: 是否返回概率分布
            
        Returns:
            预测结果列表
        """
        logger.info(f"开始批量预测 {len(texts)} 条文本...")
        start_time = time.time()
        
        results = []
        
        # 分批处理
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_results = []
            
            for text in batch_texts:
                result = self.predict_single(text, return_probabilities)
                batch_results.append(result)
            
            results.extend(batch_results)
            
            if (i // batch_size + 1) % 10 == 0:
                logger.info(f"已处理 {i + len(batch_texts)} / {len(texts)} 条文本")
        
        total_time = time.time() - start_time
        logger.info(f"批量预测完成，总耗时: {total_time:.2f}秒，平均每条: {total_time/len(texts):.3f}秒")
        
        return results
    
    def analyze_prediction_confidence(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析预测置信度
        
        Args:
            results: 预测结果列表
            
        Returns:
            置信度分析结果
        """
        confidences = [r['confidence'] for r in results if 'confidence' in r]
        confident_count = sum(1 for r in results if r.get('is_confident', False))
        
        analysis = {
            'total_predictions': len(results),
            'confident_predictions': confident_count,
            'confidence_rate': confident_count / len(results) if results else 0,
            'avg_confidence': np.mean(confidences) if confidences else 0,
            'min_confidence': np.min(confidences) if confidences else 0,
            'max_confidence': np.max(confidences) if confidences else 0,
            'confidence_std': np.std(confidences) if confidences else 0
        }
        
        # 置信度分布
        confidence_bins = [0.0, 0.3, 0.5, 0.7, 0.9, 1.0]
        confidence_dist = {}
        
        for i in range(len(confidence_bins) - 1):
            low, high = confidence_bins[i], confidence_bins[i + 1]
            count = sum(1 for c in confidences if low <= c < high)
            confidence_dist[f"{low:.1f}-{high:.1f}"] = count
        
        analysis['confidence_distribution'] = confidence_dist
        
        return analysis
    
    def explain_prediction(self, text: str, prediction_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        解释预测结果
        
        Args:
            text: 原始文本
            prediction_result: 预测结果
            
        Returns:
            解释结果
        """
        explanation = {
            'input_text': text,
            'predicted_label': prediction_result.get('predicted_label', '未知'),
            'confidence': prediction_result.get('confidence', 0),
            'key_features': [],
            'error_indicators': []
        }
        
        # 提取错误特征
        error_features = self.data_processor.extract_error_features(text)
        
        # 分析关键特征
        for feature_name, value in error_features.items():
            if value > 0:
                explanation['key_features'].append({
                    'feature': feature_name,
                    'value': value,
                    'description': self._get_feature_description(feature_name)
                })
        
        # 分析错误指示词
        text_lower = text.lower()
        for category, keywords in self.data_processor.error_keywords.items():
            found_keywords = [kw for kw in keywords if kw.lower() in text_lower]
            if found_keywords:
                explanation['error_indicators'].append({
                    'category': category,
                    'keywords': found_keywords,
                    'description': self._get_category_description(category)
                })
        
        return explanation
    
    def _get_feature_description(self, feature_name: str) -> str:
        """获取特征描述"""
        descriptions = {
            'error_connection_count': '连接相关错误关键词数量',
            'error_timeout_count': '超时相关错误关键词数量',
            'error_authentication_count': '认证相关错误关键词数量',
            'error_permission_count': '权限相关错误关键词数量',
            'error_format_count': '格式相关错误关键词数量',
            'error_null_count': '空值相关错误关键词数量',
            'error_invalid_count': '无效值相关错误关键词数量',
            'error_network_count': '网络相关错误关键词数量',
            'error_server_count': '服务器相关错误关键词数量',
            'error_database_count': '数据库相关错误关键词数量',
            'number_count': '文本中数字的数量',
            'has_large_number': '是否包含大数字（可能是错误码）',
            'text_length': '文本长度',
            'word_count': '词汇数量',
            'has_json': '是否包含JSON格式',
            'has_url': '是否包含URL',
            'has_ip': '是否包含IP地址'
        }
        return descriptions.get(feature_name, feature_name)
    
    def _get_category_description(self, category: str) -> str:
        """获取错误类别描述"""
        descriptions = {
            'connection': '连接问题：网络连接失败或中断',
            'timeout': '超时问题：请求或响应超时',
            'authentication': '认证问题：身份验证失败',
            'permission': '权限问题：访问权限不足',
            'format': '格式问题：数据格式错误或解析失败',
            'null': '空值问题：必需字段为空或缺失',
            'invalid': '无效值问题：参数值无效或错误',
            'network': '网络问题：网络层面的错误',
            'server': '服务器问题：服务器内部错误',
            'database': '数据库问题：数据库操作失败'
        }
        return descriptions.get(category, category)
    
    def save_predictions(self, 
                        texts: List[str], 
                        results: List[Dict[str, Any]], 
                        output_path: str):
        """
        保存预测结果
        
        Args:
            texts: 原始文本列表
            results: 预测结果列表
            output_path: 输出文件路径
        """
        # 构建DataFrame
        data = []
        for text, result in zip(texts, results):
            row = {
                '原始文本': text,
                '预测类别': result.get('predicted_label', '未知'),
                '置信度': result.get('confidence', 0),
                '是否可信': result.get('is_confident', False),
                '推理时间': result.get('inference_time', 0)
            }
            
            # 添加Top3预测（如果有）
            if 'top_3_predictions' in result:
                for i, (label, prob) in enumerate(result['top_3_predictions'][:3], 1):
                    row[f'Top{i}_标签'] = label
                    row[f'Top{i}_概率'] = prob
            
            data.append(row)
        
        df = pd.DataFrame(data)
        
        # 保存到Excel
        df.to_excel(output_path, index=False)
        logger.info(f"预测结果已保存到: {output_path}")
        
        return df
