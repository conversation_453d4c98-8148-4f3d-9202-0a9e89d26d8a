# 文档路径: 04-quantization > 06-fp8-w8a8 > FP8 W8A8 > 量化过程 > 1. 加载模型 > 2. 应用量化 > 3. 评估准确性

## 量化过程


量化过程涉及 3 个主要步骤:

1. 加载模型

2. 应用量化

3. 评估 vLLM 的准确性


### 1. 加载模型


使用封装了 `AutoModelForCausalLM` 的 `SparseAutoModelForCausalLM` 来保存和加载量化模型：

```python
from llmcompressor.transformers import SparseAutoModelForCausalLM
from transformers import AutoTokenizer


MODEL_ID = "meta-llama/Meta-Llama-3-8B-Instruct"


model = SparseAutoModelForCausalLM.from_pretrained(
  MODEL_ID, device_map="auto", torch_dtype="auto")
tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
```


### 2. 应用量化


对于 FP8 量化，可以通过简单的 RTN 量化来恢复精度。我们建议使用 `FP8_DYNAMIC` 方案定位所有 `Linear` 层，该方案使用：

- 静态，权重上的每通道量化
- 动态，激活值上的每 token 量化

由于简单的 RTN 在权重量化时不需要数据，，并且激活值是动态量化的，因此我们不需要此量化流程中的任何校准数据。

```python
from llmcompressor.transformers import oneshot
from llmcompressor.modifiers.quantization import QuantizationModifier


# Configure the simple PTQ quantization
# 配置简单 PTQ 量化


recipe = QuantizationModifier(
  targets="Linear", scheme="FP8_DYNAMIC", ignore=["lm_head"])


# Apply the quantization algorithm.
# 应用量化算法。


oneshot(model=model, recipe=recipe)


# Save the model.
# 保存模型。


SAVE_DIR = MODEL_ID.split("/")[1] + "-FP8-Dynamic"
model.save_pretrained(SAVE_DIR)
tokenizer.save_pretrained(SAVE_DIR)
```


### 3. 评估准确性


安装 `vllm` 和 `lm-evaluation-harness`：

```plain
pip install vllm lm_eval==0.4.3
```

在 `vllm` 中加载并运行模型：

```python
from vllm import LLM
model = LLM("./Meta-Llama-3-8B-Instruct-FP8-Dynamic")
model.generate("Hello my name is")
```

使用 `lm_eval` 评估准确性（例如，在 `gsm8k` 的 250 个样本上）:

**注意：**

量化模型可能对 `bos` token 都存在很敏感。默认情况下，`lm_eval` 不会添加 `bos` 标记，因此请确保在运行评估时包含 `add_bos_token=True` 参数。

```plain
MODEL=$PWD/Meta-Llama-3-8B-Instruct-FP8-Dynamic
lm_eval \
  --model vllm \
  --model_args pretrained=$MODEL,add_bos_token=True \
  --tasks gsm8k  --num_fewshot 5 --batch_size auto --limit 250
```

以下是所得分数的示例：

```plain
|Tasks|Version|     Filter     |n-shot|  Metric   |   |Value|   |Stderr|
|-----|------:|----------------|-----:|-----------|---|----:|---|-----:|
|gsm8k|      3|flexible-extract|     5|exact_match|↑  |0.768|±  |0.0268|
|     |       |strict-match    |     5|exact_match|↑  |0.768|±  |0.0268|
```
