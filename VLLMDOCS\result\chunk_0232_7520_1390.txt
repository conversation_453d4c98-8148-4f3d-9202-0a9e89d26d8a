# 文档路径: 07-developer-documentation > 02-offline-inference > 01-llm-class > LLM 类 > 参数：

## 参数：

- **messages** – 单个对话表示为消息列表。每个对话表示为一个消息列表。每个消息是一个带有「role」（角色）和「content」（内容）键的字典。
- **sampling_params** – 用于文本生成的采样参数。如果为 None，则使用默认的采样参数。当它是单个值时，它将应用于每个提示。当它是列表时，列表必须与提示的长度相同，并且与提示一一配对。
- **use_tqdm** – 是否使用 tqdm 显示进度条。
- **lora_request** – 如果有，则为用于生成的 LoRA 请求。
- **chat_template** – 用于构建聊天的模板。如果未提供，将使用模型的默认聊天模板。
- **add_generation_prompt** – 如果为 True，则向每条消息添加生成模板。
- **continue_final_message**– 如果为 True，则继续对话中的最后一条消息，而不是开始新的一条。如果「add_generation_prompt」也为 True，则此参数不能为 True。
- **mm_processor_kwargs\*\***–\*\* 此聊天请求的多模态处理器关键字参数覆盖。仅用于离线请求。
