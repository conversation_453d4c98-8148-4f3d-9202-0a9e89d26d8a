# 文档路径: 03-models > 07-speculative-decoding-in-vllm > vLLM 中的推测解码 > 使用 MLP 推测器进行推测

## 使用 MLP 推测器进行推测

以下代码配置 vLLM 使用推测性解码，其中提案由草稿模型生成，该草稿模型根据上下文向量和采样 token 调节草稿预测。有关更多信息，请参阅[此博客](https://pytorch.org/blog/hitchhikers-guide-speculative-decoding/)或[此技术报告](https://arxiv.org/abs/2404.19124)。

```python
from vllm import LLM, SamplingParams


prompts = [
    "The future of AI is",
]
sampling_params = SamplingParams(temperature=0.8, top_p=0.95)


llm = LLM(
    model="meta-llama/Meta-Llama-3.1-70B-Instruct",
    tensor_parallel_size=4,
    speculative_model="ibm-fms/llama3-70b-accelerator",
    speculative_draft_tensor_parallel_size=1,
    use_v2_block_manager=True,
)
outputs = llm.generate(prompts, sampling_params)


for output in outputs:
    prompt = output.prompt
    generated_text = output.outputs[0].text
    print(f"Prompt: {prompt!r}, Generated text: {generated_text!r}")
```

请注意，这些推测模型当前需要在没有张量并行性的情况下运行，尽管可以使用张量并行性运行主模型 （参见上面的示例）。由于推测模型相对较小，我们仍然能够观察到显著的速度提升。不过此限制将在未来版本中修复。

HF hub 上提供了多种此类推测模型：

- [llama-13b-accelerator](https://huggingface.co/ibm-fms/llama-13b-accelerator)
- [llama3-8b-accelerator](https://huggingface.co/ibm-fms/llama3-8b-accelerator)
- [codellama-34b-accelerator](https://huggingface.co/ibm-fms/codellama-34b-accelerator)
- [llama2-70b-accelerator](https://huggingface.co/ibm-fms/llama2-70b-accelerator)
- [llama3-70b-accelerator](https://huggingface.co/ibm-fms/llama3-70b-accelerator)
- [granite-3b-code-instruct-accelerator](https://huggingface.co/ibm-granite/granite-3b-code-instruct-accelerator)
- [granite-8b-code-instruct-accelerator](https://huggingface.co/ibm-granite/granite-8b-code-instruct-accelerator)
- [granite-7b-instruct-accelerator](https://huggingface.co/ibm-granite/granite-7b-instruct-accelerator)
- [granite-20b-code-instruct-accelerator](https://huggingface.co/ibm-granite/granite-20b-code-instruct-accelerator)
