# 文档路径: 02-serving > 01-openai-compatible-server > OpenAI 兼容服务器 > 聊天模板

## 聊天模板


为了使语言模型支持聊天协议，vLLM 要求模型在其 tokenizer 配置中包含一个聊天模板。聊天模板是一个 Jinja2 模板，它指定了角色、消息和其他特定于聊天对 tokens 如何在输入中编码。

`NousResearch/Meta-Llama-3-8B-Instruct` 的示例聊天模板可以在[这里](https://github.com/meta-llama/llama3?tab=readme-ov-file#instruction-tuned-models)找到。

一些模型即使经过了指令/聊天微调，仍然不提供聊天模板。对于这些模型，你可以在 `--chat-template` 参数中手动指定聊天模板的文件路径或字符串形式。如果没有聊天模板，服务器将无法处理聊天请求，所有聊天请求将出错。

```bash
vllm serve <model> --chat-template ./path-to-chat-template.jinja
```

vLLM 社区为流行的模型提供了一组聊天模板，可以在示例目录[这里](https://github.com/vllm-project/vllm/tree/main/examples/)找到它们。
