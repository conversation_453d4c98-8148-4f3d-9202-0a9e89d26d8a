# 文档路径: 01-getting-started > 03-installation-with-openvino > 使用 OpenVINO 安装 > 性能提示

## 性能提示


vLLM OpenVINO 后端使用以下环境变量来控制行为：

- `VLLM_OPENVINO_KVCACHE_SPACE` 用于指定 KV 缓存大小 （例如，`VLLM_OPENVINO_KVCACHE_SPACE=40` 表示 KV 缓存空间为 40 GB），设置得越大，允许 vLLM 并行处理的请求就越多。该参数应根据用户的硬件配置和内存管理模式来设置。
- `VLLM_OPENVINO_CPU_KV_CACHE_PRECISION=u8` 用于控制 KV 缓存精度。默认情况下，根据平台使用 FP16 或 BF16。
- 设置 `VLLM_OPENVINO_ENABLE_QUANTIZED_WEIGHTS=ON` 在模型加载阶段启用 U8 权重压缩。默认情况下压缩是关闭的。您还可以使用 _optimum-cli_ 以不同的压缩技术导出模型，并将导出的文件夹传递为 `<model_id>`

为了实现更好的 TPOT / TTFT 延迟，您可以使用 vLLM 的分块预填充功能 (`--enable-chunked-prefill`)。根据实验，建议的批处理大小为 256 (`--max-num-batched-tokens`)

OpenVINO 最著名的配置是：

```plain
VLLM_OPENVINO_KVCACHE_SPACE=100 VLLM_OPENVINO_CPU_KV_CACHE_PRECISION=u8 VLLM_OPENVINO_ENABLE_QUANTIZED_WEIGHTS=ON \
    python3 vllm/benchmarks/benchmark_throughput.py --model meta-llama/Llama-2-7b-chat-hf --dataset vllm/benchmarks/ShareGPT_V3_unfiltered_cleaned_split.json --enable-chunked-prefill --max-num-batched-tokens 256
```
