[{"chunk_id": "9992_8371", "file_name": "chunk_0001_9992_8371.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 入门", "main_title": "入门", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "9992_8371", "file_name": "chunk_0002_9992_8371.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 部署", "main_title": "部署", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "9992_8371", "file_name": "chunk_0003_9992_8371.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 模型", "main_title": "模型", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "9992_8371", "file_name": "chunk_0004_9992_8371.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 量化", "main_title": "量化", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "9992_8371", "file_name": "chunk_0005_9992_8371.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 自动前缀缓存", "main_title": "自动前缀缓存", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "9992_8371", "file_name": "chunk_0006_9992_8371.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 性能基准测试", "main_title": "性能基准测试", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "9992_8371", "file_name": "chunk_0007_9992_8371.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 开发者文档", "main_title": "开发者文档", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "5062_5991", "file_name": "chunk_0008_5062_5991.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\01-installation.md", "directory_path": "01-getting-started", "filename": "01-installation", "title_hierarchy": "安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "安装"}}, {"chunk_id": "5062_7159", "file_name": "chunk_0009_5062_7159.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\01-installation.md", "directory_path": "01-getting-started", "filename": "01-installation", "title_hierarchy": "安装 > 使用 pip 安装", "main_title": "使用 pip 安装", "section_level": 2, "document_title": "安装"}}, {"chunk_id": "5062_0653", "file_name": "chunk_0010_5062_0653.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\01-installation.md", "directory_path": "01-getting-started", "filename": "01-installation", "title_hierarchy": "安装 > 从源代码构建", "main_title": "从源代码构建", "section_level": 2, "document_title": "安装"}}, {"chunk_id": "3130_5991", "file_name": "chunk_0011_3130_5991.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\02-installation-with-rocm.md", "directory_path": "01-getting-started", "filename": "02-installation-with-rocm", "title_hierarchy": "使用 ROCm 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 ROCm 安装"}}, {"chunk_id": "3130_7315", "file_name": "chunk_0012_3130_7315.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\02-installation-with-rocm.md", "directory_path": "01-getting-started", "filename": "02-installation-with-rocm", "title_hierarchy": "使用 ROCm 安装 > 选项 1：使用 docker 从源代码构建 （推荐）", "main_title": "选项 1：使用 docker 从源代码构建 （推荐）", "section_level": 2, "document_title": "使用 ROCm 安装"}}, {"chunk_id": "3130_1471", "file_name": "chunk_0013_3130_1471.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\02-installation-with-rocm.md", "directory_path": "01-getting-started", "filename": "02-installation-with-rocm", "title_hierarchy": "使用 ROCm 安装 > 选项 2：从源代码构建", "main_title": "选项 2：从源代码构建", "section_level": 2, "document_title": "使用 ROCm 安装"}}, {"chunk_id": "0602_5991", "file_name": "chunk_0014_0602_5991.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "0602_3685", "file_name": "chunk_0015_0602_3685.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 使用 Dockerfile 快速开始", "main_title": "使用 Dockerfile 快速开始", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "0602_3465", "file_name": "chunk_0016_0602_3465.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 从源代码安装", "main_title": "从源代码安装", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "0602_0135", "file_name": "chunk_0017_0602_0135.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 性能提示", "main_title": "性能提示", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "0602_9065", "file_name": "chunk_0018_0602_9065.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 局限性", "main_title": "局限性", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "9977_5991", "file_name": "chunk_0019_9977_5991.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "9977_3685", "file_name": "chunk_0020_9977_3685.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 使用 Dockerfile 快速开始", "main_title": "使用 Dockerfile 快速开始", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "9977_0653", "file_name": "chunk_0021_9977_0653.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 从源代码构建", "main_title": "从源代码构建", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "9977_2514", "file_name": "chunk_0022_9977_2514.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 相关运行时环境变量", "main_title": "相关运行时环境变量", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "9977_7925", "file_name": "chunk_0023_9977_7925.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > PyTorch 的英特尔扩展", "main_title": "PyTorch 的英特尔扩展", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "9977_0135", "file_name": "chunk_0024_9977_0135.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 性能提示", "main_title": "性能提示", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "5838_5991", "file_name": "chunk_0025_5838_5991.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\05-installation-with-neuron.md", "directory_path": "01-getting-started", "filename": "05-installation-with-neuron", "title_hierarchy": "使用 Neuron 安装 > 从源代码构建 > 步骤 0. 启动 Trn1/Inf2 实例", "main_title": "步骤 0. 启动 Trn1/Inf2 实例", "section_level": 3, "document_title": "使用 <PERSON><PERSON>on 安装"}}, {"chunk_id": "5838_0653", "file_name": "chunk_0026_5838_0653.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\05-installation-with-neuron.md", "directory_path": "01-getting-started", "filename": "05-installation-with-neuron", "title_hierarchy": "使用 Neuron 安装 > 从源代码构建 > 步骤 1. 安装驱动程序和工具", "main_title": "步骤 1. 安装驱动程序和工具", "section_level": 3, "document_title": "使用 <PERSON><PERSON>on 安装"}}, {"chunk_id": "5838_0653", "file_name": "chunk_0027_5838_0653.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\05-installation-with-neuron.md", "directory_path": "01-getting-started", "filename": "05-installation-with-neuron", "title_hierarchy": "使用 Neuron 安装 > 从源代码构建 > 步骤 2. 安装 Transformers-neuronx 及其依赖", "main_title": "步骤 2. 安装 Transformers-neuronx 及其依赖", "section_level": 3, "document_title": "使用 <PERSON><PERSON>on 安装"}}, {"chunk_id": "5838_0653", "file_name": "chunk_0028_5838_0653.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\05-installation-with-neuron.md", "directory_path": "01-getting-started", "filename": "05-installation-with-neuron", "title_hierarchy": "使用 Neuron 安装 > 从源代码构建 > 步骤 3. 从源代码安装 vLLM", "main_title": "步骤 3. 从源代码安装 vLLM", "section_level": 3, "document_title": "使用 <PERSON><PERSON>on 安装"}}, {"chunk_id": "8403_5991", "file_name": "chunk_0029_8403_5991.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\06-installation-with-tpu.md", "directory_path": "01-getting-started", "filename": "06-installation-with-tpu", "title_hierarchy": "使用 TPU 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 TPU 安装"}}, {"chunk_id": "8403_4284", "file_name": "chunk_0030_8403_4284.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\06-installation-with-tpu.md", "directory_path": "01-getting-started", "filename": "06-installation-with-tpu", "title_hierarchy": "使用 TPU 安装 > 使用`Dockerfile.tpu` 构建 Docker 镜像", "main_title": "使用`Dockerfile.tpu` 构建 Docker 镜像", "section_level": 2, "document_title": "使用 TPU 安装"}}, {"chunk_id": "8403_0653", "file_name": "chunk_0031_8403_0653.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\06-installation-with-tpu.md", "directory_path": "01-getting-started", "filename": "06-installation-with-tpu", "title_hierarchy": "使用 TPU 安装 > 从源代码构建", "main_title": "从源代码构建", "section_level": 2, "document_title": "使用 TPU 安装"}}, {"chunk_id": "7444_5991", "file_name": "chunk_0032_7444_5991.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\07-installation-with-xpu.md", "directory_path": "01-getting-started", "filename": "07-installation-with-xpu", "title_hierarchy": "使用 XPU 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 XPU 安装"}}, {"chunk_id": "7444_3685", "file_name": "chunk_0033_7444_3685.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\07-installation-with-xpu.md", "directory_path": "01-getting-started", "filename": "07-installation-with-xpu", "title_hierarchy": "使用 XPU 安装 > 使用 Dockerfile 快速开始", "main_title": "使用 Dockerfile 快速开始", "section_level": 2, "document_title": "使用 XPU 安装"}}, {"chunk_id": "7444_0653", "file_name": "chunk_0034_7444_0653.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\07-installation-with-xpu.md", "directory_path": "01-getting-started", "filename": "07-installation-with-xpu", "title_hierarchy": "使用 XPU 安装 > 从源代码构建", "main_title": "从源代码构建", "section_level": 2, "document_title": "使用 XPU 安装"}}, {"chunk_id": "8634_5991", "file_name": "chunk_0035_8634_5991.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\08-quickstart.md", "directory_path": "01-getting-started", "filename": "08-quickstart", "title_hierarchy": "快速入门 > 兼容 OpenAI 服务器 > 在 vLLM 中使用 OpenAI Completions API", "main_title": "在 vLLM 中使用 OpenAI Completions API", "section_level": 3, "document_title": "快速入门"}}, {"chunk_id": "8634_7353", "file_name": "chunk_0036_8634_7353.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\08-quickstart.md", "directory_path": "01-getting-started", "filename": "08-quickstart", "title_hierarchy": "快速入门 > 兼容 OpenAI 服务器 > 在 vLLM 中使用 OpenAI Chat API", "main_title": "在 vLLM 中使用 OpenAI Chat API", "section_level": 3, "document_title": "快速入门"}}, {"chunk_id": "7173_6216", "file_name": "chunk_0037_7173_6216.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\09-debugging-tips.md", "directory_path": "01-getting-started", "filename": "09-debugging-tips", "title_hierarchy": "调试技巧 > 调试挂起与崩溃问题", "main_title": "调试挂起与崩溃问题", "section_level": 2, "document_title": "调试技巧"}}, {"chunk_id": "5559_5991", "file_name": "chunk_0038_5559_5991.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > 附加参数 > Chat API 的附加参数", "main_title": "Chat API 的附加参数", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "5559_5219", "file_name": "chunk_0039_5559_5219.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > 附加参数 > Completions API 的附加参数", "main_title": "Completions API 的附加参数", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "5559_8994", "file_name": "chunk_0040_5559_8994.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > 服务器的命令行参数 > 命名参数", "main_title": "命名参数", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "5559_9916", "file_name": "chunk_0041_5559_9916.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > chat completion API 中的工具调用 > 命名函数调用", "main_title": "命名函数调用", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "5559_9916", "file_name": "chunk_0042_5559_9916.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > chat completion API 中的工具调用 > 配置文件", "main_title": "配置文件", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "5559_9916", "file_name": "chunk_0043_5559_9916.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > chat completion API 中的工具调用 > 自动函数调用", "main_title": "自动函数调用", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "0805_3752", "file_name": "chunk_0044_0805_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\02-deploying-with-docker.md", "directory_path": "02-serving", "filename": "02-deploying-with-docker", "title_hierarchy": "使用 Docker 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 Docker 进行部署"}}, {"chunk_id": "6782_6953", "file_name": "chunk_0045_6782_6953.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\03-distributed-inference-and-serving.md", "directory_path": "02-serving", "filename": "03-distributed-inference-and-serving", "title_hierarchy": "分布式推理和服务 > 如何决定分布式推理策略？", "main_title": "如何决定分布式推理策略？", "section_level": 2, "document_title": "分布式推理和服务"}}, {"chunk_id": "6782_5509", "file_name": "chunk_0046_6782_5509.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\03-distributed-inference-and-serving.md", "directory_path": "02-serving", "filename": "03-distributed-inference-and-serving", "title_hierarchy": "分布式推理和服务 > 分布式推理和服务的详细信息", "main_title": "分布式推理和服务的详细信息", "section_level": 2, "document_title": "分布式推理和服务"}}, {"chunk_id": "6782_4150", "file_name": "chunk_0047_6782_4150.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\03-distributed-inference-and-serving.md", "directory_path": "02-serving", "filename": "03-distributed-inference-and-serving", "title_hierarchy": "分布式推理和服务 > 多节点推理和服务", "main_title": "多节点推理和服务", "section_level": 2, "document_title": "分布式推理和服务"}}, {"chunk_id": "6133_3752", "file_name": "chunk_0048_6133_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\04-production-metrics.md", "directory_path": "02-serving", "filename": "04-production-metrics", "title_hierarchy": "生产指标 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "生产指标"}}, {"chunk_id": "8217_3752", "file_name": "chunk_0049_8217_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\05-environment-variables.md", "directory_path": "02-serving", "filename": "05-environment-variables", "title_hierarchy": "环境变量 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "环境变量"}}, {"chunk_id": "2857_5991", "file_name": "chunk_0050_2857_5991.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\06-usage-stats-collection.md", "directory_path": "02-serving", "filename": "06-usage-stats-collection", "title_hierarchy": "使用统计数据收集 > 收集了哪些数据？", "main_title": "收集了哪些数据？", "section_level": 2, "document_title": "使用统计数据收集"}}, {"chunk_id": "2857_3880", "file_name": "chunk_0051_2857_3880.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\06-usage-stats-collection.md", "directory_path": "02-serving", "filename": "06-usage-stats-collection", "title_hierarchy": "使用统计数据收集 > 退出使用统计数据收集", "main_title": "退出使用统计数据收集", "section_level": 2, "document_title": "使用统计数据收集"}}, {"chunk_id": "6713_3752", "file_name": "chunk_0052_6713_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\08-tensorizer.md", "directory_path": "02-serving", "filename": "08-tensorizer", "title_hierarchy": "使用 CoreWeave 的张量器加载模型 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 CoreWeave 的张量器加载模型"}}, {"chunk_id": "0016_5991", "file_name": "chunk_0053_0016_5991.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\09-compatibility matrix.md", "directory_path": "02-serving", "filename": "09-compatibility matrix", "title_hierarchy": "兼容性矩阵 > Feature x Feature", "main_title": "Feature x Feature", "section_level": 2, "document_title": "兼容性矩阵"}}, {"chunk_id": "0016_3498", "file_name": "chunk_0054_0016_3498.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\09-compatibility matrix.md", "directory_path": "02-serving", "filename": "09-compatibility matrix", "title_hierarchy": "兼容性矩阵 > Feature x Hardware", "main_title": "Feature x Hardware", "section_level": 2, "document_title": "兼容性矩阵"}}, {"chunk_id": "3585_5991", "file_name": "chunk_0055_3585_5991.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\10-frequently-asked-questions.md", "directory_path": "02-serving", "filename": "10-frequently-asked-questions", "title_hierarchy": "常见问题 > 缓解策略", "main_title": "缓解策略", "section_level": 2, "document_title": "常见问题"}}, {"chunk_id": "6672_5991", "file_name": "chunk_0056_6672_5991.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 仅文本语言模型 > 文本生成", "main_title": "文本生成", "section_level": 3, "document_title": "支持的模型"}}, {"chunk_id": "6672_7326", "file_name": "chunk_0057_6672_7326.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 仅文本语言模型 > 文本 Embedding", "main_title": "文本 Embedding", "section_level": 3, "document_title": "支持的模型"}}, {"chunk_id": "6672_7326", "file_name": "chunk_0058_6672_7326.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 仅文本语言模型 > 获奖模型", "main_title": "获奖模型", "section_level": 3, "document_title": "支持的模型"}}, {"chunk_id": "6672_6987", "file_name": "chunk_0059_6672_6987.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 多模态语言模型 > 文本生成", "main_title": "文本生成", "section_level": 3, "document_title": "支持的模型"}}, {"chunk_id": "6672_6987", "file_name": "chunk_0060_6672_6987.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 多模态语言模型 > 多模态 Embedding", "main_title": "多模态 Embedding", "section_level": 3, "document_title": "支持的模型"}}, {"chunk_id": "1930_5991", "file_name": "chunk_0061_1930_5991.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 0. Fork vLLM 存储库", "main_title": "0. Fork vLLM 存储库", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "1930_9497", "file_name": "chunk_0062_1930_9497.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 1. 引入你的模型代码", "main_title": "1. 引入你的模型代码", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "1930_9219", "file_name": "chunk_0063_1930_9219.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 2. 重写 `forward` 的方法", "main_title": "2. 重写 `forward` 的方法", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "1930_3603", "file_name": "chunk_0064_1930_3603.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 3.（可选）实现张量并行和量化支持", "main_title": "3.（可选）实现张量并行和量化支持", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "1930_6320", "file_name": "chunk_0065_1930_6320.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 5. 注册模型", "main_title": "5. 注册模型", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "1930_4673", "file_name": "chunk_0066_1930_4673.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 6. 树外模型集成", "main_title": "6. 树外模型集成", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "9596_5991", "file_name": "chunk_0067_9596_5991.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 1. 更新基础 vLLM 模型", "main_title": "1. 更新基础 vLLM 模型", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "9596_0275", "file_name": "chunk_0068_9596_0275.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 2. 注册输入映射器", "main_title": "2. 注册输入映射器", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "9596_9543", "file_name": "chunk_0069_9596_9543.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 3. 注册多模态 token 最大数量", "main_title": "3. 注册多模态 token 最大数量", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "9596_7450", "file_name": "chunk_0070_9596_7450.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 4.（可选）注册虚拟数据", "main_title": "4.（可选）注册虚拟数据", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "9596_3627", "file_name": "chunk_0071_9596_3627.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 5.（可选）注册输入处理器", "main_title": "5.（可选）注册输入处理器", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "1058_5991", "file_name": "chunk_0072_1058_5991.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\04-engine-arguments.md", "directory_path": "03-models", "filename": "04-engine-arguments", "title_hierarchy": "引擎参数 > 命名参数", "main_title": "命名参数", "section_level": 3, "document_title": "引擎参数"}}, {"chunk_id": "1058_5600", "file_name": "chunk_0073_1058_5600.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\04-engine-arguments.md", "directory_path": "03-models", "filename": "04-engine-arguments", "title_hierarchy": "引擎参数 > 异步引擎参数 > 命名参数", "main_title": "命名参数", "section_level": 3, "document_title": "引擎参数"}}, {"chunk_id": "9193_5991", "file_name": "chunk_0074_9193_5991.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器 > LoRA 适配器服务", "main_title": "LoRA 适配器服务", "section_level": 2, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "9193_3500", "file_name": "chunk_0075_9193_3500.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器 > 动态提供 LoRA 适配器", "main_title": "动态提供 LoRA 适配器", "section_level": 2, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "9193_3396", "file_name": "chunk_0076_9193_3396.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器 > –lora-modules 的新格式", "main_title": "–lora-modules 的新格式", "section_level": 2, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "9193_8545", "file_name": "chunk_0077_9193_8545.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器 > 模型卡中的 LoRA 模型谱系", "main_title": "模型卡中的 LoRA 模型谱系", "section_level": 2, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "4331_5991", "file_name": "chunk_0078_4331_5991.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\06-using-vlms.md", "directory_path": "03-models", "filename": "06-using-vlms", "title_hierarchy": "使用 VLM > 离线推理 > 单图像输入", "main_title": "单图像输入", "section_level": 3, "document_title": "使用 VLM"}}, {"chunk_id": "4331_4533", "file_name": "chunk_0079_4331_4533.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\06-using-vlms.md", "directory_path": "03-models", "filename": "06-using-vlms", "title_hierarchy": "使用 VLM > 离线推理 > 多图像输入", "main_title": "多图像输入", "section_level": 3, "document_title": "使用 VLM"}}, {"chunk_id": "4027_5991", "file_name": "chunk_0080_4027_5991.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码 > 用草稿模型进行推测", "main_title": "用草稿模型进行推测", "section_level": 2, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "4027_1003", "file_name": "chunk_0081_4027_1003.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码 > 通过在提示符中匹配 n-grams 进行推测", "main_title": "通过在提示符中匹配 n-grams 进行推测", "section_level": 2, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "4027_5119", "file_name": "chunk_0082_4027_5119.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码 > 使用 MLP 推测器进行推测", "main_title": "使用 MLP 推测器进行推测", "section_level": 2, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "4027_2749", "file_name": "chunk_0083_4027_2749.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码 > 相关 vLLM 贡献者的资源", "main_title": "相关 vLLM 贡献者的资源", "section_level": 2, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "8413_8070", "file_name": "chunk_0084_8413_8070.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\08-performance-and-tuning.md", "directory_path": "03-models", "filename": "08-performance-and-tuning", "title_hierarchy": "性能与调优 > 抢占", "main_title": "抢占", "section_level": 2, "document_title": "性能与调优"}}, {"chunk_id": "8413_8590", "file_name": "chunk_0085_8413_8590.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\08-performance-and-tuning.md", "directory_path": "03-models", "filename": "08-performance-and-tuning", "title_hierarchy": "性能与调优 > 分块预填充", "main_title": "分块预填充", "section_level": 2, "document_title": "性能与调优"}}, {"chunk_id": "4919_5991", "file_name": "chunk_0086_4919_5991.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\01-supported_hardware.md", "directory_path": "04-quantization", "filename": "01-supported_hardware", "title_hierarchy": "量化内核支持的硬件 > 注意:", "main_title": "注意:", "section_level": 2, "document_title": "量化内核支持的硬件"}}, {"chunk_id": "9911_3752", "file_name": "chunk_0087_9911_3752.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\02-autoawq.md", "directory_path": "04-quantization", "filename": "02-<PERSON><PERSON><PERSON>", "title_hierarchy": "AutoAWQ > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "AutoAWQ"}}, {"chunk_id": "2788_5991", "file_name": "chunk_0088_2788_5991.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\03-bitsandbytes.md", "directory_path": "04-quantization", "filename": "03-bitsandbytes", "title_hierarchy": "BitsAndBytes > 读取量化 checkpoint", "main_title": "读取量化 checkpoint", "section_level": 2, "document_title": "BitsAndBytes"}}, {"chunk_id": "2788_1591", "file_name": "chunk_0089_2788_1591.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\03-bitsandbytes.md", "directory_path": "04-quantization", "filename": "03-bitsandbytes", "title_hierarchy": "BitsAndBytes > 过程中量化：加载为 4 位量化", "main_title": "过程中量化：加载为 4 位量化", "section_level": 2, "document_title": "BitsAndBytes"}}, {"chunk_id": "4815_3752", "file_name": "chunk_0090_4815_3752.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\04-gguf.md", "directory_path": "04-quantization", "filename": "04-gguf", "title_hierarchy": "GGUF > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "GGUF"}}, {"chunk_id": "1079_5991", "file_name": "chunk_0091_1079_5991.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8 > 量化过程 > 1. 加载模型", "main_title": "1. 加载模型", "section_level": 3, "document_title": "INT8 W8A8"}}, {"chunk_id": "1079_0763", "file_name": "chunk_0092_1079_0763.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8 > 量化过程 > 2. 准备校准数据", "main_title": "2. 准备校准数据", "section_level": 3, "document_title": "INT8 W8A8"}}, {"chunk_id": "1079_0763", "file_name": "chunk_0093_1079_0763.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8 > 量化过程 > 3. 应用量化", "main_title": "3. 应用量化", "section_level": 3, "document_title": "INT8 W8A8"}}, {"chunk_id": "1079_0763", "file_name": "chunk_0094_1079_0763.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8 > 量化过程 > 4. 评估准确性", "main_title": "4. 评估准确性", "section_level": 3, "document_title": "INT8 W8A8"}}, {"chunk_id": "7484_5991", "file_name": "chunk_0095_7484_5991.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8 > 量化过程 > 1. 加载模型", "main_title": "1. 加载模型", "section_level": 3, "document_title": "FP8 W8A8"}}, {"chunk_id": "7484_0763", "file_name": "chunk_0096_7484_0763.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8 > 量化过程 > 2. 应用量化", "main_title": "2. 应用量化", "section_level": 3, "document_title": "FP8 W8A8"}}, {"chunk_id": "7484_0763", "file_name": "chunk_0097_7484_0763.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8 > 量化过程 > 3. 评估准确性", "main_title": "3. 评估准确性", "section_level": 3, "document_title": "FP8 W8A8"}}, {"chunk_id": "6860_3752", "file_name": "chunk_0098_6860_3752.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\07-fp8-e5m2-kv-cache.md", "directory_path": "04-quantization", "filename": "07-fp8-e5m2-kv-cache", "title_hierarchy": "FP8 E5M2 KV 缓存 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "FP8 E5M2 KV 缓存"}}, {"chunk_id": "0872_3752", "file_name": "chunk_0099_0872_3752.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\08-fp8-e4m3-kv-cache.md", "directory_path": "04-quantization", "filename": "08-fp8-e4m3-kv-cache", "title_hierarchy": "FP8 E4M3 KV 缓存 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "FP8 E4M3 KV 缓存"}}, {"chunk_id": "1665_9692", "file_name": "chunk_0100_1665_9692.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\01-introduction-apc.md", "directory_path": "05-automatic-prefix-caching", "filename": "01-introduction-apc", "title_hierarchy": "简介 > 什么是自动前缀缓存 (Automatic Prefix Caching)", "main_title": "什么是自动前缀缓存 (Automatic Prefix Caching)", "section_level": 2, "document_title": "简介"}}, {"chunk_id": "1665_7301", "file_name": "chunk_0101_1665_7301.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\01-introduction-apc.md", "directory_path": "05-automatic-prefix-caching", "filename": "01-introduction-apc", "title_hierarchy": "简介 > 在 vLLM 中启用 APC", "main_title": "在 vLLM 中启用 APC", "section_level": 2, "document_title": "简介"}}, {"chunk_id": "1665_8772", "file_name": "chunk_0102_1665_8772.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\01-introduction-apc.md", "directory_path": "05-automatic-prefix-caching", "filename": "01-introduction-apc", "title_hierarchy": "简介 > 工作负载示例", "main_title": "工作负载示例", "section_level": 2, "document_title": "简介"}}, {"chunk_id": "1665_9065", "file_name": "chunk_0103_1665_9065.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\01-introduction-apc.md", "directory_path": "05-automatic-prefix-caching", "filename": "01-introduction-apc", "title_hierarchy": "简介 > 局限性", "main_title": "局限性", "section_level": 2, "document_title": "简介"}}, {"chunk_id": "3637_5991", "file_name": "chunk_0104_3637_5991.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\02-implementation.md", "directory_path": "05-automatic-prefix-caching", "filename": "02-implementation", "title_hierarchy": "实现 (Implementation) > 通用缓存策略", "main_title": "通用缓存策略", "section_level": 1, "document_title": "实现 (Implementation)"}}, {"chunk_id": "2416_5991", "file_name": "chunk_0105_2416_5991.txt", "metadata": {"source_file": "version-0.8.x\\06-performance-benchmarks\\06-benchmark-suites-of-vllm.md", "directory_path": "06-performance-benchmarks", "filename": "06-benchmark-suites-of-vllm", "title_hierarchy": "vLLM 基准套件 > 触发基准测试", "main_title": "触发基准测试", "section_level": 2, "document_title": "vLLM 基准套件"}}, {"chunk_id": "0351_5991", "file_name": "chunk_0106_0351_5991.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\01-sampling-parameters.md", "directory_path": "07-developer-documentation", "filename": "01-sampling-parameters", "title_hierarchy": "采样参数 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "采样参数"}}, {"chunk_id": "0578_5991", "file_name": "chunk_0107_0578_5991.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > Softmax > `qk_max` 和 `logits`", "main_title": "`qk_max` 和 `logits`", "section_level": 3, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "0578_8109", "file_name": "chunk_0108_0578_8109.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > Softmax > `exp_sum`", "main_title": "`exp_sum`", "section_level": 3, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "0404_3752", "file_name": "chunk_0109_0404_3752.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\07-dockerfile.md", "directory_path": "07-developer-documentation", "filename": "07-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "Dockerfile > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Dockerfile"}}, {"chunk_id": "9515_5991", "file_name": "chunk_0110_9515_5991.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\08-profiling-vllm.md", "directory_path": "07-developer-documentation", "filename": "08-profiling-vllm", "title_hierarchy": "分析 vLLM > 命令和使用示例： > 离线推理", "main_title": "离线推理", "section_level": 3, "document_title": "分析 vLLM"}}, {"chunk_id": "9515_5672", "file_name": "chunk_0111_9515_5672.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\08-profiling-vllm.md", "directory_path": "07-developer-documentation", "filename": "08-profiling-vllm", "title_hierarchy": "分析 vLLM > 命令和使用示例： > OpenAI 服务器：", "main_title": "OpenAI 服务器：", "section_level": 3, "document_title": "分析 vLLM"}}, {"chunk_id": "6792_0273", "file_name": "chunk_0112_6792_0273.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > A", "main_title": "A", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_8757", "file_name": "chunk_0113_6792_8757.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > B", "main_title": "B", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_1579", "file_name": "chunk_0114_6792_1579.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > C", "main_title": "C", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_9828", "file_name": "chunk_0115_6792_9828.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > D", "main_title": "D", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_4671", "file_name": "chunk_0116_6792_4671.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > E", "main_title": "E", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_8405", "file_name": "chunk_0117_6792_8405.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > F", "main_title": "F", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_0365", "file_name": "chunk_0118_6792_0365.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > G", "main_title": "G", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_5348", "file_name": "chunk_0119_6792_5348.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > H", "main_title": "H", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_3630", "file_name": "chunk_0120_6792_3630.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > I", "main_title": "I", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_7289", "file_name": "chunk_0121_6792_7289.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > L", "main_title": "L", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_2471", "file_name": "chunk_0122_6792_2471.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > M", "main_title": "M", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_4776", "file_name": "chunk_0123_6792_4776.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > N", "main_title": "N", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_3514", "file_name": "chunk_0124_6792_3514.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > P", "main_title": "P", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_5535", "file_name": "chunk_0125_6792_5535.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > R", "main_title": "R", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_3507", "file_name": "chunk_0126_6792_3507.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > S", "main_title": "S", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_5055", "file_name": "chunk_0127_6792_5055.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > T", "main_title": "T", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_9066", "file_name": "chunk_0128_6792_9066.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > U", "main_title": "U", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "6792_4434", "file_name": "chunk_0129_6792_4434.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > V", "main_title": "V", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "7481_3752", "file_name": "chunk_0130_7481_3752.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\02-python-module-index.md", "directory_path": "08-indices-and-tables", "filename": "02-python-module-index", "title_hierarchy": "Python 模块索引 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Python 模块索引"}}, {"chunk_id": "0075_3752", "file_name": "chunk_0131_0075_3752.txt", "metadata": {"source_file": "version-0.8.x\\09-community\\01-vllm-meetups.md", "directory_path": "09-community", "filename": "01-vllm-meetups", "title_hierarchy": "vLLM 交流会 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "vLLM 交流会"}}, {"chunk_id": "4970_3752", "file_name": "chunk_0132_4970_3752.txt", "metadata": {"source_file": "version-0.8.x\\09-community\\02-sponsors.md", "directory_path": "09-community", "filename": "02-sponsors", "title_hierarchy": "赞助商 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "赞助商"}}, {"chunk_id": "0050_5991", "file_name": "chunk_0133_0050_5991.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 二、开始使用 > 2.1 模型准备", "main_title": "2.1 模型准备", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "0050_1372", "file_name": "chunk_0134_0050_1372.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 二、开始使用 > 2.2 离线推理", "main_title": "2.2 离线推理", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "0050_8815", "file_name": "chunk_0135_0050_8815.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 三、启动 vLLM 服务器 > 3.1 主要参数设置", "main_title": "3.1 主要参数设置", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "0050_8815", "file_name": "chunk_0136_0050_8815.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 三、启动 vLLM 服务器 > 3.2 启动命令行", "main_title": "3.2 启动命令行", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "0050_8708", "file_name": "chunk_0137_0050_8708.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 四、发出请求 > 4.1 使用 OpenAI 客户端", "main_title": "4.1 使用 OpenAI 客户端", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "0050_8708", "file_name": "chunk_0138_0050_8708.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 四、发出请求 > 4.2 使用 Curl 命令请求", "main_title": "4.2 使用 Curl 命令请求", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "7811_5991", "file_name": "chunk_0139_7811_5991.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 目录", "main_title": "目录", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "7811_3652", "file_name": "chunk_0140_7811_3652.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 1. 安装 vLLM", "main_title": "1. 安装 vLLM", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "7811_3456", "file_name": "chunk_0141_7811_3456.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 2. 使用 vLLM 加载 Qwen 量化模型", "main_title": "2. 使用 vLLM 加载 Qwen 量化模型", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "7811_6745", "file_name": "chunk_0142_7811_6745.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 3. 加载测试数据", "main_title": "3. 加载测试数据", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "7811_2594", "file_name": "chunk_0143_7811_2594.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 4. 提示工程", "main_title": "4. 提示工程", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "7811_2638", "file_name": "chunk_0144_7811_2638.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 5. Infer 测试", "main_title": "5. <PERSON><PERSON> 测试", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "7811_2464", "file_name": "chunk_0145_7811_2464.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 6. 提取推理概率", "main_title": "6. 提取推理概率", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "7811_0110", "file_name": "chunk_0146_7811_0110.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 7. 创建提交 CSV", "main_title": "7. 创建提交 CSV", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "7811_8698", "file_name": "chunk_0147_7811_8698.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 8. 计算 CV 分数", "main_title": "8. 计算 CV 分数", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "9907_5991", "file_name": "chunk_0148_9907_5991.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 5. 辅助函数 > 在给定 subject 和 construct 的情况下获取最相似的 question_ids'", "main_title": "在给定 subject 和 construct 的情况下获取最相似的 question_ids'", "section_level": 3, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "9907_6129", "file_name": "chunk_0149_9907_6129.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 5. 辅助函数 > 获取每个问题的聊天对话", "main_title": "获取每个问题的聊天对话", "section_level": 3, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "9907_5446", "file_name": "chunk_0150_9907_5446.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 7. 找到最相似的误解 > 合并每个问题的每个生成输出的排名", "main_title": "合并每个问题的每个生成输出的排名", "section_level": 3, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "7110_5991", "file_name": "chunk_0151_7110_5991.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\04-vllm-langchain-tutorial.md", "directory_path": "10-tutorials", "filename": "04-vllm-langchain-tutorial", "title_hierarchy": "将 LangChain 与 vLLM 结合使用：完整教程 > 1. 安装和设置 vLLM > Docker 安装", "main_title": "Docker 安装", "section_level": 3, "document_title": "将 LangChain 与 vLLM 结合使用：完整教程"}}, {"chunk_id": "7457_5273", "file_name": "chunk_0152_7457_5273.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\10-examples.md", "directory_path": "01-getting-started > 10-examples", "filename": "10-examples", "title_hierarchy": "示例 > 脚本", "main_title": "脚本", "section_level": 2, "document_title": "示例"}}, {"chunk_id": "9728_3752", "file_name": "chunk_0153_9728_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\01-api_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "01-api_client", "title_hierarchy": "API 客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "API 客户端"}}, {"chunk_id": "6995_3752", "file_name": "chunk_0154_6995_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\02-aqlm_example.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "02-aqlm_example", "title_hierarchy": "Aqlm 示例 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Aqlm 示例"}}, {"chunk_id": "1156_3752", "file_name": "chunk_0155_1156_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\03-cpu_offload.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "03-cpu_offload", "title_hierarchy": "CPU 离线处理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "CPU 离线处理"}}, {"chunk_id": "9452_3752", "file_name": "chunk_0156_9452_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\04-gguf_inference.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "04-gguf_inference", "title_hierarchy": "GGUF 推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "GGUF 推理"}}, {"chunk_id": "5450_3752", "file_name": "chunk_0157_5450_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\05-gradio_openai_chatbot_webserver.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "05-gradio_openai_chatbot_webserver", "title_hierarchy": "Gradio OpenAI 聊天机器人 Web 服务器 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Gradio OpenAI 聊天机器人 Web 服务器"}}, {"chunk_id": "3006_3752", "file_name": "chunk_0158_3006_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\06-gradio_webserver.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "06-gradio_webserver", "title_hierarchy": "Gradio Web 服务器 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Gradio Web 服务器"}}, {"chunk_id": "0110_3752", "file_name": "chunk_0159_0110_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\07-llm_engine_example.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "07-llm_engine_example", "title_hierarchy": "LLM 引擎示例 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "LLM 引擎示例"}}, {"chunk_id": "3077_3752", "file_name": "chunk_0160_3077_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\08-lora_with_quantization_inference.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "08-lora_with_quantization_inference", "title_hierarchy": "带量化的 LoRA 推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "带量化的 LoRA 推理"}}, {"chunk_id": "8451_3752", "file_name": "chunk_0161_8451_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\09-multilora_inference.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "09-multilora_inference", "title_hierarchy": "MultiLoRA 推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "MultiLoRA 推理"}}, {"chunk_id": "9915_3752", "file_name": "chunk_0162_9915_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\10-offline_chat_with_tools.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "10-offline_chat_with_tools", "title_hierarchy": "离线聊天工具 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线聊天工具"}}, {"chunk_id": "2493_3752", "file_name": "chunk_0163_2493_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\11-offline_inference.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "11-offline_inference", "title_hierarchy": "API Client > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "API Client"}}, {"chunk_id": "0732_3752", "file_name": "chunk_0164_0732_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\12-offline_inference_arctic.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "12-offline_inference_arctic", "title_hierarchy": "离线推理 Arctic > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 Arctic"}}, {"chunk_id": "4981_3752", "file_name": "chunk_0165_4981_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\13-offline_inference_audio_language.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "13-offline_inference_audio_language", "title_hierarchy": "离线推理音频语言 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理音频语言"}}, {"chunk_id": "2867_3752", "file_name": "chunk_0166_2867_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\14-offline_inference_chat.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "14-offline_inference_chat", "title_hierarchy": "离线推理聊天 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理聊天"}}, {"chunk_id": "8832_3752", "file_name": "chunk_0167_8832_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\15-offline_inference_distributed.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "15-offline_inference_distributed", "title_hierarchy": "离线推理分布式 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理分布式"}}, {"chunk_id": "4344_3752", "file_name": "chunk_0168_4344_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\16-offline_inference_embedding.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "16-offline_inference_embedding", "title_hierarchy": "离线推理嵌入 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理嵌入"}}, {"chunk_id": "4927_3752", "file_name": "chunk_0169_4927_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\17-offline_inference_encoder_decoder.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "17-offline_inference_encoder_decoder", "title_hierarchy": "离线推理编码器-解码器 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理编码器-解码器"}}, {"chunk_id": "9704_3752", "file_name": "chunk_0170_9704_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\18-offline_inference_mlpspeculator.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "18-offline_inference_mlpspeculator", "title_hierarchy": "离线推理 MlpSpeculator > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 MlpSpeculator"}}, {"chunk_id": "2218_3752", "file_name": "chunk_0171_2218_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\19-offline_inference_neuron.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "19-offline_inference_neuron", "title_hierarchy": "离线推理 Neuron > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 Neuron"}}, {"chunk_id": "1785_3752", "file_name": "chunk_0172_1785_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\20-offline_inference_neuron_int8_quantization.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "20-offline_inference_neuron_int8_quantization", "title_hierarchy": "离线推理 Neuron Int8 量化 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 Neuron Int8 量化"}}, {"chunk_id": "7724_3752", "file_name": "chunk_0173_7724_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\21-offline_inference_pixtral.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "21-offline_inference_pixtral", "title_hierarchy": "使用 Pixtral 进行离线推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 Pixtral 进行离线推理"}}, {"chunk_id": "0608_3752", "file_name": "chunk_0174_0608_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\22-offline_inference_tpu.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "22-offline_inference_tpu", "title_hierarchy": "离线推理 TPU > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 TPU"}}, {"chunk_id": "0081_3752", "file_name": "chunk_0175_0081_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\23-offline_inference_vision_language.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "23-offline_inference_vision_language", "title_hierarchy": "离线推理视觉语言 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理视觉语言"}}, {"chunk_id": "8551_3752", "file_name": "chunk_0176_8551_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\24-offline_inference_vision_language_multi_image.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "24-offline_inference_vision_language_multi_image", "title_hierarchy": "离线推理视觉语言多图像 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理视觉语言多图像"}}, {"chunk_id": "9327_3752", "file_name": "chunk_0177_9327_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\25-offline_inference_with_prefix.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "25-offline_inference_with_prefix", "title_hierarchy": "带前缀的离线推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "带前缀的离线推理"}}, {"chunk_id": "4831_3752", "file_name": "chunk_0178_4831_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\26-offline_inference_with_profiler.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "26-offline_inference_with_profiler", "title_hierarchy": "启用分析器的离线推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "启用分析器的离线推理"}}, {"chunk_id": "7486_3752", "file_name": "chunk_0179_7486_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\27-openai_audio_api_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "27-openai_audio_api_client", "title_hierarchy": "OpenAI 音频 API 客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 音频 API 客户端"}}, {"chunk_id": "8564_3752", "file_name": "chunk_0180_8564_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\28-openai_chat_completion_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "28-openai_chat_completion_client", "title_hierarchy": "OpenAI 聊天补全客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 聊天补全客户端"}}, {"chunk_id": "5727_3752", "file_name": "chunk_0181_5727_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\29-openai_chat_completion_client_with_tools.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "29-openai_chat_completion_client_with_tools", "title_hierarchy": "带有工具的 OpenAI 聊天补全客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "带有工具的 OpenAI 聊天补全客户端"}}, {"chunk_id": "5811_3752", "file_name": "chunk_0182_5811_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\30-openai_completion_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "30-openai_completion_client", "title_hierarchy": "OpenAI 补全客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 补全客户端"}}, {"chunk_id": "7064_3752", "file_name": "chunk_0183_7064_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\31-openai_embedding_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "31-openai_embedding_client", "title_hierarchy": "OpenAI 嵌入客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 嵌入客户端"}}, {"chunk_id": "0153_3752", "file_name": "chunk_0184_0153_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\32-openai_vision_api_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "32-openai_vision_api_client", "title_hierarchy": "OpenAI 视觉 API 客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 视觉 API 客户端"}}, {"chunk_id": "2992_3752", "file_name": "chunk_0185_2992_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\33-save_sharded_state.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "33-save_sharded_state", "title_hierarchy": "保存分片状态 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "保存分片状态"}}, {"chunk_id": "1575_3752", "file_name": "chunk_0186_1575_3752.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\34-tensorize_vllm_model.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "34-tensorize_vllm_model", "title_hierarchy": "Tensorize vLLM 模型 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Tensorize vLLM 模型"}}, {"chunk_id": "9139_5991", "file_name": "chunk_0187_9139_5991.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\01-deploying&scaling-up-with-skypilot.md", "directory_path": "02-serving > 07-integrations", "filename": "01-deploying&scaling-up-with-skypilot", "title_hierarchy": "使用 SkyPilot 进行部署和扩充 > 扩充到多个副本 > **可选**: 将 GUI 连接到端点", "main_title": "**可选**: 将 GUI 连接到端点", "section_level": 3, "document_title": "使用 SkyPilot 进行部署和扩充"}}, {"chunk_id": "4753_3752", "file_name": "chunk_0188_4753_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\02-deploying-with-kserve.md", "directory_path": "02-serving > 07-integrations", "filename": "02-deploying-with-kserve", "title_hierarchy": "使用 KServe 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 KServe 进行部署"}}, {"chunk_id": "2763_3752", "file_name": "chunk_0189_2763_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\03-deploying-with-nvidia-triton.md", "directory_path": "02-serving > 07-integrations", "filename": "03-deploying-with-nvidia-triton", "title_hierarchy": "使用 NVIDIA Triton 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 NVIDIA Triton 进行部署"}}, {"chunk_id": "1965_3752", "file_name": "chunk_0190_1965_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\04-deploying-with-bentoml.md", "directory_path": "02-serving > 07-integrations", "filename": "04-deploying-with-bentoml", "title_hierarchy": "使用 BentoML 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 BentoML 进行部署"}}, {"chunk_id": "0510_3752", "file_name": "chunk_0191_0510_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\05-deploying-with-cerebrium.md", "directory_path": "02-serving > 07-integrations", "filename": "05-deploying-with-cerebrium", "title_hierarchy": "使用 Cerebrium 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 Cerebrium 进行部署"}}, {"chunk_id": "0798_3752", "file_name": "chunk_0192_0798_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\06-deploying-with-lws.md", "directory_path": "02-serving > 07-integrations", "filename": "06-deploying-with-lws", "title_hierarchy": "使用 LWS 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 LWS 进行部署"}}, {"chunk_id": "9944_3752", "file_name": "chunk_0193_9944_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\07-deploying-with-dstack.md", "directory_path": "02-serving > 07-integrations", "filename": "07-deploying-with-dstack", "title_hierarchy": "使用 dstack 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 dstack 进行部署"}}, {"chunk_id": "0951_3752", "file_name": "chunk_0194_0951_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\08-serving-with-langchain.md", "directory_path": "02-serving > 07-integrations", "filename": "08-serving-with-langchain", "title_hierarchy": "使用 Langchain 提供服务 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 Langchain 提供服务"}}, {"chunk_id": "2240_3752", "file_name": "chunk_0195_2240_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\09-serving-with-llama_index.md", "directory_path": "02-serving > 07-integrations", "filename": "09-serving-with-llama_index", "title_hierarchy": "使用 llama_index 服务 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 llama_index 服务"}}, {"chunk_id": "0596_3752", "file_name": "chunk_0196_0596_3752.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\readme.md", "directory_path": "02-serving > 07-integrations", "filename": "readme", "title_hierarchy": "整合 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "整合"}}, {"chunk_id": "9889_5991", "file_name": "chunk_0197_9889_5991.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "9889_9370", "file_name": "chunk_0198_9889_9370.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "9889_9428", "file_name": "chunk_0199_9889_9428.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 返回", "main_title": "返回", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "9889_9370", "file_name": "chunk_0200_9889_9370.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "9889_9428", "file_name": "chunk_0201_9889_9428.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 返回", "main_title": "返回", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "9889_9370", "file_name": "chunk_0202_9889_9370.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "9889_9428", "file_name": "chunk_0203_9889_9428.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 返回", "main_title": "返回", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "2321_9875", "file_name": "chunk_0204_2321_9875.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\02-llm-inputs.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "02-llm-inputs", "title_hierarchy": "LLM Inputs > vllm.inputs.PromptType", "main_title": "vllm.inputs.PromptType", "section_level": 2, "document_title": "LLM Inputs"}}, {"chunk_id": "2599_3752", "file_name": "chunk_0205_2599_3752.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\readme.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "readme", "title_hierarchy": "离线推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理"}}, {"chunk_id": "5419_5991", "file_name": "chunk_0206_5419_5991.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine > Example", "main_title": "Example", "section_level": 2, "document_title": "LLMEngine"}}, {"chunk_id": "5419_2895", "file_name": "chunk_0207_5419_2895.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine > 案例", "main_title": "案例", "section_level": 2, "document_title": "LLMEngine"}}, {"chunk_id": "5419_8585", "file_name": "chunk_0208_5419_8585.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine > 详细信息：", "main_title": "详细信息：", "section_level": 2, "document_title": "LLMEngine"}}, {"chunk_id": "5419_7163", "file_name": "chunk_0209_5419_7163.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine > 示例", "main_title": "示例", "section_level": 2, "document_title": "LLMEngine"}}, {"chunk_id": "2850_3752", "file_name": "chunk_0210_2850_3752.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\02-asyncllmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "02-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "AsyncLLMEngine > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "AsyncLLMEngine"}}, {"chunk_id": "3783_3752", "file_name": "chunk_0211_3783_3752.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\readme.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "readme", "title_hierarchy": "vLLM Engine > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "vLLM Engine"}}, {"chunk_id": "1929_5991", "file_name": "chunk_0212_1929_5991.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\05-input-processing\\01-model_inputs_index.md", "directory_path": "07-developer-documentation > 05-input-processing", "filename": "01-model_inputs_index", "title_hierarchy": "输入处理 > 模块内容 > LLM 引擎输入", "main_title": "LLM 引擎输入", "section_level": 3, "document_title": "输入处理"}}, {"chunk_id": "1929_7714", "file_name": "chunk_0213_1929_7714.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\05-input-processing\\01-model_inputs_index.md", "directory_path": "07-developer-documentation > 05-input-processing", "filename": "01-model_inputs_index", "title_hierarchy": "输入处理 > 模块内容 > 注册", "main_title": "注册", "section_level": 3, "document_title": "输入处理"}}, {"chunk_id": "6657_3752", "file_name": "chunk_0214_6657_3752.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\05-input-processing\\02-input-processing-pipeline.md", "directory_path": "07-developer-documentation > 05-input-processing", "filename": "02-input-processing-pipeline", "title_hierarchy": "输入处理管道 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "输入处理管道"}}, {"chunk_id": "3556_3752", "file_name": "chunk_0215_3556_3752.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\06-multi-modality\\01-adding-a-multimodal-plugin.md", "directory_path": "07-developer-documentation > 06-multi-modality", "filename": "01-adding-a-multimodal-plugin", "title_hierarchy": "添加多模态插件 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "添加多模态插件"}}, {"chunk_id": "0629_5991", "file_name": "chunk_0216_0629_5991.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\06-multi-modality\\readme.md", "directory_path": "07-developer-documentation > 06-multi-modality", "filename": "readme", "title_hierarchy": "多模态 > 模块内容 > 注册", "main_title": "注册", "section_level": 3, "document_title": "多模态"}}, {"chunk_id": "0629_7714", "file_name": "chunk_0217_0629_7714.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\06-multi-modality\\readme.md", "directory_path": "07-developer-documentation > 06-multi-modality", "filename": "readme", "title_hierarchy": "多模态 > 模块内容 > 基础类 (Base Classes)", "main_title": "基础类 (Base Classes)", "section_level": 3, "document_title": "多模态"}}, {"chunk_id": "0629_7714", "file_name": "chunk_0218_0629_7714.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\06-multi-modality\\readme.md", "directory_path": "07-developer-documentation > 06-multi-modality", "filename": "readme", "title_hierarchy": "多模态 > 模块内容 > 图像类", "main_title": "图像类", "section_level": 3, "document_title": "多模态"}}]