# 文档路径: 07-developer-documentation > 02-offline-inference > 01-llm-class > LLM 类 > 参数：

## 参数：


- **prompts** – 给 LLM 的提示。您可以传递一系列提示以进行批量推理。有关每个提示的格式的更多详细信息，请参阅 [PromptType](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.PromptType)。
- **sampling_params** – 用于文本生成的采样参数。如果为 None，则使用默认的采样参数。当它是单个值时，它将应用于每个提示。当它是一个列表时，该列表必须与提示长度相同，并且与提示一一配对。
- **use_tqdm** – 是否使用 tqdm 显示进度条。
- **lora_request** – 如果有，则为用于生成的 LoRA 请求。
- **prompt_adapter_request** – 如果有，则为用于生成的提示适配器请求。
