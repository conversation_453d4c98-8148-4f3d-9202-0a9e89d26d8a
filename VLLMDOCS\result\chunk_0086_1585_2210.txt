# 文档路径: 03-models > 03-enabling-multimodal-inputs > 启用多模态输入 > 5.（可选）注册输入处理器

## 5.（可选）注册输入处理器

有时，需要在将输入传递给模型执行器之前，在 `LLMEngine` 层面对它们进行处理，这通常是因为与 HuggingFace Transformers 中的实现不同，多模态嵌入的重塑和/或扩展需要在模型的 `forward()` 调用之外进行。您可以通过 `INPUT_REGISTRY.register_input_processor` 注册输入处理器。

```diff
from vllm.inputs import INPUT_REGISTRY
from vllm.model_executor.models.interfaces import SupportsMultiModal
from vllm.multimodal import MULTIMODAL_REGISTRY


@MULTIMODAL_REGISTRY.register_image_input_mapper()
@MULTIMODAL_REGISTRY.register_max_image_tokens(<your_calculation>)
@INPUT_REGISTRY.register_dummy_data(<your_dummy_data_factory>)
+ @INPUT_REGISTRY.register_input_processor(<your_input_processor>)
class YourModelForImage2Seq(nn.Module, SupportsMultiModal):
```

一个常见的输入处理器用例是插入占位符 tokens，以利用 vLLM 框架生成注意力掩码。以下是一些示例：

- 插入静态图像 token 数量：[LLaVA-1.5模型](https://github.com/vllm-project/vllm/blob/main/vllm/model_executor/models/llava.py)
- 插入动态数量的图像标记：[LLaVA-NeXT 模型](https://github.com/vllm-project/vllm/blob/main/vllm/model_executor/models/llava_next.py)

**另见**

`输入处理管道`
