{"embedding_model_name": "all-MiniLM-L6-v2", "label_mapping": {"0": "tool_call中的arguments为非标准JSON体，大模型无法解析", "1": "入参中填写tools，但实际未配置工具", "2": "向量化模型不支持自定义dimensions参数", "3": "响应体数据超过默认的缓冲区限制", "4": "图片base64编码数据部分未进行正确填充", "5": "图片错误", "6": "多模态使用中type为text时，text为一个对象", "7": "多模态使用中type为text时，text为数字", "8": "大模型600S还没回答完，导致超时关闭连接", "9": "大模型回答是null", "10": "大模型要求回答输出tokens数超大，导致请求tokens+回答tokens超过上下文tokens数量。", "11": "大模型请求超600s未返回", "12": "大模型鉴权未通过", "13": "对话类大模型messages被错误嵌套", "14": "当前使用模型不支持多模态输入", "15": "模型不支持content中为list的数据格式", "16": "模型服务返回异常回答", "17": "用户主动切断大模型回复", "18": "请求体中arguments字段未进行转义", "19": "请求体中content为数组", "20": "请求体中content为空", "21": "请求体中top_p为0", "22": "请求体中模型名称错误", "23": "请求体中缺少必要的message", "24": "请求体使用function call时缺少arguments字段", "25": "请求体的message中无内容", "26": "请求体输入内容过长（前置判断，非大模型判断）", "27": "请求模型与请求地址未匹配", "28": "非标准JSON入参"}}