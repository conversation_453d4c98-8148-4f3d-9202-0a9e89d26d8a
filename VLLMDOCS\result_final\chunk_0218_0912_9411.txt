# 文档路径: 07-developer-documentation > 02-offline-inference > 02-llm-inputs > LLM Inputs > class vllm.inputs.TextPrompt

# class vllm.inputs.TextPrompt


[[source]](https://docs.vllm.ai/en/latest/_modules/vllm/inputs/data.html#TextPrompt)

基类： [TypedDict](https://typing-extensions.readthedocs.io/en/latest/index.html#typing_extensions.TypedDict)

文本提示的架构。

> prompt: [str](https://docs.python.org/3/library/stdtypes.html#str)
> 在传递到模型之前要标记化的输入文本。

> multi_modal_data: typing_extensions.NotRequired[MultiModalDataDict]
> 如果模型支持，可传递给模型的可选多模态数据。

> mm_processor_kwargs: typing_extensions.NotRequired[[Dict](https://docs.python.org/3/library/typing.html#typing.Dict)[[str](https://docs.python.org/3/library/stdtypes.html#str), [Any](https://docs.python.org/3/library/typing.html#typing.Any)]]
> 将被转发到多模态输入映射器和处理器的可选多模态处理器关键字参数。请注意，如果有多种模态已为正在考虑的模型注册了映射器等，我们会尝试将多模态处理器关键字参数传递给它们中的每一个。
