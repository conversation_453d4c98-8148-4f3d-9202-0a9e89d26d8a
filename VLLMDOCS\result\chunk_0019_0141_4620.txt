# 文档路径: 01-getting-started > 03-installation-with-openvino > 使用 OpenVINO 安装 > 从源代码安装

## 从源代码安装

- 首先，安装 Python。例如，在 Ubuntu 22.04 上，您可以运行：

```plain
    sudo apt-get update  -y
    sudo apt-get install python3
```

- 其次，安装 vLLM OpenVINO 后端的依赖：

```plain
    pip install --upgrade pip
    pip install -r requirements-build.txt --extra-index-url https://download.pytorch.org/whl/cpu
```

- 最后，安装带有 OpenVINO 后端的 vLLM：

```plain
    PIP_EXTRA_INDEX_URL="https://download.pytorch.org/whl/cpu" VLLM_TARGET_DEVICE=openvino python -m pip install -v .
```
