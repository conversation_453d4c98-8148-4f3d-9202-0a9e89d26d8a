# 文档路径: 03-models > 05-using-lora-adapters > 使用 LoRA 适配器

本文档向您展示如何在基本模型之上将 [LoRA 适配器](https://arxiv.org/abs/2106.09685) 与 vLLM 结合使用。

LoRA 适配器可与任何实现了 `SupportsLoRA` 的 vLLM 模型一起使用。

适配器能以最小的开销根据每个请求有效地服务。首先，我们下载适配器并将其保存在本地：

```python
from huggingface_hub import snapshot_download


sql_lora_path = snapshot_download(repo_id="yard1/llama-2-7b-sql-lora-test")
```

然后我们实例化基础模型并传入 `enable_lora=True` 标志:

```python
from vllm import LLM, SamplingParams
from vllm.lora.request import LoRARequest


llm = LLM(model="meta-llama/Llama-2-7b-hf", enable_lora=True)
```

我们现在可以提交提示并使用 `lora_request` 参数调用 `llm.generate` 。`LoRARequest` 的第一个参数是人为可识别的名称，第二个参数是适配器的全局唯一 ID，第三个参数是 LoRA 适配器的路径。

```python
sampling_params = SamplingParams(
    temperature=0,
    max_tokens=256,
    stop=["[/assistant]"]
)


prompts = [
     "[user] Write a SQL query to answer the question based on the table schema.\n\n context: CREATE TABLE table_name_74 (icao VARCHAR, airport VARCHAR)\n\n question: Name the ICAO for lilongwe international airport [/user] [assistant]",
     "[user] Write a SQL query to answer the question based on the table schema.\n\n context: CREATE TABLE table_name_11 (nationality VARCHAR, elector VARCHAR)\n\n question: When Anchero Pantaleone was the elector what is under nationality? [/user] [assistant]",
]


outputs = llm.generate(
    prompts,
    sampling_params,
    lora_request=LoRARequest("sql_adapter", 1, sql_lora_path)
)
```

查看 [examples/multilora_inference.py](https://github.com/vllm-project/vllm/blob/main/examples/multilora_inference.py)，了解如何将 LoRA 适配器与异步引擎结合使用以及如何使用更高级的配置选项。