"""
数据预处理模块

负责文本数据的清洗、标准化、特征提取和编码等预处理工作。
支持中英文混合文本处理，针对应用服务报错场景进行优化。
"""

import pandas as pd
import numpy as np
import re
import jieba
from typing import List, Tuple, Dict, Any, Optional
from sklearn.preprocessing import LabelEncoder
from sklearn.feature_extraction.text import TfidfVectorizer
from transformers import AutoTokenizer
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataProcessor:
    """
    数据预处理器
    
    主要功能：
    1. 文本清洗和标准化
    2. 特征提取（TF-IDF、词嵌入等）
    3. 标签编码
    4. 数据增强（针对小样本场景）
    """
    
    def __init__(self, 
                 tokenizer_name: str = "bert-base-chinese",
                 max_length: int = 512,
                 min_text_length: int = 5,
                 max_text_length: int = 10000):
        """
        初始化数据预处理器
        
        Args:
            tokenizer_name: 预训练模型的tokenizer名称
            max_length: 文本最大长度（用于截断）
            min_text_length: 最小文本长度（过滤用）
            max_text_length: 最大文本长度（过滤用）
        """
        self.tokenizer_name = tokenizer_name
        self.max_length = max_length
        self.min_text_length = min_text_length
        self.max_text_length = max_text_length
        
        # 初始化组件
        self.tokenizer = None
        self.label_encoder = LabelEncoder()
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            ngram_range=(1, 2),
            stop_words=None  # 保留所有词，因为错误信息中的停用词可能有意义
        )
        
        # 错误关键词词典（用于特征增强）
        self.error_keywords = {
            'connection': ['connection', 'connect', '连接', '链接'],
            'timeout': ['timeout', 'time out', '超时', '时间'],
            'authentication': ['auth', 'authentication', 'unauthorized', '认证', '授权', '鉴权'],
            'permission': ['permission', 'forbidden', 'access', '权限', '禁止'],
            'format': ['format', 'json', 'xml', 'parse', '格式', '解析'],
            'null': ['null', 'none', 'empty', '空', '为空'],
            'invalid': ['invalid', 'error', 'wrong', '无效', '错误', '异常'],
            'network': ['network', 'net', 'http', 'https', '网络'],
            'server': ['server', 'service', '服务器', '服务'],
            'database': ['database', 'db', 'sql', '数据库']
        }
        
        logger.info(f"数据预处理器初始化完成，使用tokenizer: {tokenizer_name}")
    
    def load_tokenizer(self):
        """延迟加载tokenizer"""
        if self.tokenizer is None:
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(self.tokenizer_name)
                logger.info(f"成功加载tokenizer: {self.tokenizer_name}")
            except Exception as e:
                logger.warning(f"无法加载预训练tokenizer {self.tokenizer_name}: {e}")
                logger.info("将使用基础文本处理方法")
    
    def clean_text(self, text: str) -> str:
        """
        清洗文本数据
        
        Args:
            text: 原始文本
            
        Returns:
            清洗后的文本
        """
        if pd.isna(text) or text is None:
            return ""
        
        text = str(text)
        
        # 1. 去除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 2. 去除特殊字符（保留中英文、数字、常见标点）
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()[\]{}"\'`~@#$%^&*+=|\\/<>-]', '', text)
        
        # 3. 标准化常见错误表达
        text = re.sub(r'(\d+)ms', r'\1毫秒', text)
        text = re.sub(r'(\d+)s', r'\1秒', text)
        text = re.sub(r'HTTP\s*(\d+)', r'HTTP\1', text)
        
        # 4. 去除首尾空白
        text = text.strip()
        
        return text
    
    def extract_error_features(self, text: str) -> Dict[str, int]:
        """
        提取错误特征
        
        Args:
            text: 文本内容
            
        Returns:
            错误特征字典
        """
        text_lower = text.lower()
        features = {}
        
        # 统计各类错误关键词出现次数
        for category, keywords in self.error_keywords.items():
            count = sum(text_lower.count(keyword.lower()) for keyword in keywords)
            features[f'error_{category}_count'] = count
        
        # 提取数字特征（可能是错误码、时间等）
        numbers = re.findall(r'\d+', text)
        features['number_count'] = len(numbers)
        features['has_large_number'] = int(any(int(num) > 1000 for num in numbers if num.isdigit()))
        
        # 文本长度特征
        features['text_length'] = len(text)
        features['word_count'] = len(text.split())
        
        # 特殊字符特征
        features['has_json'] = int('{' in text and '}' in text)
        features['has_url'] = int('http' in text_lower or 'www' in text_lower)
        features['has_ip'] = int(bool(re.search(r'\d+\.\d+\.\d+\.\d+', text)))
        
        return features
    
    def segment_chinese_text(self, text: str) -> List[str]:
        """
        中文分词
        
        Args:
            text: 中文文本
            
        Returns:
            分词结果列表
        """
        # 使用jieba进行中文分词
        words = jieba.lcut(text)
        # 过滤掉长度为1的单字（除非是数字或英文）
        filtered_words = [word for word in words if len(word) > 1 or word.isdigit() or word.isalpha()]
        return filtered_words
    
    def preprocess_texts(self, texts: List[str]) -> Tuple[List[str], np.ndarray]:
        """
        批量预处理文本
        
        Args:
            texts: 文本列表
            
        Returns:
            (清洗后的文本列表, 特征矩阵)
        """
        logger.info(f"开始预处理 {len(texts)} 条文本...")
        
        # 1. 文本清洗
        cleaned_texts = [self.clean_text(text) for text in texts]
        
        # 2. 过滤异常长度的文本
        valid_indices = []
        valid_texts = []
        
        for i, text in enumerate(cleaned_texts):
            if self.min_text_length <= len(text) <= self.max_text_length:
                valid_indices.append(i)
                valid_texts.append(text)
        
        logger.info(f"过滤后保留 {len(valid_texts)} 条有效文本")
        
        # 3. 提取错误特征
        error_features_list = [self.extract_error_features(text) for text in valid_texts]
        
        # 转换为特征矩阵
        if error_features_list:
            feature_names = list(error_features_list[0].keys())
            feature_matrix = np.array([[features[name] for name in feature_names] 
                                     for features in error_features_list])
        else:
            feature_matrix = np.array([]).reshape(0, 0)
        
        logger.info(f"提取了 {feature_matrix.shape[1] if feature_matrix.size > 0 else 0} 维错误特征")
        
        return valid_texts, feature_matrix, valid_indices
    
    def encode_labels(self, labels: List[str], fit: bool = True) -> np.ndarray:
        """
        编码标签
        
        Args:
            labels: 标签列表
            fit: 是否拟合编码器
            
        Returns:
            编码后的标签数组
        """
        if fit:
            encoded_labels = self.label_encoder.fit_transform(labels)
            logger.info(f"标签编码完成，共 {len(self.label_encoder.classes_)} 个类别")
        else:
            encoded_labels = self.label_encoder.transform(labels)
        
        return encoded_labels
    
    def decode_labels(self, encoded_labels: np.ndarray) -> List[str]:
        """
        解码标签
        
        Args:
            encoded_labels: 编码后的标签
            
        Returns:
            原始标签列表
        """
        return self.label_encoder.inverse_transform(encoded_labels).tolist()
    
    def get_label_mapping(self) -> Dict[int, str]:
        """
        获取标签映射关系
        
        Returns:
            {编码: 原始标签} 的映射字典
        """
        return {i: label for i, label in enumerate(self.label_encoder.classes_)}
    
    def process_dataset(self, 
                       texts: List[str], 
                       labels: List[str],
                       fit_encoders: bool = True) -> Tuple[List[str], np.ndarray, np.ndarray, List[int]]:
        """
        处理完整数据集
        
        Args:
            texts: 文本列表
            labels: 标签列表
            fit_encoders: 是否拟合编码器
            
        Returns:
            (处理后的文本, 特征矩阵, 编码后的标签, 有效索引)
        """
        logger.info("开始处理数据集...")
        
        # 预处理文本
        processed_texts, feature_matrix, valid_indices = self.preprocess_texts(texts)
        
        # 过滤对应的标签
        valid_labels = [labels[i] for i in valid_indices]
        
        # 编码标签
        encoded_labels = self.encode_labels(valid_labels, fit=fit_encoders)
        
        logger.info(f"数据集处理完成：{len(processed_texts)} 条样本，{len(self.label_encoder.classes_)} 个类别")
        
        return processed_texts, feature_matrix, encoded_labels, valid_indices
