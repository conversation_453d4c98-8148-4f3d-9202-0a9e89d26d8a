# 文档路径: 07-developer-documentation > 02-offline-inference > 01-llm-class > LLM 类 > 返回

## 返回


一个包含 _EmbeddingRequestOutput_ 对象的列表，这些对象包含生成的嵌入，其顺序与输入提示的顺序相同。

**注意：**

使用 `prompts` 和 `prompt_token_ids` 作为关键字参数的做法已经过时，将来可能会被弃用。您应该改为通过 `inputs` 参数传递它们。

> generate(prompts: [str](https://docs.python.org/3/library/stdtypes.html#str), sampling_params: [SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams) | [List](https://docs.python.org/3/library/typing.html#typing.List)[[SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams)] | [None](https://docs.python.org/3/library/constants.html#None) = None, prompt_token_ids: [List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)] | [None](https://docs.python.org/3/library/constants.html#None) = None, use_tqdm: [bool](https://docs.python.org/3/library/functions.html#bool) = True, lora_request: [List](https://docs.python.org/3/library/typing.html#typing.List)[LoRARequest] | LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [List](https://docs.python.org/3/library/typing.html#typing.List)[RequestOutput] 
[[source]](https://docs.vllm.ai/en/latest/_modules/vllm/entrypoints/llm.html#LLM.generate)

> generate(prompts: [List](https://docs.python.org/3/library/typing.html#typing.List)[[str](https://docs.python.org/3/library/stdtypes.html#str)], sampling_params: [SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams) | [List](https://docs.python.org/3/library/typing.html#typing.List)[[SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams)] | [None](https://docs.python.org/3/library/constants.html#None) = None, prompt_token_ids: [List](https://docs.python.org/3/library/typing.html#typing.List)[[List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)]] | [None](https://docs.python.org/3/library/constants.html#None) = None, use_tqdm: [bool](https://docs.python.org/3/library/functions.html#bool) = True, lora_request: [List](https://docs.python.org/3/library/typing.html#typing.List)[LoRARequest] | LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [List](https://docs.python.org/3/library/typing.html#typing.List)[RequestOutput]
> generate(prompts: [str](https://docs.python.org/3/library/stdtypes.html#str) | [None](https://docs.python.org/3/library/constants.html#None) = None, sampling_params: [SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams) | [List](https://docs.python.org/3/library/typing.html#typing.List)[[SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams)] | [None](https://docs.python.org/3/library/constants.html#None) = None, *, prompt_token_ids: [List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)], use_tqdm: [bool](https://docs.python.org/3/library/functions.html#bool) = True, lora_request: [List](https://docs.python.org/3/library/typing.html#typing.List)[LoRARequest] | LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [List](https://docs.python.org/3/library/typing.html#typing.List)[RequestOutput]
generate(prompts: [List](https://docs.python.org/3/library/typing.html#typing.List)[[str](https://docs.python.org/3/library/stdtypes.html#str)] | [None](https://docs.python.org/3/library/constants.html#None) = None, sampling_params: [SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams) | [List](https://docs.python.org/3/library/typing.html#typing.List)[[SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams)] | [None](https://docs.python.org/3/library/constants.html#None) = None, *, prompt_token_ids: [List](https://docs.python.org/3/library/typing.html#typing.List)[[List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)]], use_tqdm: [bool](https://docs.python.org/3/library/functions.html#bool) = True, lora_request: [List](https://docs.python.org/3/library/typing.html#typing.List)[LoRARequest] | LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [List](https://docs.python.org/3/library/typing.html#typing.List)[RequestOutput]
> generate(prompts: [None](https://docs.python.org/3/library/constants.html#None), sampling_params: [None](https://docs.python.org/3/library/constants.html#None), prompt_token_ids: [List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)] | [List](https://docs.python.org/3/library/typing.html#typing.List)[[List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)]], use_tqdm: [bool](https://docs.python.org/3/library/functions.html#bool) = True, lora_request: [List](https://docs.python.org/3/library/typing.html#typing.List)[LoRARequest] | LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [List](https://docs.python.org/3/library/typing.html#typing.List)[RequestOutput]
> generate(prompts: [PromptType](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.PromptType) | [Sequence](https://docs.python.org/3/library/typing.html#typing.Sequence)[[PromptType](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.PromptType)], /, \*, sampling_params: [SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams) | [Sequence](https://docs.python.org/3/library/typing.html#typing.Sequence)[[SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams)] | [None](https://docs.python.org/3/library/constants.html#None) = None, use_tqdm: [bool](https://docs.python.org/3/library/functions.html#bool) = True, lora_request: [List](https://docs.python.org/3/library/typing.html#typing.List)[LoRARequest] | LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [List](https://docs.python.org/3/library/typing.html#typing.List)[RequestOutput]

为输入的提示生成补全内容。

这个类会自动对给定的提示进行批处理，同时考虑内存限制。为了获得最佳性能，请将你所有的提示放入一个单独的列表中，并将其传递给这个方法。
