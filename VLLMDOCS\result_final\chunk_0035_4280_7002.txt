# 文档路径: 02-serving > 01-openai-compatible-server > OpenAI 兼容服务器 > chat completion API 中的工具调用 > 命名函数调用 > 配置文件

## chat completion API 中的工具调用



### 命名函数调用


默认情况下，vLLM 在聊天完成 API 中仅支持命名函数调用。它通过 Outlines 实现这一点，因此默认情况下是启用的，并且可以与任何受支持的模型一起使用。你将获得一个可有效解析的函数调用 —— 但不一定是高质量的。

要使用命名函数，你需要在 chat completion 请求的 `tools` 参数中定义函数，并在 `tool_choice` 参数中指定其中一个工具的 `name`。


### 配置文件


`serve` 模块也可以接受来自 `yaml` 格式配置文件的参数。yaml 中的参数必须使用[这里](https://docs.vllm.ai/en/latest/serving/openai_compatible_server.html#command-line-arguments-for-the-server)概述的参数长格式来指定。

例如：

```yaml
# config.yaml


host: "127.0.0.1"
port: 6379
uvicorn-log-level: "info"
$ vllm serve SOME_MODEL --config config.yaml

---
```

**注意**

如果通过命令行和配置文件提供了参数，则命令行中的值将优先。优先级顺序为 `command line > config file values > defaults`。

---
