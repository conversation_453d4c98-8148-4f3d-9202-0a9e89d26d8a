# 文档路径: 10-tutorials > 01-vLLM-stepbysteb > vLLM 入门教程：零基础分步指南 > 二、开始使用 > 2.1 模型准备

## 二、开始使用


### 2.1 模型准备


#### 方法一：使用平台公共模型

首先，我们可以检查平台的公共模型是否已经存在。如果模型已上传到公共资源库，您可以直接使用。如果没有找到，则请参考方法二进行下载。

例如，平台已存放了 `Qwen-1_8B-Chat` 模型。以下是绑定模型的步骤（本教程已将此模型捆绑）。

![图片](/img/docs/02-tutorials/model.png)

![图片](/img/docs/02-tutorials/id.png)

![图片](/img/docs/02-tutorials/bangding.png)


#### 方法二：从 HuggingFace下载 或者 联系客服帮忙上传平台

大多数主流模型都可以在 HuggingFace 上找到，vLLM 支持的模型列表请参见官方文档： [vllm-supported-models](https://vllm.io/docs/supported_models)。

本教程将使用 `Qwen-1_8B-Chat` 进行测试。请按照以下步骤使用 Git LFS 下载模型：

```bash
apt install git-lfs -y
git lfs install
# 下载模型，将模型文件下载到当前文件夹
cd /input0/
git clone https://huggingface.co/Qwen/Qwen-1_8B-Chat
```
