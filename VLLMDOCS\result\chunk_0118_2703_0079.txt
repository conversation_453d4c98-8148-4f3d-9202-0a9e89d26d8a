# 文档路径: 04-quantization > 06-fp8-w8a8 > FP8 W8A8 > 量化过程 > 1. 加载模型

## 量化过程

量化过程涉及 3 个主要步骤:

1. 加载模型

2. 应用量化

3. 评估 vLLM 的准确性


### 1. 加载模型

使用封装了 `AutoModelForCausalLM` 的 `SparseAutoModelForCausalLM` 来保存和加载量化模型：

```python
from llmcompressor.transformers import SparseAutoModelForCausalLM
from transformers import AutoTokenizer


MODEL_ID = "meta-llama/Meta-Llama-3-8B-Instruct"


model = SparseAutoModelForCausalLM.from_pretrained(
  MODEL_ID, device_map="auto", torch_dtype="auto")
tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
```
