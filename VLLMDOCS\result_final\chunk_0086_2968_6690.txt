# 文档路径: 04-quantization > 06-fp8-w8a8 > FP8 W8A8 > 使用在线动态量化快速入门

vLLM 支持在 Nvidia H100 和 AMD MI300x 等 GPU 上利用硬件加速进行 FP8（8 位浮点数）的权重和激活量化。当前，仅 Hopper 和 Ada Lovelace GPU 对 W8A8 提供官方支持。对于 W8A16（仅权重为 FP8），可借助 Marlin 内核在 Ampere GPU 上得到支持。通过 FP8 对模型进行量化，能够使模型内存需求降低一半，吞吐量最多可提高 1.6 倍，同时对准确性的影响最小。

请访问 Hugging Face 上[可与 vLLM 一起使用的流行 LLM 的量化 FP8 检查点集合](https://huggingface.co/collections/neuralmagic/fp8-llms-for-vllm-666742ed2b78b7ac8df13127)。

硬件中通常支持的 FP8 类型有 2 种不同的表示形式，每种表示形式在不同的场景中都很有用：

- **E4M3**：由 1 位符号位、4 位指数位和 3 位尾数组成。它可以存储高达 +/-448 和 `nan` 的值。
- **E5M2**：由 1 位符号位、5 位指数位和 2 位尾数组成。它可以存储高达 +/-57344、+/- `inf` 和 `nan` 的值。增加动态范围的代价是存储值的精度较低。

**注意：**

FP8 计算支持计算能力大于 8.9 的 NVIDIA GPU (Ada Lovelace，Hopper)。FP8 模型将在计算能力大于 8.0 (Ampere) 的 GPU 上以仅权重为 W8A16 的形式运行，使用 FP8 Marlin。

## 使用在线动态量化快速入门


使用 vLLM 可以实现原始精密为 BF16/FP16 的模型到 FP8 的动态量化，且无需任何校准数据。您可以通过在命令行中指定 `--quantization="fp8"` ，或在 LLM 构造函数中设置 `quantization="fp8"` 来启用该功能。

在此模式下，所有 Linear 模块（最终的 `lm_head` 除外） 的权重均按张量尺度量化至 FP8_E4M3 精度。激活值在每次前向传播过程中计算其最小值和最大值，以提供动态的每个张量尺度，从而实现高准确性。因此，在这种模式下，延迟的改善是有限的。

```python
from vllm import LLM
model = LLM("facebook/opt-125m", quantization="fp8")
# INFO 06-10 17:55:42 model_runner.py:157] Loading model weights took 0.1550 GB
# INFO 06-10 17:55:42 model_runner.py:157] 加载模型权重占用了 0.1550 GB


result = model.generate("Hello, my name is")
```

**警告：**
目前，我们在量化到 8 位之前以原始精度加载模型，因此您需要足够的内存来加载整个模型。
