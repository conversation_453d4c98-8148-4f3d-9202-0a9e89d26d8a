# 文档路径: 01-getting-started > 01-installation > 安装 > 从源代码构建

## 从源代码构建


您还可以从源代码构建并安装 vLLM：

```plain
git clone https://github.com/vllm-project/vllm.git
cd vllm
pip install -e .  # This may take 5-10 minutes.

pip install -e 。  # 这可能需要 5-10 分钟。
```

**注意**

vLLM 只能在 Linux 上完全运行，但您仍然可以在其他系统（例如 macOS）上构建它。此构建仅用于开发目的，允许导入并提供更方便的开发环境。这些二进制文件不会被编译，也无法在非 Linux 系统上运行。您可以使用以下命令创建这样的构建：

```plain
export VLLM_TARGET_DEVICE=empty
pip install -e .
```

**提示**

从源代码进行构建需要大量的编译工作。如果您多次从源代码构建，那么缓存编译结果是很有益处的。例如，您可以通过 _conda install ccache_ 或 _apt install ccache_ 安装 [ccache](https://github.com/ccache/ccache) 。只要 _which ccache_ 命令可以找到 _ccache_ 二进制文件，构建系统就会自动使用它。在第一次构建之后，后续的构建将会快很多。

**提示**

为了避免系统过载，您可以通过环境变量 _MAX_JOBS_ 限制同时运行的编译任务数量。例如：

```plain
export MAX_JOBS=6
pip install -e .
```

**提示**

如果您在构建 vLLM 时遇到问题，我们建议使用 NVIDIA PyTorch Docker 镜像。

```plain
# Use `--ipc=host` to make sure the shared memory is large enough.

# 使用 `--ipc=host` 确保共享内存足够大。

docker run --gpus all -it --rm --ipc=host nvcr.io/nvidia/pytorch:23.10-py3
```

如果您不想使用 docker，建议完整安装 CUDA 工具包。您可以从[官方网站](https://developer.nvidia.com/cuda-toolkit-archive)下载并安装它。安装完成后，将环境变量 _CUDA_HOME_ 设置为 CUDA 工具包的安装路径，并确保 _nvcc_ 编译器在您的 _PATH_ 中，例如：

```plain
export CUDA_HOME=/usr/local/cuda
export PATH="${CUDA_HOME}/bin:$PATH"
```

以下是验证 CUDA 工具包是否已正确安装的完整检查：

```plain
nvcc --version # verify that nvcc is in your PATH

nvcc --version # 验证 nvcc 是否在您的 PATH 中

${CUDA_HOME}/bin/nvcc --version # verify that nvcc is in your CUDA_HOME

${CUDA_HOME}/bin/nvcc --version # 验证 nvcc 是否在您的 CUDA_HOME 中
```
