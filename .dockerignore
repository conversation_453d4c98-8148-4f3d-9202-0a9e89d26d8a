# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Logs
*.log
logs/

# Data files (exclude large data files)
*.xlsx
*.csv
*.json
data/
outputs/

# Model files (exclude large model files)
models/
*.pth
*.bin
*.safetensors

# Temporary files
tmp/
temp/
.tmp/

# Documentation
docs/
*.md
!README.md

# Test files
tests/
.pytest_cache/
.coverage
htmlcov/

# Docker
Dockerfile*
docker-compose*
.dockerignore
