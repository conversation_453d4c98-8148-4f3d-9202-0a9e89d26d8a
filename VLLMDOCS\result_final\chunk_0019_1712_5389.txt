# 文档路径: 01-getting-started > 04-installation-with-cpu > 使用 CPU 安装 > 性能提示

## 性能提示


- 我们强烈建议使用 TCMalloc 来实现高性能内存分配和更好的缓存局部性。例如，在 Ubuntu 22.4 上，您可以运行：

```plain
sudo apt-get install libtcmalloc-minimal4 # install TCMalloc library

sudo apt-get install libtcmalloc-minimal4 # 安装 TCMalloc 库

find / -name *libtcmalloc* # find the dynamic link library path

find / -name *libtcmalloc* #查找动态链接库路径

export LD_PRELOAD=/usr/lib/x86_64-linux-gnu/libtcmalloc_minimal.so.4:$LD_PRELOAD # prepend the library to LD_PRELOAD

export LD_PRELOAD=/usr/lib/x86_64-linux-gnu/libtcmalloc_minimal.so.4:$LD_PRELOAD # 将库添加到 LD_PRELOAD 之前

python examples/offline_inference.py # run vLLM

python examples/offline_inference.py # 运行 vLLM
```

- 使用在线服务时，建议为服务框架预留 1-2 个 CPU 核心，以避免 CPU 超额使用。例如，在一个具有 32 个物理 CPU 核心的平台上，为框架预留 CPU 30 和 31，并将 CPU 0-29 用于 OpenMP：

```plain
export VLLM_CPU_KVCACHE_SPACE=40
export VLLM_CPU_OMP_THREADS_BIND=0-29
vllm serve facebook/opt-125m
```

- 如果在具有超线程的计算机上使用 vLLM CPU 后端，建议使用 `VLLM_CPU_OMP_THREADS_BIND`在每个物理 CPU 核心上仅绑定一个 OpenMP 线程。在一个启用超线程且具有 16 个逻辑 CPU 核心 / 8 个物理 CPU 核心的平台上：

```plain
lscpu -e # check the mapping between logical CPU cores and physical CPU cores
lscpu -e # 查看逻辑 CPU 核和物理 CPU 核的映射关系


# The "CPU" column means the logical CPU core IDs, and the "CORE" column means the physical core IDs. On this platform, two logical cores are sharing one physical core.
# 「CPU」列表示逻辑 CPU 核心 ID，「CORE」列表示物理核心 ID。在此平台上，两个逻辑核心共享一个物理核心。

CPU NODE SOCKET CORE L1d:L1i:L2:L3 ONLINE    MAXMHZ   MINMHZ      MHZ
0    0      0    0 0:0:0:0          yes 2401.0000 800.0000  800.000
1    0      0    1 1:1:1:0          yes 2401.0000 800.0000  800.000
2    0      0    2 2:2:2:0          yes 2401.0000 800.0000  800.000
3    0      0    3 3:3:3:0          yes 2401.0000 800.0000  800.000
4    0      0    4 4:4:4:0          yes 2401.0000 800.0000  800.000
5    0      0    5 5:5:5:0          yes 2401.0000 800.0000  800.000
6    0      0    6 6:6:6:0          yes 2401.0000 800.0000  800.000
7    0      0    7 7:7:7:0          yes 2401.0000 800.0000  800.000
8    0      0    0 0:0:0:0          yes 2401.0000 800.0000  800.000
9    0      0    1 1:1:1:0          yes 2401.0000 800.0000  800.000
10   0      0    2 2:2:2:0          yes 2401.0000 800.0000  800.000
11   0      0    3 3:3:3:0          yes 2401.0000 800.0000  800.000
12   0      0    4 4:4:4:0          yes 2401.0000 800.0000  800.000
13   0      0    5 5:5:5:0          yes 2401.0000 800.0000  800.000
14   0      0    6 6:6:6:0          yes 2401.0000 800.0000  800.000
15   0      0    7 7:7:7:0          yes 2401.0000 800.0000  800.000

# On this platform, it is recommend to only bind openMP threads on logical CPU cores 0-7 or 8-15

# 在此平台上，建议仅在逻辑 CPU 核心 0-7 或 8-15 上绑定 openMP 线程

export VLLM_CPU_OMP_THREADS_BIND=0-7
python examples/offline_inference.py
```

- 如果在具有非统一内存访问架构（NUMA）的多插槽机器上使用 vLLM 的 CPU 后端，请注意使用 `VLLM_CPU_OMP_THREADS_BIND` 设置 CPU 核心，以避免跨 NUMA 节点的内存访问。
