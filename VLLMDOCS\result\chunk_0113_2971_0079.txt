# 文档路径: 04-quantization > 05-int8-w8a8 > INT8 W8A8 > 量化过程 > 1. 加载模型

## 量化过程

量化过程涉及 4 个主要步骤：

1. 加载模型

2. 准备校准数据

3. 应用量化

4. 评估 vLLM 的准确性


### 1. 加载模型

使用包装了 `AutoModelForCausalLM` 的 `SparseAutoModelForCausalLM` 来实现量化模型的保存和加载：

```python
from llmcompressor.transformers import SparseAutoModelForCausalLM
from transformers import AutoTokenizer


MODEL_ID = "meta-llama/Meta-Llama-3-8B-Instruct"
model = SparseAutoModelForCausalLM.from_pretrained(
    MODEL_ID, device_map="auto", torch_dtype="auto",
)
tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
```
