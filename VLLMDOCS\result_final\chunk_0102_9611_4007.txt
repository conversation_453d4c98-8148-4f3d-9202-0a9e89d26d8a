# 文档路径: 07-developer-documentation > 04-vllm-paged-attention > vLLM  分页注意力 > 概念

## 概念


- 在我们深入讨论计算流程之前，我想先介绍一下后续部分需要理解的一些概念。如果您遇到任何难以理解的术语，可以选择跳过本节，稍后再回来查看。
- **序列** **(\*\***Sequence\***\*)**：序列代表了客户端的请求。例如，由 `q` 指向的数据具有 `[num_seqs，num_heads，head_size]` 的形状。这表示 `q` 指向总共 `num_seqs` 个查询序列数据。由于该内核是 1 个单查询注意力内核，每个序列仅包含一个查询 token 。因此，`num_seqs` 等于批次中处理的 token 总数。
- **上下文\*\***(Context)：\*\*上下文由序列中生成的 token 组成。例如，`["What" ,  "is" ,  "your"]` 是上下文 token，输入查询 token 是 `"name"`。该模型可能会生成 token `?`。
- **向量 (\*\***Vec\***\*)：**Vec 是指一起获取和计算的一组元素。对于查询和键数据，确定 vec 大小 （`VEC_SIZE`），以便每个线程组可以一次性获取和计算 16 字节的数据。对于值数据，确定 vec 大小 (`V_VEC_SIZE`)，以便每个线程可以一次性获取和计算 16 字节的数据。例如，如果 `scalar_t` 为 FP16（2 字节） 且 `THREAD_GROUP_SIZE` 为 2，则 `VEC_SIZE` 将为 4，而 `V_VEC_SIZE` 将为 8。
- **线程组** **(\*\***Thread group\***\*)：**线程组是由一定数量的线程 (`THREAD_GROUP_SIZE`) 组成的小组，它们一次获取和计算 1 个查询标记和 1 个键标记。每个线程仅处理 token 数据的一部分。一个线程组处理的元素总数称为 `x`。例如，如果线程组包含 2 个线程，头大小为 8，则线程 0 处理索引为 0、2、4、6 处的查询和键元素，而线程 1 处理索引为 1、3、5、7。
- **块\*\***(Block)：\*_vLLM 中的键和值缓存数据被分分割成块。每个块在一个头中存储固定数量 (`BLOCK_SIZE`) 的 token 的数据。每个块可能仅包含整个上下文 token 的一部分。例如，如果块大小为 16，头大小为 128，那么对于 1 个头，1 个块可以存储 16 _ 128 = 2048 个元素。
- **Warp**：Warp 是一组 32 个线程 (`WARP_SIZE`)，它们在流多处理器 (SM) 上同时执行。在这个内核中，每个 warp 同时处理一个查询 token 与一个完整块的键 token 之间的计算（它可以在多次迭代中处理多个块）。例如，如果有 4 个 warp 和 6 个块用于一个上下文，那么分配方式将是：warp 0 处理第 0 和第 4 块，warp 1 处理第 1 和第 5 块，warp 2 处理第 2 个块，warp 3 处理第 3 个块。
- **线程块** **(\*\***Thread block\***\*)：**线程块是一组可以访问同一共享内存的线程 (`NUM_THREADS`)。每个线程块包含多个 warp (NUM_WARPS)，在这个内核中，每个线程块处理一个查询 token 和整个上下文的键 token 之间的计算。
- **网格\*\***(Grid)：\*\*网格是线程块的集合，并定义了集合的形状。在此内核中，形状为 `(num_heads, num_seqs, max_num_partitions)`。因此，每个线程块只处理 1 个头、1 个序列、1 个分区的计算。
