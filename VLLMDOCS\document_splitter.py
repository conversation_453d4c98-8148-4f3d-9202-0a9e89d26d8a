#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VLLM文档切分程序
用于将VLLM文档切分为适合向量检索的文档块
"""

import os
import re
import json
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import argparse


@dataclass
class DocumentSection:
    """文档章节数据结构"""
    level: int  # 标题级别 (1, 2, 3)
    title: str  # 标题文本
    content: str  # 章节内容
    line_start: int  # 起始行号
    line_end: int  # 结束行号


@dataclass
class DocumentChunk:
    """文档块数据结构"""
    content: str  # 完整内容（包含元数据和正文）
    metadata: Dict  # 元数据信息
    source_file: str  # 源文件路径
    chunk_id: str  # 块ID


class DocumentParser:
    """Markdown文档解析器"""
    
    def __init__(self):
        self.heading_pattern = re.compile(r'^(#{1,6})\s+(.+)$', re.MULTILINE)
        self.frontmatter_pattern = re.compile(r'^---\n(.*?)\n---\n', re.DOTALL)
    
    def parse_document(self, content: str, file_path: str) -> Tuple[Dict, List[DocumentSection]]:
        """
        解析markdown文档
        
        Args:
            content: 文档内容
            file_path: 文件路径
            
        Returns:
            (frontmatter, sections): 前置元数据和章节列表
        """
        # 提取前置元数据
        frontmatter = self._extract_frontmatter(content)
        
        # 移除前置元数据
        content_without_frontmatter = self._remove_frontmatter(content)
        
        # 解析章节
        sections = self._parse_sections(content_without_frontmatter)
        
        return frontmatter, sections
    
    def _extract_frontmatter(self, content: str) -> Dict:
        """提取YAML前置元数据"""
        match = self.frontmatter_pattern.match(content)
        if match:
            try:
                # 简单解析title字段
                frontmatter_text = match.group(1)
                frontmatter = {}
                for line in frontmatter_text.split('\n'):
                    if ':' in line:
                        key, value = line.split(':', 1)
                        frontmatter[key.strip()] = value.strip().strip('"\'')
                return frontmatter
            except:
                return {}
        return {}
    
    def _remove_frontmatter(self, content: str) -> str:
        """移除前置元数据"""
        match = self.frontmatter_pattern.match(content)
        if match:
            return content[match.end():]
        return content
    
    def _parse_sections(self, content: str) -> List[DocumentSection]:
        """解析文档章节，正确处理代码块和前置内容"""
        lines = content.split('\n')
        sections = []

        # 找到所有标题，但排除代码块中的标题
        headings = []
        in_code_block = False
        code_block_delimiter = None

        for i, line in enumerate(lines):
            # 检查代码块的开始和结束
            stripped_line = line.strip()
            if stripped_line.startswith('```'):
                if not in_code_block:
                    # 进入代码块
                    in_code_block = True
                    code_block_delimiter = '```'
                elif code_block_delimiter == '```':
                    # 退出代码块
                    in_code_block = False
                    code_block_delimiter = None
                continue
            elif stripped_line.startswith('~~~'):
                if not in_code_block:
                    # 进入代码块
                    in_code_block = True
                    code_block_delimiter = '~~~'
                elif code_block_delimiter == '~~~':
                    # 退出代码块
                    in_code_block = False
                    code_block_delimiter = None
                continue

            # 只在非代码块中识别标题
            if not in_code_block:
                match = re.match(r'^(#{1,6})\s+(.+)$', line)
                if match:
                    level = len(match.group(1))
                    title = match.group(2).strip()
                    headings.append((i, level, title))

        # 如果没有标题，整个文档作为一个章节
        if not headings:
            return [DocumentSection(
                level=1,
                title="文档内容",
                content=content,
                line_start=0,
                line_end=len(lines) - 1
            )]

        # 处理第一个标题前的前置内容
        first_heading_line = headings[0][0]
        if first_heading_line > 0:
            # 检查前置内容是否有实质内容（不只是空行）
            preamble_lines = lines[:first_heading_line]
            preamble_content = '\n'.join(preamble_lines).strip()

            if preamble_content:  # 如果有实质内容
                sections.append(DocumentSection(
                    level=0,  # 使用特殊级别0表示前置内容
                    title="前置内容",
                    content=preamble_content,
                    line_start=0,
                    line_end=first_heading_line - 1
                ))

        # 为每个标题创建章节
        for i, (line_num, level, title) in enumerate(headings):
            start_line = line_num

            # 确定章节结束位置
            if i + 1 < len(headings):
                end_line = headings[i + 1][0] - 1
            else:
                end_line = len(lines) - 1

            # 提取章节内容
            section_lines = lines[start_line:end_line + 1]
            section_content = '\n'.join(section_lines)

            sections.append(DocumentSection(
                level=level,
                title=title,
                content=section_content,
                line_start=start_line,
                line_end=end_line
            ))

        return sections


class DocumentSplitter:
    """文档切分器"""
    
    def __init__(self):
        pass
    
    def split_document(self, sections: List[DocumentSection], file_path: str, frontmatter: Dict) -> List[DocumentChunk]:
        """
        根据层级关系切分文档

        新策略：
        1. 保持标题的层级关系
        2. 优先使用三级标题（###）作为切分单位，但包含其上级标题
        3. 如果没有三级标题，使用二级标题（##），但包含其上级标题
        4. 如果没有二级标题，使用一级标题（#）
        5. 前置内容与第一个标题合并
        """
        if not sections:
            return []

        # 分析标题层级
        levels = [section.level for section in sections if section.level > 0]

        # 确定切分级别
        if 3 in levels:
            split_level = 3
        elif 2 in levels:
            split_level = 2
        elif 1 in levels:
            split_level = 1
        else:
            # 只有前置内容，整个文档作为一个块
            chunks = self._create_chunk(sections, file_path, frontmatter)
            return [chunks] if chunks else []

        # 按层级关系切分
        chunks = self._split_by_hierarchy(sections, split_level, file_path, frontmatter)

        return chunks
    
    def _split_by_hierarchy(self, sections: List[DocumentSection], target_level: int,
                           file_path: str, frontmatter: Dict) -> List[DocumentChunk]:
        """按层级关系切分文档，保持上下级标题的关系"""
        chunks = []

        # 处理前置内容 - 单独成块
        preamble_sections = [s for s in sections if s.level == 0]
        if preamble_sections:
            # 前置内容单独创建一个块
            chunk = self._create_chunk(preamble_sections[:1], file_path, frontmatter)
            if chunk:
                chunks.append(chunk)

            # 检查是否只有前置内容
            first_real_section_idx = next((i for i, s in enumerate(sections) if s.level > 0), len(sections))
            if first_real_section_idx >= len(sections):
                return chunks

        # 找到所有目标级别的标题
        target_sections = [(i, s) for i, s in enumerate(sections) if s.level == target_level]

        if not target_sections:
            # 没有目标级别的标题，整个文档作为一个块（排除前置内容）
            non_preamble_sections = [s for s in sections if s.level > 0]
            if non_preamble_sections:
                chunk = self._create_chunk(non_preamble_sections, file_path, frontmatter)
                if chunk:
                    chunks.append(chunk)
            return chunks

        # 为每个目标级别的标题创建块
        for i, (section_idx, target_section) in enumerate(target_sections):
            chunk_sections = []

            # 添加上级标题
            parent_sections = self._get_parent_sections(sections, section_idx, target_level)
            chunk_sections.extend(parent_sections)

            # 添加目标标题
            chunk_sections.append(target_section)

            # 添加子标题（直到下一个同级或更高级标题）
            next_section_idx = len(sections)
            # 找到下一个同级或更高级标题的位置
            for j in range(section_idx + 1, len(sections)):
                if sections[j].level <= target_level:
                    next_section_idx = j
                    break

            # 添加所有子标题
            for j in range(section_idx + 1, next_section_idx):
                chunk_sections.append(sections[j])

            # 创建块
            chunk = self._create_chunk(chunk_sections, file_path, frontmatter)
            if chunk:
                chunks.append(chunk)

        return chunks

    def _get_parent_sections(self, sections: List[DocumentSection], target_idx: int, target_level: int) -> List[DocumentSection]:
        """获取目标标题的所有上级标题"""
        parent_sections = []

        # 从目标位置向前查找上级标题
        for i in range(target_idx - 1, -1, -1):
            section = sections[i]
            if section.level == 0:  # 跳过前置内容
                continue
            if section.level < target_level:
                # 检查是否已经有这个级别的标题
                if not any(p.level == section.level for p in parent_sections):
                    parent_sections.insert(0, section)
                # 如果找到了一级标题，停止查找
                if section.level == 1:
                    break

        return parent_sections
    
    def _create_chunk(self, sections: List[DocumentSection], file_path: str, 
                     frontmatter: Dict) -> Optional[DocumentChunk]:
        """创建文档块"""
        if not sections:
            return None
        
        # 合并章节内容
        content_parts = []
        for section in sections:
            content_parts.append(section.content)
        
        chunk_content = '\n\n'.join(content_parts)
        
        # 生成元数据
        metadata = self._generate_metadata(sections, file_path, frontmatter)
        
        # 生成块ID
        chunk_id = self._generate_chunk_id(file_path, sections[0])
        
        # 生成最终内容（元数据 + 正文）
        final_content = self._format_chunk_content(metadata, chunk_content)
        
        return DocumentChunk(
            content=final_content,
            metadata=metadata,
            source_file=file_path,
            chunk_id=chunk_id
        )
    
    def _generate_metadata(self, sections: List[DocumentSection], file_path: str,
                          frontmatter: Dict) -> Dict:
        """生成元数据"""
        path_parts = Path(file_path).parts

        # 提取目录层级
        directory_path = []
        filename = ""

        for part in path_parts:
            if part.endswith('.md'):
                filename = part[:-3]  # 移除.md扩展名
            elif part not in ['VLLMDOCS', 'version-0.8.x']:
                directory_path.append(part)

        # 构建标题层级
        title_hierarchy = []
        if frontmatter.get('title'):
            title_hierarchy.append(frontmatter['title'])

        # 添加各级标题，跳过前置内容
        main_title = ""
        section_level = 1
        target_section = None  # 目标标题（最后一个最高级别的标题）

        for section in sections:
            if section.level > 0 and section.level <= 3:  # 只记录到三级标题，跳过前置内容
                title_hierarchy.append(section.title)
                if not main_title:  # 第一个真正的标题作为主标题
                    main_title = section.title
                    section_level = section.level
                # 更新目标标题为最后一个标题（通常是最具体的标题）
                target_section = section

        # 如果有目标标题，使用它作为主标题
        if target_section:
            main_title = target_section.title
            section_level = target_section.level

        # 如果没有真正的标题，使用前置内容或文档标题
        if not main_title:
            if sections and sections[0].level == 0:
                main_title = "前置内容"
                section_level = 0
            else:
                main_title = frontmatter.get('title', '文档内容')
                section_level = 1

        return {
            'source_file': file_path,
            'directory_path': ' > '.join(directory_path) if directory_path else '',
            'filename': filename,
            'title_hierarchy': ' > '.join(title_hierarchy),
            'main_title': main_title,
            'section_level': section_level,
            'document_title': frontmatter.get('title', ''),
        }
    
    def _generate_chunk_id(self, file_path: str, first_section: DocumentSection) -> str:
        """生成块ID"""
        path_hash = hash(file_path) % 10000
        title_hash = hash(first_section.title) % 10000
        return f"{path_hash:04d}_{title_hash:04d}"
    
    def _format_chunk_content(self, metadata: Dict, content: str) -> str:
        """格式化块内容"""
        # 构建完整的元数据路径
        path_parts = []

        # 添加目录路径
        if metadata['directory_path']:
            path_parts.append(metadata['directory_path'])

        # 添加文件名
        path_parts.append(metadata['filename'])

        # 使用完整的标题层级
        if metadata['title_hierarchy']:
            path_parts.append(metadata['title_hierarchy'])

        metadata_line = f"# 文档路径: {' > '.join(path_parts)}"

        return f"{metadata_line}\n\n{content}"


class FileProcessor:
    """文件处理器"""
    
    def __init__(self, source_dir: str, output_dir: str):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.parser = DocumentParser()
        self.splitter = DocumentSplitter()
    
    def process_all_documents(self):
        """处理所有文档"""
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 遍历所有md文件
        md_files = list(self.source_dir.rglob('*.md'))
        
        print(f"找到 {len(md_files)} 个markdown文件")
        
        all_chunks = []
        
        for md_file in md_files:
            print(f"处理文件: {md_file}")
            chunks = self._process_single_file(md_file)
            all_chunks.extend(chunks)
        
        # 保存所有块
        self._save_chunks(all_chunks)
        
        print(f"处理完成，共生成 {len(all_chunks)} 个文档块")
    
    def _process_single_file(self, file_path: Path) -> List[DocumentChunk]:
        """处理单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析文档
            frontmatter, sections = self.parser.parse_document(content, str(file_path))
            
            # 切分文档
            chunks = self.splitter.split_document(sections, str(file_path), frontmatter)
            
            return chunks
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            return []
    
    def _save_chunks(self, chunks: List[DocumentChunk]):
        """保存文档块"""
        # 保存为单独的文本文件
        for i, chunk in enumerate(chunks):
            chunk_file = self.output_dir / f"chunk_{i+1:04d}_{chunk.chunk_id}.txt"
            with open(chunk_file, 'w', encoding='utf-8') as f:
                f.write(chunk.content)
        
        # 保存元数据索引
        metadata_list = []
        for i, chunk in enumerate(chunks):
            metadata_list.append({
                'chunk_id': chunk.chunk_id,
                'file_name': f"chunk_{i+1:04d}_{chunk.chunk_id}.txt",
                'metadata': chunk.metadata
            })
        
        metadata_file = self.output_dir / 'chunks_metadata.json'
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata_list, f, ensure_ascii=False, indent=2)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='VLLM文档切分程序')
    parser.add_argument('--source', default='VLLMDOCS/version-0.8.x', 
                       help='源文档目录 (默认: VLLMDOCS/version-0.8.x)')
    parser.add_argument('--output', default='VLLMDOCS/result', 
                       help='输出目录 (默认: VLLMDOCS/result)')
    
    args = parser.parse_args()
    
    # 检查源目录是否存在
    if not os.path.exists(args.source):
        print(f"错误: 源目录 {args.source} 不存在")
        return
    
    # 创建文件处理器并处理文档
    processor = FileProcessor(args.source, args.output)
    processor.process_all_documents()


if __name__ == '__main__':
    main()
