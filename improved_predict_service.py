"""
改进的预测服务

使用混合规则+机器学习模型的预测服务
"""

import argparse
import json
import os
import pandas as pd
import logging
from typing import List, Dict, Any, Optional
import joblib
import re
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import LabelEncoder

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ImprovedPredictionService:
    """
    改进的预测服务类
    
    使用混合规则+ML模型
    """
    
    def __init__(self, model_path: str):
        """
        初始化预测服务
        
        Args:
            model_path: 模型路径
        """
        self.model_path = model_path
        self.ml_model = None
        self.vectorizer = None
        self.label_encoder = None
        self.config = None
        self.precise_rules = {}
        self.keyword_weights = {}
        self.label_mapping = {}
        
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        try:
            logger.info(f"加载混合模型: {self.model_path}")
            
            # 加载配置
            config_path = os.path.join(self.model_path, 'config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            self.precise_rules = self.config['precise_rules']
            self.keyword_weights = self.config['keyword_weights']
            self.label_mapping = self.config['label_mapping']
            
            # 加载ML组件
            ml_model_path = os.path.join(self.model_path, 'ml_model.pkl')
            if os.path.exists(ml_model_path):
                self.ml_model = joblib.load(ml_model_path)
            
            vectorizer_path = os.path.join(self.model_path, 'vectorizer.pkl')
            if os.path.exists(vectorizer_path):
                self.vectorizer = joblib.load(vectorizer_path)
            
            label_encoder_path = os.path.join(self.model_path, 'label_encoder.pkl')
            if os.path.exists(label_encoder_path):
                self.label_encoder = joblib.load(label_encoder_path)
            
            logger.info("混合模型加载完成")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def rule_based_predict(self, text: str) -> Optional[tuple]:
        """
        基于规则的预测
        
        Args:
            text: 输入文本
            
        Returns:
            (预测标签, 置信度) 或 None
        """
        text_lower = text.lower()
        
        best_match = None
        best_score = 0
        
        for label, patterns in self.precise_rules.items():
            score = 0
            matched_patterns = 0
            
            for pattern in patterns:
                if re.search(pattern, text_lower, re.IGNORECASE):
                    matched_patterns += 1
                    pattern_score = len(pattern.split('.*')) * 2
                    score += pattern_score
            
            if matched_patterns > 0:
                final_score = score * (matched_patterns / len(patterns))
                if final_score > best_score:
                    best_score = final_score
                    best_match = label
        
        if best_score >= 2.0:
            confidence = min(0.95, 0.5 + best_score * 0.1)
            return best_match, confidence
        
        return None
    
    def extract_ml_features(self, text: str) -> Dict[str, float]:
        """
        为机器学习提取特征
        
        Args:
            text: 输入文本
            
        Returns:
            特征字典
        """
        features = {}
        text_lower = text.lower()
        
        # 关键词特征
        for keyword, weight in self.keyword_weights.items():
            count = text_lower.count(keyword.lower())
            features[f'keyword_{keyword}'] = count * weight
        
        # 数字特征
        numbers = re.findall(r'\d+', text)
        features['number_count'] = len(numbers)
        features['has_600'] = 1 if '600' in text else 0
        features['has_http_code'] = 1 if any(n in ['400', '401', '403', '404', '500'] for n in numbers) else 0
        
        # 结构特征
        features['has_json'] = 1 if '{' in text or '}' in text else 0
        features['has_quotes'] = text.count('"') + text.count("'")
        features['text_length'] = len(text)
        features['word_count'] = len(text.split())
        
        # 语言特征
        chinese_chars = len(re.findall(r'[\u4e00-\u9fa5]', text))
        features['chinese_ratio'] = chinese_chars / len(text) if text else 0
        
        return features
    
    def ml_predict(self, text: str) -> tuple:
        """
        机器学习预测
        
        Args:
            text: 输入文本
            
        Returns:
            (预测标签, 置信度)
        """
        if self.ml_model is None or self.vectorizer is None:
            return "未知错误", 0.0
        
        try:
            # 特征提取
            tfidf_features = self.vectorizer.transform([text])
            manual_features = np.array([list(self.extract_ml_features(text).values())])
            X = np.hstack([tfidf_features.toarray(), manual_features])
            
            # 预测
            pred_proba = self.ml_model.predict_proba(X)[0]
            pred_class = np.argmax(pred_proba)
            
            # 转换为字符串键
            str_label_mapping = {str(k): v for k, v in self.label_mapping.items()}
            predicted_label = str_label_mapping.get(str(pred_class), "未知错误")
            
            return predicted_label, pred_proba[pred_class]
            
        except Exception as e:
            logger.error(f"ML预测失败: {e}")
            return "预测失败", 0.0
    
    def predict_single(self, text: str, explain: bool = False) -> Dict[str, Any]:
        """
        单条预测
        
        Args:
            text: 输入文本
            explain: 是否提供解释
            
        Returns:
            预测结果
        """
        # 首先尝试规则预测
        rule_result = self.rule_based_predict(text)
        if rule_result:
            predicted_label, confidence = rule_result
            method = "规则匹配"
        else:
            # 使用ML模型
            predicted_label, confidence = self.ml_predict(text)
            method = "机器学习"
        
        result = {
            'predicted_label': predicted_label,
            'confidence': float(confidence),
            'prediction_method': method,
            'is_confident': confidence > 0.5
        }
        
        if explain:
            result['explanation'] = self._explain_prediction(text, predicted_label, method)
        
        return result
    
    def _explain_prediction(self, text: str, predicted_label: str, method: str) -> Dict[str, Any]:
        """
        解释预测结果
        
        Args:
            text: 输入文本
            predicted_label: 预测标签
            method: 预测方法
            
        Returns:
            解释信息
        """
        explanation = {
            'prediction_method': method,
            'matched_rules': [],
            'key_indicators': []
        }
        
        # 找出匹配的规则
        if method == "规则匹配":
            for label, patterns in self.precise_rules.items():
                if label == predicted_label:
                    for pattern in patterns:
                        if re.search(pattern, text.lower(), re.IGNORECASE):
                            explanation['matched_rules'].append(pattern)
        
        # 提取关键词
        text_lower = text.lower()
        for keyword, weight in self.keyword_weights.items():
            if keyword.lower() in text_lower and weight >= 5.0:
                explanation['key_indicators'].append(keyword)
        
        return explanation
    
    def predict_batch(self, texts: List[str], explain: bool = False) -> List[Dict[str, Any]]:
        """
        批量预测
        
        Args:
            texts: 文本列表
            explain: 是否提供解释
            
        Returns:
            预测结果列表
        """
        results = []
        for text in texts:
            result = self.predict_single(text, explain)
            results.append(result)
        return results
    
    def predict_from_file(self, 
                         input_path: str, 
                         output_path: str,
                         text_column: str = '响应内容',
                         explain: bool = False) -> Dict[str, Any]:
        """
        从文件读取数据进行预测
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            text_column: 文本列名
            explain: 是否提供解释
            
        Returns:
            预测统计信息
        """
        logger.info(f"从文件预测: {input_path}")
        
        # 读取数据
        if input_path.endswith('.xlsx'):
            df = pd.read_excel(input_path)
        elif input_path.endswith('.csv'):
            df = pd.read_csv(input_path)
        else:
            raise ValueError("不支持的文件格式")
        
        if text_column not in df.columns:
            raise ValueError(f"文件中不存在列: {text_column}")
        
        texts = df[text_column].astype(str).tolist()
        
        # 批量预测
        results = self.predict_batch(texts, explain=explain)
        
        # 构建输出数据
        output_data = []
        rule_count = 0
        ml_count = 0
        confident_count = 0
        
        for i, (text, result) in enumerate(zip(texts, results)):
            row = df.iloc[i].to_dict()  # 保留原始数据
            row.update({
                '预测类别': result['predicted_label'],
                '置信度': result['confidence'],
                '预测方法': result['prediction_method'],
                '是否可信': result['is_confident']
            })
            
            # 统计
            if result['prediction_method'] == '规则匹配':
                rule_count += 1
            else:
                ml_count += 1
            
            if result['is_confident']:
                confident_count += 1
            
            # 添加解释信息
            if explain and 'explanation' in result:
                exp = result['explanation']
                row['匹配规则'] = '; '.join(exp['matched_rules'][:3])
                row['关键指示词'] = '; '.join(exp['key_indicators'])
            
            output_data.append(row)
        
        # 保存结果
        output_df = pd.DataFrame(output_data)
        output_df.to_excel(output_path, index=False)
        
        # 统计信息
        stats = {
            'total_predictions': len(results),
            'rule_predictions': rule_count,
            'ml_predictions': ml_count,
            'confident_predictions': confident_count,
            'rule_ratio': rule_count / len(results),
            'confident_ratio': confident_count / len(results),
            'input_file': input_path,
            'output_file': output_path
        }
        
        logger.info(f"预测完成，结果保存到: {output_path}")
        logger.info(f"总预测数: {stats['total_predictions']}")
        logger.info(f"规则预测: {rule_count} ({rule_count/len(results)*100:.1f}%)")
        logger.info(f"ML预测: {ml_count} ({ml_count/len(results)*100:.1f}%)")
        logger.info(f"可信预测: {confident_count} ({confident_count/len(results)*100:.1f}%)")
        
        return stats
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        info = {
            'model_path': self.model_path,
            'model_type': '混合规则+机器学习模型',
            'rule_count': len(self.precise_rules),
            'has_ml_model': self.ml_model is not None,
            'supported_classes': list(self.precise_rules.keys())
        }
        
        return info

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='改进的应用服务报错根因分析预测服务')
    parser.add_argument('--model', type=str, default='models/hybrid_classifier',
                       help='模型路径')
    parser.add_argument('--mode', type=str, choices=['single', 'batch', 'file'], default='single',
                       help='预测模式')
    parser.add_argument('--text', type=str, default=None,
                       help='单条预测的文本内容')
    parser.add_argument('--input', type=str, default=None,
                       help='输入文件路径（batch或file模式）')
    parser.add_argument('--output', type=str, default=None,
                       help='输出文件路径')
    parser.add_argument('--text-column', type=str, default='响应内容',
                       help='文本列名（file模式）')
    parser.add_argument('--explain', action='store_true',
                       help='是否提供预测解释')
    parser.add_argument('--info', action='store_true',
                       help='显示模型信息')
    
    args = parser.parse_args()
    
    try:
        # 创建预测服务
        service = ImprovedPredictionService(args.model)
        
        # 显示模型信息
        if args.info:
            info = service.get_model_info()
            print("=" * 60)
            print("混合模型信息")
            print("=" * 60)
            print(f"模型路径: {info['model_path']}")
            print(f"模型类型: {info['model_type']}")
            print(f"规则数量: {info['rule_count']}")
            print(f"ML模型: {'是' if info['has_ml_model'] else '否'}")
            print(f"支持类别数: {len(info['supported_classes'])}")
            print("=" * 60)
            return
        
        # 执行预测
        if args.mode == 'single':
            if not args.text:
                args.text = input("请输入要预测的文本: ")
            
            result = service.predict_single(args.text, explain=args.explain)
            
            print("=" * 60)
            print("预测结果")
            print("=" * 60)
            print(f"输入文本: {args.text}")
            print(f"预测类别: {result['predicted_label']}")
            print(f"置信度: {result['confidence']:.3f}")
            print(f"预测方法: {result['prediction_method']}")
            print(f"是否可信: {'是' if result['is_confident'] else '否'}")
            
            if args.explain and 'explanation' in result:
                exp = result['explanation']
                print(f"\n预测解释:")
                print(f"  方法: {exp['prediction_method']}")
                if exp['matched_rules']:
                    print(f"  匹配规则: {exp['matched_rules'][:3]}")
                if exp['key_indicators']:
                    print(f"  关键指示词: {exp['key_indicators']}")
            
            print("=" * 60)
        
        elif args.mode == 'file':
            if not args.input:
                raise ValueError("file模式需要指定输入文件")
            if not args.output:
                args.output = args.input.replace('.xlsx', '_predicted.xlsx').replace('.csv', '_predicted.csv')
            
            stats = service.predict_from_file(
                args.input, args.output, args.text_column, explain=args.explain
            )
            
            print("=" * 60)
            print("批量预测统计")
            print("=" * 60)
            print(f"输入文件: {stats['input_file']}")
            print(f"输出文件: {stats['output_file']}")
            print(f"预测总数: {stats['total_predictions']}")
            print(f"规则预测: {stats['rule_predictions']} ({stats['rule_ratio']*100:.1f}%)")
            print(f"ML预测: {stats['ml_predictions']} ({(1-stats['rule_ratio'])*100:.1f}%)")
            print(f"可信预测: {stats['confident_predictions']} ({stats['confident_ratio']*100:.1f}%)")
            print("=" * 60)
        
    except Exception as e:
        logger.error(f"预测失败: {e}")
        raise

if __name__ == "__main__":
    main()
