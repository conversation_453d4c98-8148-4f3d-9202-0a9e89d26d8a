"""
句子Embedding + 分类器方案

使用预训练的句子embedding模型提取特征，然后训练分类器
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import classification_report, accuracy_score
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sentence_transformers import SentenceTransformer
import logging
from typing import List, Dict, Tuple, Any
import joblib
import os
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmbeddingClassifier:
    """
    基于句子Embedding的错误分析器
    
    核心思路：
    1. 使用预训练的句子embedding模型将文本转换为向量
    2. 训练传统分类器对向量进行分类
    3. 利用embedding的语义理解能力
    """
    
    def __init__(self, embedding_model_name='all-MiniLM-L6-v2'):
        """
        初始化embedding分类器
        
        Args:
            embedding_model_name: 预训练embedding模型名称
        """
        self.embedding_model_name = embedding_model_name
        self.embedding_model = None
        self.classifier = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.label_mapping = {}
        
        # 加载embedding模型
        self._load_embedding_model()
    
    def _load_embedding_model(self):
        """加载预训练的embedding模型"""
        try:
            logger.info(f"加载embedding模型: {self.embedding_model_name}")
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            logger.info(f"模型加载成功，embedding维度: {self.embedding_model.get_sentence_embedding_dimension()}")
        except Exception as e:
            logger.error(f"加载embedding模型失败: {e}")
            # 如果加载失败，尝试使用中文模型
            try:
                logger.info("尝试使用中文embedding模型...")
                self.embedding_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
                logger.info("中文embedding模型加载成功")
            except Exception as e2:
                logger.error(f"所有embedding模型加载失败: {e2}")
                raise
    
    def extract_embeddings(self, texts: List[str]) -> np.ndarray:
        """
        提取文本的embedding特征
        
        Args:
            texts: 文本列表
            
        Returns:
            embedding矩阵
        """
        logger.info(f"提取 {len(texts)} 条文本的embedding...")
        
        # 文本预处理
        processed_texts = []
        for text in texts:
            # 基本清洗
            text = str(text).strip()
            processed_texts.append(text)
        
        # 提取embedding
        embeddings = self.embedding_model.encode(
            processed_texts,
            show_progress_bar=True,
            convert_to_numpy=True
        )
        
        logger.info(f"Embedding提取完成，形状: {embeddings.shape}")
        return embeddings
    
    def train_classifiers(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练多个分类器并选择最佳
        
        Args:
            X: embedding特征矩阵
            y: 标签
            
        Returns:
            训练结果
        """
        # 特征标准化
        X_scaled = self.scaler.fit_transform(X)
        
        # 定义分类器
        classifiers = {
            'LogisticRegression': LogisticRegression(
                max_iter=2000,
                random_state=42,
                class_weight='balanced',
                C=1.0
            ),
            'RandomForest': RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=3,
                min_samples_leaf=2,
                random_state=42,
                class_weight='balanced'
            ),
            'SVM': SVC(
                kernel='rbf',
                random_state=42,
                class_weight='balanced',
                probability=True,
                C=1.0,
                gamma='scale'
            ),
            'KNN': KNeighborsClassifier(
                n_neighbors=min(5, len(np.unique(y))),  # 邻居数不超过类别数
                weights='distance',
                metric='cosine'  # 余弦相似度，适合embedding
            )
        }
        
        results = {}
        
        # 交叉验证评估
        cv_folds = min(3, len(np.unique(y)))
        cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
        
        logger.info(f"使用{cv_folds}折交叉验证评估分类器:")
        logger.info("=" * 60)
        
        for name, classifier in classifiers.items():
            try:
                # 交叉验证
                cv_scores = cross_val_score(
                    classifier, X_scaled, y, 
                    cv=cv, scoring='accuracy'
                )
                
                # 训练完整模型
                classifier.fit(X_scaled, y)
                
                results[name] = {
                    'classifier': classifier,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'cv_scores': cv_scores
                }
                
                logger.info(f"{name:18s}: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
                
            except Exception as e:
                logger.warning(f"{name} 训练失败: {e}")
                continue
        
        # 选择最佳分类器
        if results:
            best_name = max(results.keys(), key=lambda k: results[k]['cv_mean'])
            self.classifier = results[best_name]['classifier']
            logger.info(f"\n选择最佳分类器: {best_name}")
            logger.info(f"交叉验证准确率: {results[best_name]['cv_mean']:.3f}")
        
        return results
    
    def train(self, texts: List[str], labels: List[str]) -> Dict[str, Any]:
        """
        训练embedding分类器
        
        Args:
            texts: 文本列表
            labels: 标签列表
            
        Returns:
            训练结果
        """
        logger.info("开始训练Embedding分类器...")
        logger.info(f"数据量: {len(texts)}, 类别数: {len(set(labels))}")
        
        # 1. 标签编码
        y = self.label_encoder.fit_transform(labels)
        self.label_mapping = {i: label for i, label in enumerate(self.label_encoder.classes_)}
        
        # 2. 提取embedding特征
        X = self.extract_embeddings(texts)
        
        # 3. 训练分类器
        classifier_results = self.train_classifiers(X, y)
        
        # 4. 在训练集上评估
        if self.classifier is not None:
            X_scaled = self.scaler.transform(X)
            y_pred = self.classifier.predict(X_scaled)
            train_accuracy = accuracy_score(y, y_pred)
            
            logger.info(f"\n训练集准确率: {train_accuracy:.3f}")
            
            # 详细分类报告
            target_names = self.label_encoder.classes_
            report = classification_report(y, y_pred, target_names=target_names, zero_division=0)
            logger.info("\n分类报告:")
            logger.info(report)
        
        return {
            'classifier_results': classifier_results,
            'embedding_dim': X.shape[1],
            'train_accuracy': train_accuracy if self.classifier else 0,
            'label_mapping': self.label_mapping
        }
    
    def predict(self, text: str) -> Dict[str, Any]:
        """
        预测单条文本
        
        Args:
            text: 输入文本
            
        Returns:
            预测结果
        """
        if self.classifier is None:
            raise ValueError("分类器未训练")
        
        # 提取embedding
        embedding = self.extract_embeddings([text])
        X_scaled = self.scaler.transform(embedding)
        
        # 预测
        pred_proba = self.classifier.predict_proba(X_scaled)[0]
        pred_class = np.argmax(pred_proba)
        
        # 获取Top-5预测
        top_indices = np.argsort(pred_proba)[::-1][:5]
        top_predictions = [(self.label_mapping[i], pred_proba[i]) for i in top_indices]
        
        return {
            'predicted_label': self.label_mapping[pred_class],
            'confidence': pred_proba[pred_class],
            'top_5_predictions': top_predictions,
            'prediction_method': 'Embedding分类器',
            'is_confident': pred_proba[pred_class] > 0.3
        }
    
    def predict_batch(self, texts: List[str]) -> List[Dict[str, Any]]:
        """
        批量预测
        
        Args:
            texts: 文本列表
            
        Returns:
            预测结果列表
        """
        if self.classifier is None:
            raise ValueError("分类器未训练")
        
        # 批量提取embedding
        embeddings = self.extract_embeddings(texts)
        X_scaled = self.scaler.transform(embeddings)
        
        # 批量预测
        pred_probas = self.classifier.predict_proba(X_scaled)
        
        results = []
        for i, pred_proba in enumerate(pred_probas):
            pred_class = np.argmax(pred_proba)
            
            # Top-5预测
            top_indices = np.argsort(pred_proba)[::-1][:5]
            top_predictions = [(self.label_mapping[j], pred_proba[j]) for j in top_indices]
            
            result = {
                'predicted_label': self.label_mapping[pred_class],
                'confidence': pred_proba[pred_class],
                'top_5_predictions': top_predictions,
                'prediction_method': 'Embedding分类器',
                'is_confident': pred_proba[pred_class] > 0.3
            }
            results.append(result)
        
        return results
    
    def analyze_embeddings(self, texts: List[str], labels: List[str]) -> Dict[str, Any]:
        """
        分析embedding的质量
        
        Args:
            texts: 文本列表
            labels: 标签列表
            
        Returns:
            分析结果
        """
        logger.info("分析embedding质量...")
        
        # 提取embedding
        embeddings = self.extract_embeddings(texts)
        
        # 计算类内和类间距离
        from sklearn.metrics.pairwise import cosine_similarity
        
        analysis = {}
        
        # 按类别分组
        label_groups = {}
        for i, label in enumerate(labels):
            if label not in label_groups:
                label_groups[label] = []
            label_groups[label].append(i)
        
        # 计算类内相似度
        intra_class_similarities = []
        for label, indices in label_groups.items():
            if len(indices) > 1:
                class_embeddings = embeddings[indices]
                similarities = cosine_similarity(class_embeddings)
                # 取上三角矩阵（排除对角线）
                upper_tri = similarities[np.triu_indices_from(similarities, k=1)]
                intra_class_similarities.extend(upper_tri)
        
        # 计算类间相似度
        inter_class_similarities = []
        labels_list = list(label_groups.keys())
        for i in range(len(labels_list)):
            for j in range(i+1, len(labels_list)):
                label1, label2 = labels_list[i], labels_list[j]
                indices1, indices2 = label_groups[label1], label_groups[label2]
                
                emb1 = embeddings[indices1]
                emb2 = embeddings[indices2]
                
                similarities = cosine_similarity(emb1, emb2)
                inter_class_similarities.extend(similarities.flatten())
        
        analysis = {
            'embedding_dim': embeddings.shape[1],
            'avg_intra_class_similarity': np.mean(intra_class_similarities) if intra_class_similarities else 0,
            'avg_inter_class_similarity': np.mean(inter_class_similarities) if inter_class_similarities else 0,
            'separability_score': (np.mean(intra_class_similarities) - np.mean(inter_class_similarities)) if intra_class_similarities and inter_class_similarities else 0
        }
        
        logger.info(f"Embedding分析结果:")
        logger.info(f"  维度: {analysis['embedding_dim']}")
        logger.info(f"  类内平均相似度: {analysis['avg_intra_class_similarity']:.3f}")
        logger.info(f"  类间平均相似度: {analysis['avg_inter_class_similarity']:.3f}")
        logger.info(f"  可分离性得分: {analysis['separability_score']:.3f}")
        
        return analysis
    
    def save_model(self, save_path: str):
        """保存模型"""
        os.makedirs(save_path, exist_ok=True)
        
        # 保存分类器和预处理器
        joblib.dump(self.classifier, os.path.join(save_path, 'classifier.pkl'))
        joblib.dump(self.scaler, os.path.join(save_path, 'scaler.pkl'))
        joblib.dump(self.label_encoder, os.path.join(save_path, 'label_encoder.pkl'))
        
        # 保存配置
        config = {
            'embedding_model_name': self.embedding_model_name,
            'label_mapping': self.label_mapping
        }
        
        with open(os.path.join(save_path, 'config.json'), 'w', encoding='utf-8') as f:
            import json
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Embedding分类器已保存到: {save_path}")

def main():
    """主函数"""
    # 读取数据
    df = pd.read_excel('processed_data.xlsx')
    texts = df['响应内容'].astype(str).tolist()
    labels = df['报错原因'].astype(str).tolist()
    
    # 创建embedding分类器
    classifier = EmbeddingClassifier()
    
    # 分析embedding质量
    embedding_analysis = classifier.analyze_embeddings(texts, labels)
    
    # 训练模型
    results = classifier.train(texts, labels)
    
    # 保存模型
    classifier.save_model('models/embedding_classifier')
    
    # 测试预测
    test_texts = [
        "Did not observe any item or terminal signal within 600000ms in 'peek'",
        "Authentication failed: invalid token",
        "JSON parse error in arguments field",
        "Model name not found in request",
        "Connection timeout after 30 seconds"
    ]
    
    logger.info("\nEmbedding分类器测试预测:")
    logger.info("=" * 60)
    for text in test_texts:
        try:
            result = classifier.predict(text)
            logger.info(f"文本: {text}")
            logger.info(f"预测: {result['predicted_label']}")
            logger.info(f"置信度: {result['confidence']:.3f}")
            logger.info(f"可信度: {'是' if result['is_confident'] else '否'}")
            logger.info(f"Top3: {result['top_5_predictions'][:3]}")
            logger.info("-" * 40)
        except Exception as e:
            logger.error(f"预测失败: {e}")

if __name__ == "__main__":
    main()
