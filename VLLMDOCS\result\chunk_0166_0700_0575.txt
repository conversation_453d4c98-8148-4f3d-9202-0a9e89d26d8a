# 文档路径: 10-tutorials > 01-vLLM-stepbysteb > vLLM 入门教程：零基础分步指南 > 四、发出请求 > 4.1 使用 OpenAI 客户端

## 四、发出请求

在本教程中启动的 API 地址是 `http://localhost:8080`，您可以通过访问该地址来使用 API。`localhost` 指平台本机，`8080` 是 API 服务监听的端口号。

在工作空间右侧，API 地址将转发到本地 8080 服务，可以通过真实主机进行请求，如下图所示：

![图片](/img/docs/02-tutorials/api_path.png)


### 4.1 使用 OpenAI 客户端

在第四步中启动 vLLM 服务后，您可以通过 OpenAI 客户端调用 API。以下是一个简单的示例：

```python
# 注意：请先安装 openai
# pip install openai
from openai import OpenAI

# 设置 OpenAI API 密钥和 API 基础地址
openai_api_key = "EMPTY"  # 请替换为您的 API 密钥
openai_api_base = "http://localhost:8080/v1"  # 本地服务地址

client = OpenAI(api_key=openai_api_key, base_url=openai_api_base)

models = client.models.list()
model = models.data[0].id
prompt = "描述一下北京的秋天"

# Completion API 调用
completion

 = client.completions.create(model=model, prompt=prompt)
res = completion.choices[0].text.strip()
print(f"Prompt: {prompt}\nResponse: {res}")
```

执行命令：

```bash
python api_infer.py
```

您将看到如下输出结果：

![图片](/img/docs/02-tutorials/res_api.png)
