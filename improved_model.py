"""
改进的应用服务报错根因分析模型

针对小样本场景重新设计的轻量级模型
"""

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.naive_bayes import MultinomialNB
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
from sklearn.preprocessing import LabelEncoder
import re
import jieba
from typing import List, Dict, Tuple, Any
import logging
import json
import joblib
import os
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedErrorAnalyzer:
    """
    改进的错误分析器
    
    针对小样本场景优化的轻量级模型
    """
    
    def __init__(self):
        self.vectorizer = None
        self.model = None
        self.label_encoder = LabelEncoder()
        self.feature_names = []
        self.label_mapping = {}
        
        # 错误关键词模式（针对应用服务错误优化）
        self.error_patterns = {
            '超时': ['timeout', '600s', '600000ms', 'time', '超时', '时间'],
            '连接': ['connection', 'connect', '连接', '链接'],
            '认证': ['auth', 'authentication', 'token', '认证', '鉴权', '权限'],
            '格式': ['json', 'parse', 'format', '格式', '解析', 'arguments'],
            '模型': ['model', 'embedding', 'deepseek', 'qwen', '模型'],
            '请求': ['request', 'body', 'content', '请求', '内容'],
            '响应': ['response', 'answer', 'null', '响应', '回答'],
            '参数': ['parameter', 'param', 'top_p', 'dimensions', '参数'],
            '多模态': ['multimodal', 'image', 'text', 'type', '多模态', '图片'],
            '工具': ['tool', 'function', 'call', '工具', '函数']
        }
    
    def extract_enhanced_features(self, text: str) -> Dict[str, float]:
        """
        提取增强特征
        
        Args:
            text: 输入文本
            
        Returns:
            特征字典
        """
        features = {}
        text_lower = text.lower()
        
        # 1. 错误模式匹配
        for pattern_name, keywords in self.error_patterns.items():
            count = sum(1 for keyword in keywords if keyword.lower() in text_lower)
            features[f'pattern_{pattern_name}'] = count
        
        # 2. 数字特征
        numbers = re.findall(r'\d+', text)
        features['number_count'] = len(numbers)
        features['has_large_number'] = 1 if any(int(n) > 100 for n in numbers if n.isdigit()) else 0
        features['has_http_code'] = 1 if any(n in ['400', '401', '403', '404', '500', '502', '503'] for n in numbers) else 0
        
        # 3. 特殊字符和格式
        features['has_quotes'] = 1 if '"' in text or "'" in text else 0
        features['has_json_like'] = 1 if '{' in text or '}' in text else 0
        features['has_url_like'] = 1 if 'http' in text_lower or 'www' in text_lower else 0
        
        # 4. 文本长度特征
        features['text_length'] = len(text)
        features['word_count'] = len(text.split())
        features['avg_word_length'] = np.mean([len(word) for word in text.split()]) if text.split() else 0
        
        # 5. 语言特征
        chinese_chars = len(re.findall(r'[\u4e00-\u9fa5]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        features['chinese_ratio'] = chinese_chars / len(text) if text else 0
        features['english_ratio'] = english_chars / len(text) if text else 0
        
        # 6. 特定错误指示词
        error_indicators = ['error', 'failed', 'exception', 'null', 'invalid', 'timeout', 'refused']
        features['error_indicator_count'] = sum(1 for indicator in error_indicators if indicator in text_lower)
        
        return features
    
    def preprocess_text(self, text: str) -> str:
        """
        文本预处理
        
        Args:
            text: 原始文本
            
        Returns:
            预处理后的文本
        """
        # 基本清洗
        text = str(text).strip()
        
        # 保留重要的标点和数字
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()[\]{}"\'`~@#$%^&*+=|\\/<>-]', ' ', text)
        
        # 标准化空白字符
        text = re.sub(r'\s+', ' ', text)
        
        return text
    
    def create_combined_features(self, texts: List[str]) -> Tuple[np.ndarray, List[str]]:
        """
        创建组合特征
        
        Args:
            texts: 文本列表
            
        Returns:
            特征矩阵和特征名称
        """
        # 1. 文本预处理
        processed_texts = [self.preprocess_text(text) for text in texts]
        
        # 2. TF-IDF特征
        if self.vectorizer is None:
            self.vectorizer = TfidfVectorizer(
                max_features=200,  # 减少特征数量
                ngram_range=(1, 2),
                min_df=1,  # 保留所有词汇（数据太少）
                max_df=0.95,
                stop_words=None,  # 不使用停用词（错误信息中的停用词可能有意义）
                token_pattern=r'(?u)\b\w+\b'
            )
            tfidf_features = self.vectorizer.fit_transform(processed_texts)
        else:
            tfidf_features = self.vectorizer.transform(processed_texts)
        
        # 3. 手工特征
        manual_features = []
        for text in texts:
            features = self.extract_enhanced_features(text)
            manual_features.append(list(features.values()))
        
        manual_features = np.array(manual_features)
        
        # 4. 组合特征
        combined_features = np.hstack([tfidf_features.toarray(), manual_features])
        
        # 5. 特征名称
        tfidf_names = [f'tfidf_{i}' for i in range(tfidf_features.shape[1])]
        manual_names = list(self.extract_enhanced_features("").keys())
        feature_names = tfidf_names + manual_names
        
        return combined_features, feature_names
    
    def train_multiple_models(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练多个模型并选择最佳
        
        Args:
            X: 特征矩阵
            y: 标签
            
        Returns:
            模型评估结果
        """
        models = {
            'RandomForest': RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=2,
                min_samples_leaf=1,
                random_state=42,
                class_weight='balanced'
            ),
            'LogisticRegression': LogisticRegression(
                max_iter=1000,
                random_state=42,
                class_weight='balanced',
                C=1.0
            ),
            'SVM': SVC(
                kernel='rbf',
                random_state=42,
                class_weight='balanced',
                probability=True,
                C=1.0
            ),
            'NaiveBayes': MultinomialNB(alpha=1.0)
        }
        
        results = {}
        
        # 使用交叉验证评估模型
        cv = StratifiedKFold(n_splits=min(5, len(np.unique(y))), shuffle=True, random_state=42)
        
        for name, model in models.items():
            try:
                # 交叉验证
                cv_scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
                
                # 训练完整模型
                model.fit(X, y)
                
                results[name] = {
                    'model': model,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'cv_scores': cv_scores
                }
                
                logger.info(f"{name}: CV准确率 = {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
                
            except Exception as e:
                logger.warning(f"{name} 训练失败: {e}")
                continue
        
        # 选择最佳模型
        if results:
            best_model_name = max(results.keys(), key=lambda k: results[k]['cv_mean'])
            self.model = results[best_model_name]['model']
            logger.info(f"选择最佳模型: {best_model_name}")
        
        return results
    
    def train(self, texts: List[str], labels: List[str]) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            texts: 文本列表
            labels: 标签列表
            
        Returns:
            训练结果
        """
        logger.info("开始训练改进的错误分析模型...")
        logger.info(f"训练数据: {len(texts)} 条, 类别数: {len(set(labels))}")
        
        # 标签编码
        y = self.label_encoder.fit_transform(labels)
        self.label_mapping = {i: label for i, label in enumerate(self.label_encoder.classes_)}
        
        # 特征提取
        X, feature_names = self.create_combined_features(texts)
        self.feature_names = feature_names
        
        logger.info(f"特征维度: {X.shape[1]}")
        
        # 训练多个模型
        model_results = self.train_multiple_models(X, y)
        
        # 详细评估最佳模型
        if self.model is not None:
            y_pred = self.model.predict(X)
            accuracy = accuracy_score(y, y_pred)
            
            logger.info(f"训练集准确率: {accuracy:.3f}")
            
            # 分类报告
            report = classification_report(y, y_pred, target_names=self.label_encoder.classes_, zero_division=0)
            logger.info(f"分类报告:\n{report}")
        
        return {
            'model_results': model_results,
            'feature_count': X.shape[1],
            'class_count': len(self.label_encoder.classes_),
            'label_mapping': self.label_mapping
        }
    
    def predict(self, text: str) -> Dict[str, Any]:
        """
        预测单条文本
        
        Args:
            text: 输入文本
            
        Returns:
            预测结果
        """
        if self.model is None:
            raise ValueError("模型未训练")
        
        # 特征提取
        X, _ = self.create_combined_features([text])
        
        # 预测
        pred_proba = self.model.predict_proba(X[0:1])[0]
        pred_class = np.argmax(pred_proba)
        
        # 获取Top-3预测
        top_indices = np.argsort(pred_proba)[::-1][:3]
        top_predictions = [(self.label_mapping[i], pred_proba[i]) for i in top_indices]
        
        return {
            'predicted_label': self.label_mapping[pred_class],
            'confidence': pred_proba[pred_class],
            'top_3_predictions': top_predictions,
            'all_probabilities': {self.label_mapping[i]: prob for i, prob in enumerate(pred_proba)}
        }
    
    def save_model(self, path: str):
        """保存模型"""
        os.makedirs(path, exist_ok=True)
        
        # 保存模型组件
        joblib.dump(self.model, os.path.join(path, 'model.pkl'))
        joblib.dump(self.vectorizer, os.path.join(path, 'vectorizer.pkl'))
        joblib.dump(self.label_encoder, os.path.join(path, 'label_encoder.pkl'))
        
        # 保存配置
        config = {
            'feature_names': self.feature_names,
            'label_mapping': self.label_mapping,
            'error_patterns': self.error_patterns
        }
        
        with open(os.path.join(path, 'config.json'), 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"模型已保存到: {path}")
    
    def load_model(self, path: str):
        """加载模型"""
        self.model = joblib.load(os.path.join(path, 'model.pkl'))
        self.vectorizer = joblib.load(os.path.join(path, 'vectorizer.pkl'))
        self.label_encoder = joblib.load(os.path.join(path, 'label_encoder.pkl'))
        
        with open(os.path.join(path, 'config.json'), 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        self.feature_names = config['feature_names']
        self.label_mapping = config['label_mapping']
        self.error_patterns = config['error_patterns']
        
        logger.info(f"模型已从 {path} 加载")

def main():
    """主函数"""
    # 读取数据
    df = pd.read_excel('processed_data.xlsx')
    texts = df['响应内容'].astype(str).tolist()
    labels = df['报错原因'].astype(str).tolist()
    
    logger.info(f"数据统计:")
    logger.info(f"总样本数: {len(texts)}")
    logger.info(f"类别数: {len(set(labels))}")
    
    # 显示类别分布
    label_counts = Counter(labels)
    logger.info("类别分布:")
    for label, count in label_counts.most_common():
        logger.info(f"  {label}: {count}")
    
    # 创建和训练模型
    analyzer = ImprovedErrorAnalyzer()
    results = analyzer.train(texts, labels)
    
    # 保存模型
    analyzer.save_model('models/improved_classifier')
    
    # 测试预测
    test_texts = [
        "Did not observe any item or terminal signal within 600000ms in 'peek'",
        "Authentication failed: invalid token",
        "JSON parse error in arguments field",
        "Model name not found in request",
        "Connection timeout after 30 seconds"
    ]
    
    logger.info("\n测试预测:")
    for text in test_texts:
        try:
            result = analyzer.predict(text)
            logger.info(f"文本: {text}")
            logger.info(f"预测: {result['predicted_label']} (置信度: {result['confidence']:.3f})")
            logger.info(f"Top3: {result['top_3_predictions']}")
            logger.info("-" * 50)
        except Exception as e:
            logger.error(f"预测失败: {e}")

if __name__ == "__main__":
    main()
