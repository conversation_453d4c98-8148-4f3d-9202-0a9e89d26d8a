# 文档路径: 07-developer-documentation > 02-offline-inference > 01-llm-class > LLM 类

> class vllm.LLM(model: [str](https://docs.python.org/3/library/stdtypes.html#str), tokenizer: [str](https://docs.python.org/3/library/stdtypes.html#str) | [None](https://docs.python.org/3/library/constants.html#None) = None, tokenizer_mode: [str](https://docs.python.org/3/library/stdtypes.html#str) = 'auto', skip_tokenizer_init: [bool](https://docs.python.org/3/library/functions.html#bool) = False, trust_remote_code: [bool](https://docs.python.org/3/library/functions.html#bool) = False, tensor_parallel_size: [int](https://docs.python.org/3/library/functions.html#int) = 1, dtype: [str](https://docs.python.org/3/library/stdtypes.html#str) = 'auto', quantization: [str](https://docs.python.org/3/library/stdtypes.html#str) | [None](https://docs.python.org/3/library/constants.html#None) = None, revision: [str](https://docs.python.org/3/library/stdtypes.html#str) | [None](https://docs.python.org/3/library/constants.html#None) = None, tokenizer_revision: [str](https://docs.python.org/3/library/stdtypes.html#str) | [None](https://docs.python.org/3/library/constants.html#None) = None, seed: [int](https://docs.python.org/3/library/functions.html#int) = 0, gpu_memory_utilization: [float](https://docs.python.org/3/library/functions.html#float) = 0.9, swap_space: [float](https://docs.python.org/3/library/functions.html#float) = 4, cpu_offload_gb: [float](https://docs.python.org/3/library/functions.html#float) = 0, enforce_eager: [bool](https://docs.python.org/3/library/functions.html#bool) | [None](https://docs.python.org/3/library/constants.html#None) = None, max_context_len_to_capture: [int](https://docs.python.org/3/library/functions.html#int) | [None](https://docs.python.org/3/library/constants.html#None) = None, max_seq_len_to_capture: [int](https://docs.python.org/3/library/functions.html#int) = 8192, disable_custom_all_reduce: [bool](https://docs.python.org/3/library/functions.html#bool) = False, disable_async_output_proc: [bool](https://docs.python.org/3/library/functions.html#bool) = False, \*\*kwargs)
> [[source]](https://docs.vllm.ai/en/latest/_modules/vllm/entrypoints/llm.html#LLM)

1 个用于根据给定提示和采样参数生成文本的大语言模型。

该类包括 1 个 tokenizer、1 个语言模型（可能分布在多个 GPU 上）以及为中间状态分配的 GPU 内存空间（也称为 KV 缓存）。给定一批提示和采样参数，该类使用智能批处理机制和高效的内存管理从模型中生成文本。