# 文档路径: 02-serving > 01-openai-compatible-server > OpenAI 兼容服务器 > chat completion API 中的工具调用 > 自动函数调用

## chat completion API 中的工具调用

vLLM 默认仅支持 chat completion API 中的命名函数调用。`tool_choice` 选项 `auto` 和 `required` 目前**尚未\*\***支持\*\*，但已在开发计划中。

调用者需要负责向模型提示工具信息，vLLM 不会自动处理这些提示。

vLLM 将使用引导式解码机制，以确保响应内容与 `tools` 参数中定义的工具参数对象的 JSON 模式保持一致。


### 自动函数调用

要启用此功能，你需要设置以下标志：

- `--enable-auto-tool-choice` — **强制**启用自动工具选择。它告诉 vLLM，当模型认为适当时，你希望启用模型自行生成工具调用。
- `--tool-call-parser` — 选择要使用的工具解析器——目前可以选择 `hermes` 或 `mistral`。未来将继续添加其他工具解析器。
- `--chat-template` — 自动工具选择的**可选配置**。指定聊天模板的路径，该模板用于处理 `tool` 角色消息和包含先前生成的工具调用的 `assistant` 角色消息。Hermes 和 Mistral 模型在其 `tokenizer_config.json` 文件中有与工具兼容的聊天模板，但你可以指定自定义模板。如果您的模型在 `tokenizer_config.json` 中配置了特定于工具使用的场景聊天模板，这个参数可以设置为 `tool_use`。这种情况下，它将按照 `transformers` 规范使用。有关更多信息，可以在 HuggingFace 的[相关文档](https://huggingface.co/docs/transformers/en/chat_templating#why-do-some-models-have-multiple-templates)中找到；您可以在 tokenizer_config.json [示例](https://huggingface.co/NousResearch/Hermes-2-Pro-Llama-3-8B/blob/main/tokenizer_config.json)中看到这一点。

如果您喜欢的工具调用模型尚未得到支持，欢迎随时贡献解析器和工具使用聊天模板！


#### Hermes 模型

支持所有 Nous Research Hermes 系列的模型（Hermes 2 Pro 及更高版本）。

- `NousResearch/Hermes-2-Pro-*`
- `NousResearch/Hermes-2-Theta-*`
- `NousResearch/Hermes-3-*`

**注意：**由于合并操作，**Hermes 2 Theta** 模型的工具调用质量和能力据悉已有所降低。

标志：`--tool-call-parser hermes`


#### Mistral 模型

支持的模型：

- `mistralai/Mistral-7B-Instruct-v0.3`（已确认）
- 其他 Mistral 函数调用模型也兼容。

已知问题：

1. Mistral 7B 在正确生成并行工具调用时存在困难。

2. Mistral 的 `tokenizer_config.json` 聊天模板要求工具调用 ID 恰好为 9 位，这比 vLLM 生成的 ID 短的多。由于不符合该条件时会引发异常，因此提供了以下额外的聊天模板：

- `examples/tool_chat_template_mistral.jinja` - 这是「官方」的 Mistral 聊天模板，但经过了调整，以适应 vLLM 的工具调用 ID（提供的 `tool_call_id` 字段被截断为最后 9 位）
- `examples/tool_chat_template_mistral_parallel.jinja` - 这是一个「更好」的版本，当提供工具时，它会添加一个工具使用系统提示，这在处理并行工具调用时可靠性会更高。

推荐标志： `--tool-call-parser mistral --chat-template examples/tool_chat_template_mistral_parallel.jinja`
