# 文档路径: 10-tutorials > 03-few-shot-w-qwen2-5 > 使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 目录

[在线运行此教程](https://openbayes.com/console/hyperai-tutorials/containers/1HFARLMLJXL)

该教程为在 RTX4090 上该教程为使用 vLLM 加载 Qwen2.5-3B-Instruct-AWQ 模型进行少样本学习。

- 对于每个测试问题，我们使用训练数据检索一组「支持」它的类似问题。
  - 考虑「construct」和「subject」等内容
- 使用一组类似的问题，我们创建了一个可以馈送到我们的模型的对话

  - 在对话中使用最近支持的 chat（） 功能
  - 生成温度略高的 n 个响应，以创建不同的输出

- 对于每个问题/答案对，我们现在有 n 个推断的误解，对于每个误解，我们使用 BGE 嵌入检索前 25 个误解。
- 对于每个问题/答案对的 n 个推断错误中的每一个的 25 个最接近的误解，现在可以使用 Borda Ranking 进行组合，这有点像最简单的集成形式。

## 目录


- [1. 导入相关的库](#1.导入相关的库)
- [2. 加载数据](#2.加载数据)
- [3. 使用 vLLM 启动 Qwen2.5-3B-Instruct-AWQ](#3.使用vLLM启动Qwen2.5-3B-Instruct-AWQ)
- [4. 后处理数据](#4.后处理数据)
- [5. 辅助函数](#5.辅助函数)
- [6. 使用llm.chat](#6.使用llm.chat)
- [7. 找到最相似的误解](#7.找到最相似的误解)
- [8. 提交](#8.提交)
