# 文档路径: 02-serving > 09-compatibility matrix > 兼容性矩阵 > Feature x Hardware

## Feature x Hardware


| Feature                                                                      | Volta                                                 | Turing | Ampere | Ada | Hopper | CPU                                                                                                                          | AMD |
| :--------------------------------------------------------------------------- | :---------------------------------------------------- | :----- | :----- | :-- | :----- | :--------------------------------------------------------------------------------------------------------------------------- | :-- |
| [CP](https://docs.vllm.ai/en/latest/models/performance.html#chunked-prefill) | [✗](https://github.com/vllm-project/vllm/issues/2729) | ✅     | ✅     | ✅  | ✅     | ✗                                                                                                                            | ✅  |
| [APC](https://docs.vllm.ai/en/latest/automatic_prefix_caching/apc.html#apc)  | [✗](https://github.com/vllm-project/vllm/issues/3687) | ✅     | ✅     | ✅  | ✅     | ✗                                                                                                                            | ✅  |
| [LoRA](https://docs.vllm.ai/en/latest/models/lora.html#lora)                 | ✅                                                    | ✅     | ✅     | ✅  | ✅     | [✗](https://github.com/vllm-project/vllm/pull/4830)                                                                          | ✅  |
| prmpt adptr                                                                  | ✅                                                    | ✅     | ✅     | ✅  | ✅     | [✗](https://github.com/vllm-project/vllm/issues/8475)                                                                        | ✅  |
| [SD](https://docs.vllm.ai/en/latest/models/spec_decode.html#spec-decode)     | ✅                                                    | ✅     | ✅     | ✅  | ✅     | ✅                                                                                                                           | ✅  |
| CUDA graph                                                                   | ✅                                                    | ✅     | ✅     | ✅  | ✅     | ✗                                                                                                                            | ✅  |
| enc-dec                                                                      | ✅                                                    | ✅     | ✅     | ✅  | ✅     | [✗](https://github.com/vllm-project/vllm/blob/a84e598e2125960d3b4f716b78863f24ac562947/vllm/worker/cpu_model_runner.py#L125) | ✗   |
| logP                                                                         | ✅                                                    | ✅     | ✅     | ✅  | ✅     | ✅                                                                                                                           | ✅  |
| prmpt logP                                                                   | ✅                                                    | ✅     | ✅     | ✅  | ✅     | ✅                                                                                                                           | ✅  |
| async output                                                                 | ✅                                                    | ✅     | ✅     | ✅  | ✅     | ✗                                                                                                                            | ✗   |
| multi-step                                                                   | ✅                                                    | ✅     | ✅     | ✅  | ✅     | [✗](https://github.com/vllm-project/vllm/issues/8477)                                                                        | ✅  |
| MM                                                                           | ✅                                                    | ✅     | ✅     | ✅  | ✅     | ✅                                                                                                                           | ✅  |
| best-of                                                                      | ✅                                                    | ✅     | ✅     | ✅  | ✅     | ✅                                                                                                                           | ✅  |
| beam-search                                                                  | ✅                                                    | ✅     | ✅     | ✅  | ✅     | ✅                                                                                                                           | ✅  |
| guided dec                                                                   | ✅                                                    | ✅     | ✅     | ✅  | ✅     | ✅                                                                                                                           | ✅  |
