# 文档路径: 10-tutorials > 03-few-shot-w-qwen2-5 > 使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 7. 找到最相似的误解 > 合并每个问题的每个生成输出的排名

## 7. 找到最相似的误解

### 合并每个问题的每个生成输出的排名


Borda count 是一种非常简单的排名机制

```
def borda_count(rankings):
    scores = {}
    num_elements = len(next(iter(rankings)))

    for model_ranking in rankings:
        for idx, item in enumerate(model_ranking):
            points = num_elements - idx
            scores[item] = scores.get(item, 0) + points

    # 根据总分排序误解。Sort the misconceptions based on total points
    final_ranking = sorted(scores.items(), key=lambda x: x[1], reverse=True)
    ranked_results = [r for r, score in final_ranking]
    return ranked_results

# 计算最终排名。Compute the final ranking
final_rankings = np.array([borda_count(result) for result in n_results])

final_rankings.shape
```

```
submission['MisconceptionId'] = final_rankings[:, :25].tolist()
```
