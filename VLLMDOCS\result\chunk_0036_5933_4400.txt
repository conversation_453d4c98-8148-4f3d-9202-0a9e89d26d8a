# 文档路径: 01-getting-started > 06-installation-with-tpu > 使用 TPU 安装 > 使用`Dockerfile.tpu` 构建 Docker 镜像

## 使用`Dockerfile.tpu` 构建 Docker 镜像

[Dockerfile.tpu](https://github.com/vllm-project/vllm/blob/main/Dockerfile.tpu) 用于构建具有 TPU 支持的 docker 镜像。

```plain
docker build -f Dockerfile.tpu -t vllm-tpu .
```

您可以使用以下命令运行 docker 镜像：

```plain
# Make sure to add `--privileged --net host --shm-size=16G`.

# 确保添加 `--privileged --net host --shm-size=16G`。

docker run --privileged --net host --shm-size=16G -it vllm-tpu
```
