# 文档路径: 04-quantization > 05-int8-w8a8 > INT8 W8A8 > 量化过程 > 3. 应用量化

## 量化过程

量化过程涉及 4 个主要步骤：

1. 加载模型

2. 准备校准数据

3. 应用量化

4. 评估 vLLM 的准确性


### 3. 应用量化

现在，应用量化算法：

```python
from llmcompressor.transformers import oneshot
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier


# Configure the quantization algorithms
# 配置量化算法


recipe = [
    SmoothQuantModifier(smoothing_strength=0.8),
    GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
]


# Apply quantization
# 应用量化


oneshot(
    model=model,
    dataset=ds,
    recipe=recipe,
    max_seq_length=MAX_SEQUENCE_LENGTH,
    num_calibration_samples=NUM_CALIBRATION_SAMPLES,
)


# Save the compressed model
# 保存压缩后的模型


SAVE_DIR = MODEL_ID.split("/")[1] + "-W8A8-Dynamic-Per-Token"
model.save_pretrained(SAVE_DIR, save_compressed=True)
tokenizer.save_pretrained(SAVE_DIR)
```

这个过程会创建一个 W8A8 模型，其权重和激活被量化为 8 位整数。
