# 文档路径: 03-models > 01-supported-models > 支持的模型 > 多模态语言模型 > 多模态 Embedding

## 多模态语言模型

### 多模态 Embedding


| 架构             | 模型               | 输入  | HF 模型案例            | [LoRA](https://docs.vllm.ai/en/latest/models/lora.html#lora) | [PP](https://docs.vllm.ai/en/latest/serving/distributed_serving.html#distributed-serving) |
| :--------------- | :----------------- | :---- | :--------------------- | :----------------------------------------------------------- | :---------------------------------------------------------------------------------------- |
| Phi3VForCausalLM | Phi-3-Vision-based | T + I | TIGER-Lab/VLM2Vec-Full | 🚧                                                           | ✅︎                                                                                       |

**注意：**

有些模型架构同时支持生成和嵌入任务。在这种情况下，你需要传入 `--task embedding` 参数，才能以嵌入模式运行该模型。

如果您的模型使用上述模型架构之一，您可以使用 vLLM 无缝运行您的模型。否则，请参阅 `添加新模型 <adding_a_new_model>` 和 `启用多模式输入 <enabling_multimodal_inputs>` 中的说明了解如何为您的模型提供支持。或者，您可以在我们的 [GitHub](https://github.com/vllm-project/vllm/issues) 项目上提出问题。

**提示：**

要确认您的模型是否得到支持，最简单的方法是执行以下程序：

```python
from vllm import LLM


llm = LLM(model=...)  # Name or path of your model


llm = LLM(model=...) # 模型的名称或路径


output = llm.generate("Hello, my name is")
print(output)
```

如果 vLLM 成功生成文本，则表明您的模型受支持。

**提示：**

要使用 [ModelScope](https://www.modelscope.cn) 中的模型而不是 HuggingFace Hub 的模型，请设置一个环境变量：

```plain
export VLLM_USE_MODELSCOPE=True
```

并与 `trust_remote_code=True` 一起使用。

```python
from vllm import LLM


llm = LLM(model=..., revision=..., trust_remote_code=True)  # Name or path of your model


llm = LLM(model=..., revision=..., trust_remote_code=True) # 模型的名称或路径


output = llm.generate("Hello, my name is")
print(output)
```
