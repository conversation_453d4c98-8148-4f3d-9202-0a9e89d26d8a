# 文档路径: 01-getting-started > 05-installation-with-neuron > 使用 Neuron 安装 > 从源代码构建 > 步骤 1. 安装驱动程序和工具

## 从源代码构建

以下说明适用于 Neuron SDK 2.16 及更高版本。


### 步骤 1. 安装驱动程序和工具

如果 [Deep Learning AMI Neuron](https://docs.aws.amazon.com/dlami/latest/devguide/appendix-ami-release-notes.html) 已安装，则无需安装驱动程序和工具。如果操作系统上未安装驱动程序和工具，请按照以下步骤操作:

```plain
# Configure Linux for Neuron repository updates
# 配置 Linux 以进行 Neuron 存储库更新

. /etc/os-release
sudo tee /etc/apt/sources.list.d/neuron.list > /dev/null <<EOF
deb https://apt.repos.neuron.amazonaws.com ${VERSION_CODENAME} main
EOF
wget -qO - https://apt.repos.neuron.amazonaws.com/GPG-PUB-KEY-AMAZON-AWS-NEURON.PUB | sudo apt-key add -

# Update OS packages
# 更新操作系统包

sudo apt-get update -y

# Install OS headers
# 安装操作系统头文件

sudo apt-get install linux-headers-$(uname -r) -y

# Install git
# 安装git

sudo apt-get install git -y

# install Neuron Driver
# 安装 Neuron 驱动

sudo apt-get install aws-neuronx-dkms=2.* -y

# Install Neuron Runtime
# 安装 Neuron 运行时

sudo apt-get install aws-neuronx-collectives=2.* -y
sudo apt-get install aws-neuronx-runtime-lib=2.* -y

# Install Neuron Tools
# 安装 Neuron Tools

sudo apt-get install aws-neuronx-tools=2.* -y

# Add PATH
# 添加路径

export PATH=/opt/aws/neuron/bin:$PATH
```
