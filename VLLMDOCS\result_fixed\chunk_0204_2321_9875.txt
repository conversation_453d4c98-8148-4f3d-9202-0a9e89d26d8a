# 文档路径: 07-developer-documentation > 02-offline-inference > 02-llm-inputs > LLM Inputs > vllm.inputs.PromptType

## vllm.inputs.PromptType


内部 API 的核心部分。

这表示具有类型参数「params」的「origin」类型的通用版本。这类别名有两种：用户定义的和特殊的。特殊的是 collections.abc 中关于内置容器合与容器中 ABCs 的封装。它们必须始终设置「name」。如果「inst」为 False，则该别名无法实例化，例如可以使用别名： Typing.List 和 Typing.Dict。

是 [Union](https://docs.python.org/3/library/typing.html#typing.Union)[[str](https://docs.python.org/3/library/stdtypes.html#str), [TextPrompt](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TextPrompt), [TokensPrompt](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TokensPrompt), ExplicitEncoderDecoderPrompt] 的别名。
