# 文档路径: 02-serving > 07-integrations > 01-deploying&scaling-up-with-skypilot > 使用 SkyPilot 进行部署和扩充 > 扩充到多个副本 > **可选**: 将 GUI 连接到端点

![图片](/img/docs/02-07/01-Deploying&scaling-up-with-SkyPilot.png)

vLLM 可以通过 [SkyPilot](https://github.com/skypilot-org/skypilot) （一个用于在任何云端运行 LLM 的开源框架）**在云\*\***端\***\*和 Kubernetes 上运行并扩充为多个服务副本**。关于更多各种开放模型的示例，例如 Llama-3、Mixtral 等，可以在 [SkyPilot AI gallery](https://skypilot.readthedocs.io/en/latest/gallery/index.html) 中找到。

## 扩充到多个副本

### **可选**: 将 GUI 连接到端点


也可以使用单独的 GUI 前端访问 Llama-3 服务，这样发送到 GUI 的用户请求将在副本之间进行负载平衡。

<details>

<summary>点击查看完整的 GUI YAML</summary>

```yaml
envs:
  MODEL_NAME: meta-llama/Meta-Llama-3-8B-Instruct
  ENDPOINT: x.x.x.x:3031 # Address of the API server running vllm.


  ENDPOINT: x.x.x.x:3031 # 运行 vllm 的 API 服务器的地址。




resources:
  cpus: 2


setup: |
  conda create -n vllm python=3.10 -y
  conda activate vllm


  # Install Gradio for web UI.


  # 安装 Gradio for Web UI。


  pip install gradio openai


run: |
  conda activate vllm
  export PATH=$PATH:/sbin


  echo 'Starting gradio server...'
  git clone https://github.com/vllm-project/vllm.git || true
  python vllm/examples/gradio_openai_chatbot_webserver.py \
    -m $MODEL_NAME \
    --port 8811 \
    --model-url http://$ENDPOINT/v1 \
    --stop-token-ids 128009,128001 | tee ~/gradio.log
```

 </details>

1.启动聊天 Web UI：

```plain
sky launch -c gui ./gui.yaml --env ENDPOINT=$(sky serve status --endpoint vllm)
```

2.然后，我们可以通过返回的 gradio 链接访问 GUI：

```plain
| INFO | stdout | Running on public URL: https://6141e84201ce0bb4ed.gradio.live
```
