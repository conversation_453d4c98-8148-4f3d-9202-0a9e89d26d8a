# 文档路径: 02-serving > 03-distributed-inference-and-serving > 分布式推理和服务 > 分布式推理和服务的详细信息

## 分布式推理和服务的详细信息


vLLM 支持分布式张量并行推理和服务。目前，我们支持 [Megatron-LM 的张量并行算法](https://arxiv.org/pdf/1909.08053.pdf)。我们还支持将管道并行作为在线服务的测试版功能。我们使用 [Ray](https://github.com/ray-project/ray) 或 python 的原生多进程来管理分布式运行时。在单节点部署时可以使用多进程，多节点推理目前需要 Ray。

当未在 Ray 放置组中运行时，并且同一节点上有足够的 GPU 可用于配置的 `tensor_parallel_size` ，则默认情况下将使用多进程，否则将使用 Ray。这个默认设置可以通过 `LLM` 类的 `distributed-executor-backend` 参数或 API 服务器的 `--distributed-executor-backend` 参数来覆盖。将其设置为 `mp` （用于多进程） 或 `ray` （用于 Ray）。对于多进程情况，不需要安装 Ray。

要使用 `LLM` 类运行多 GPU 推理，请将 `tensor_parallel_size` 参数设置为要使用的 GPU 数量。例如，要在 4 个 GPU 上运行推理:

```python
from vllm import LLM
llm = LLM(`facebook/opt-13b`, tensor_parallel_size=4)
output = llm.generate(`San Franciso is a`)
```

要运行多 GPU 服务，请在启动服务器时传入 `--tensor-parallel-size` 参数。例如，要在 4 个 GPU 上运行 API 服务器:

```plain
vllm serve facebook/opt-13b \
    --tensor-parallel-size 4
```

您还可以另外指定 `--pipeline-parallel-size` 以启用管道并行性。例如，要在 8 个 GPU 上运行具有管道并行性和张量并行性的 API 服务器:

```plain
vllm serve gpt2 \
    --tensor-parallel-size 4 \
    --pipeline-parallel-size 2
```

**注意：**

管道并行是一项测试功能，仅支持在线服务以及 LLaMa、GPT2、Mixtral、Qwen、Qwen2 和 Nemotron 风格模型。
