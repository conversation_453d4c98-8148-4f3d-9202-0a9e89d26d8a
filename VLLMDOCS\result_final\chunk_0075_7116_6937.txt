# 文档路径: 03-models > 08-performance-and-tuning > 性能与调优 > 抢占

## 抢占


由于 Transformer 架构的自回归特性，有时 KV 缓存空间不足以处理所有批量请求。 vLLM 可以抢占请求，为其他请求释放 KV 缓存空间。当有足够的 KV 缓存空间再次可用时，被抢占的请求将重新计算。发生这种情况时，会打印以下警告:

`WARNING 05-09 00:49:33 scheduler.py:1057] Sequence group 0 is preempted by PreemptionMode.SWAP mode because there is not enough KV cache space. This can affect the end-to-end performance. Increase gpu_memory_utilization or tensor_parallel_size to provide more KV cache memory. total_cumulative_preemption_cnt=1`

虽然这种机制确保了系统的稳健性，但抢占和重新计算可能会对端到端延迟有不利影响。如果您经常遇到 vLLM 引擎的抢占，建议进行以下操作:

- 增加 _gpu_memory_utilization_。 vLLM 使用 gpu_memory_utilization% 的内存来预分配 GPU 缓存。通过提高此利用率，您可以提供更多的 KV 缓存空间。
- 减少 _max_num_seqs_ 或 _max_num_batched_tokens_。这可以减少批处理中并发请求的数量，因此仅需更少的 KV 缓存空间。
- 增加 _tensor_parallel_size_。该法对模型权重进行了分片，这样每个 GPU 都有更多的内存用于 KV 缓存。

您还可以通过 vLLM 公开的 Prometheus 指标来监控抢占请求的数量。此外，还可以通过设置 disable_log_stats=False 来记录抢占请求的累积数量。
