# 文档路径: 08-indices-and-tables > 01-index > 索引 > G

## G

| [generate() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.generate)                                                                   | [(vllm.LLM method)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm.html#vllm.LLM.generate)                                                                                                                      |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| [get_data_key() (vllm.multimodal.image.ImagePlugin method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.image.ImagePlugin.get_data_key)                           | [(vllm.multimodal.MultiModalPlugin method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin.get_data_key)                                                            |
| [get_decoding_config() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.get_decoding_config)                                             | [(vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.get_decoding_config)                                                                                                   |
| [get_hf_config() (vllm.inputs.registry.InputContext method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputContext.get_hf_config)                 | [get_hf_image_processor_config() (vllm.inputs.registry.InputContext method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputContext.get_hf_image_processor_config) |
| [get_lora_config() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.get_lora_config)                                                     | [(vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.get_lora_config)                                                                                                       |
| [get_max_multimodal_tokens() (vllm.multimodal.MultiModalPlugin method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin.get_max_multimodal_tokens)   | [(vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.get_max_multimodal_tokens)                                           |
| [get_mm_limits_per_prompt() (vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.get_mm_limits_per_prompt) | [get_model_config() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.get_model_config)                                                                   |
| [(vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.get_model_config)                                                                                      | [get_num_unfinished_requests() (vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.get_num_unfinished_requests)                                                             |
| [get_parallel_config() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.get_parallel_config)                                             | [(vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.get_parallel_config)                                                                                                   |
| [get_scheduler_config() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.get_scheduler_config)                                           | [(vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.get_scheduler_config)                                                                                                  |
