# 文档路径: 07-developer-documentation > 04-vllm-paged-attention > vLLM  分页注意力 > 值

## 值


![图片](/img/docs/07-04/value.png)

1 个头中全部上下文 token 的值数据

![图片](/img/docs/07-04/logits_vec.png)

一个线程中的 `logits_vec`

![图片](/img/docs/07-04/v_vec.png)

一个线程中的 `v_vec` 列表

- 现在我们需要检索值数据并与 `logits` 进行点乘运算。与查询和键不同，值数据没有线程组的概念。如图所示，与键 token 的内存布局不同，同一列的元素对应于相同的值 token。对于 1 个值数据块，有 `HEAD_SIZE` 行，有 `BLOCK_SIZE` 列，它们被分割成多个 `v_vec`。
- 每个线程一次始终从相同的 `V_VEC_SIZE` 个 token 中获取 `V_VEC_SIZE` 个元素。因此，单个线程通过多次内部迭代，从不同的行和相同的列中检索多个 `v_vec`。对于每个 `v_vec`，它需要与相应的 `logits_vec` 进行点乘， `logits_vec` 也是来自 `logits` 的 `V_VEC_SIZE` 个元素。总体而言，通过多次内部迭代，每个 warp 将处理一个块的值 token；而通过多次外部迭代，整个上下文的值 token 将被处理。

```plain
    float accs[NUM_ROWS_PER_THREAD];
    for ... { // Iteration over different blocks. // 在不同块上迭代
        logits_vec = ...
        for ... { // Iteration over different rows. // 在不同行上迭代
            v_vec = ...
            ...
            accs[i] += dot(logits_vec, v_vec);
        }
    }
```

- 如上所示的伪代码中，在外部循环中，`logits_vec` 类似于 `k_ptr`，遍历不同的块并从 `logits` 中读取 `V_VEC_SIZE` 个元素。在内部循环中，每个线程从相同的 token 中读取 `V_VEC_SIZE` 个元素作为 `v_vec` 并进行点乘运算。需要注意的是，在每次内部迭代中，线程为相同的 token 获取不同头位置的元素。点乘结果随后被累加到 `accs` 中。因此，`accs` 的每个条目映射到当前线程分配的头位置。
- 例如，如果 `BLOCK_SIZE` 为 16，`V_VEC_SIZE` 为 8，则每个线程一次为 8 个 token 获取 8 个值元素。每个元素都来自同一头位置的不同 token。如果 `HEAD_SIZE` 为 128 且 `WARP_SIZE` 为 32，则对于每次内部循环，1 个 warp 需要获取 `WARP_SIZE * V_VEC_SIZE = 256` 个元素。这意味着 1 个 warp 总共需要 128 \* 16 / 256 = 8 次内部迭代来处理整个块的值 token。并且每个线程中的每个 accs 包含 8 个元素，这些元素累积在 8 个不同的头位置。对于线程 0，`accs` 变量将有 8 个元素，它们是从所有分配的 8 个 token 中累积的值头的第 0 个、第 32 个……第 224 个元素。
