# 文档路径: 02-serving > 01-openai-compatible-server > OpenAI 兼容服务器 > API 参考

vLLM 提供了一个实现 OpenAI [Completions](https://platform.openai.com/docs/api-reference/completions) 和 [Chat](https://platform.openai.com/docs/api-reference/chat) API 的 HTTP 服务器。

您可以使用 Python 或 [Docker](https://docs.vllm.ai/en/latest/serving/deploying_with_docker.html) 启动服务器：

```bash
vllm serve NousResearch/Meta-Llama-3-8B-Instruct --dtype auto --api-key token-abc123
```

如需要调用服务器，您可以使用官方的 OpenAI Python 客户端库，或任何其他 HTTP 客户端。

```python
from openai import OpenAI
client = OpenAI(
    base_url="http://localhost:8000/v1",
    api_key="token-abc123",
)


completion = client.chat.completions.create(
  model="NousResearch/Meta-Llama-3-8B-Instruct",
  messages=[
    {"role": "user", "content": "Hello!"}
  ]
)


print(completion.choices[0].message)
```

## API 参考


有关 API 的更多信息，请参阅 [OpenAI API 参考](https://platform.openai.com/docs/api-reference)。我们支持除以下参数的所有参数：

- Chat：`tools` 和 `tool_choice`。
- Completions：`suffix`。

vLLM 还提供对 OpenAI Vision API 兼容推理的实验性支持。有关更多详细信息，请参阅[使用 VLMs](https://docs.vllm.ai/en/latest/models/vlm.html)。
