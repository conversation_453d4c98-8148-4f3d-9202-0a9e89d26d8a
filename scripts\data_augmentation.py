"""
数据增强脚本

针对小样本场景，提供多种数据增强策略
"""

import pandas as pd
import numpy as np
import random
import re
from typing import List, Tuple, Dict
import logging
from pathlib import Path
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataAugmenter:
    """
    数据增强器
    
    提供多种数据增强策略来扩充训练数据
    """
    
    def __init__(self):
        # 错误关键词替换词典
        self.error_synonyms = {
            'error': ['错误', 'err', 'exception', '异常'],
            'failed': ['失败', 'fail', '失效', '不成功'],
            'timeout': ['超时', '时间超限', '响应超时', '请求超时'],
            'connection': ['连接', '链接', 'connect', '网络连接'],
            'invalid': ['无效', '非法', '不合法', '错误的'],
            'unauthorized': ['未授权', '无权限', '认证失败', '鉴权失败'],
            'forbidden': ['禁止', '拒绝访问', '权限不足', '访问被拒'],
            'not found': ['未找到', '不存在', '找不到', '404'],
            'server': ['服务器', '服务端', 'srv', '后端'],
            'database': ['数据库', 'db', '数据存储', '数据源']
        }
        
        # 数字替换范围
        self.number_ranges = {
            'http_codes': [400, 401, 403, 404, 500, 502, 503, 504],
            'timeouts': [30, 60, 120, 300, 600],
            'ports': [80, 443, 8080, 8443, 3306, 5432, 6379, 27017]
        }
    
    def synonym_replacement(self, text: str, num_replacements: int = 1) -> str:
        """
        同义词替换
        
        Args:
            text: 原始文本
            num_replacements: 替换次数
            
        Returns:
            增强后的文本
        """
        words = text.split()
        new_words = words.copy()
        
        replacements_made = 0
        for i, word in enumerate(words):
            if replacements_made >= num_replacements:
                break
                
            word_lower = word.lower()
            for original, synonyms in self.error_synonyms.items():
                if original in word_lower and random.random() < 0.5:
                    synonym = random.choice(synonyms)
                    new_words[i] = word.replace(original, synonym)
                    replacements_made += 1
                    break
        
        return ' '.join(new_words)
    
    def number_replacement(self, text: str) -> str:
        """
        数字替换
        
        Args:
            text: 原始文本
            
        Returns:
            增强后的文本
        """
        # 替换HTTP状态码
        text = re.sub(r'\b(4|5)\d{2}\b', 
                     lambda m: str(random.choice(self.number_ranges['http_codes'])), 
                     text)
        
        # 替换超时时间
        text = re.sub(r'\b\d+\s*(s|sec|second|秒)\b', 
                     lambda m: f"{random.choice(self.number_ranges['timeouts'])}s", 
                     text)
        
        # 替换端口号
        text = re.sub(r':\d{2,5}\b', 
                     lambda m: f":{random.choice(self.number_ranges['ports'])}", 
                     text)
        
        return text
    
    def random_insertion(self, text: str) -> str:
        """
        随机插入错误相关词汇
        
        Args:
            text: 原始文本
            
        Returns:
            增强后的文本
        """
        insertion_words = ['suddenly', 'unexpectedly', 'immediately', 
                          '突然', '意外地', '立即', '马上']
        
        words = text.split()
        if len(words) > 1:
            insert_pos = random.randint(0, len(words))
            insert_word = random.choice(insertion_words)
            words.insert(insert_pos, insert_word)
        
        return ' '.join(words)
    
    def random_deletion(self, text: str, deletion_prob: float = 0.1) -> str:
        """
        随机删除词汇
        
        Args:
            text: 原始文本
            deletion_prob: 删除概率
            
        Returns:
            增强后的文本
        """
        words = text.split()
        if len(words) <= 2:
            return text
        
        new_words = []
        for word in words:
            if random.random() > deletion_prob:
                new_words.append(word)
        
        return ' '.join(new_words) if new_words else text
    
    def back_translation_simulation(self, text: str) -> str:
        """
        模拟回译（简化版）
        
        Args:
            text: 原始文本
            
        Returns:
            增强后的文本
        """
        # 简单的词序调整
        words = text.split()
        if len(words) > 3:
            # 随机交换相邻词汇
            for _ in range(random.randint(1, 2)):
                i = random.randint(0, len(words) - 2)
                words[i], words[i + 1] = words[i + 1], words[i]
        
        return ' '.join(words)
    
    def augment_single_text(self, text: str, label: str, num_augmentations: int = 3) -> List[Tuple[str, str]]:
        """
        对单条文本进行增强
        
        Args:
            text: 原始文本
            label: 标签
            num_augmentations: 增强数量
            
        Returns:
            增强后的文本-标签对列表
        """
        augmented_data = [(text, label)]  # 包含原始数据
        
        augmentation_methods = [
            self.synonym_replacement,
            self.number_replacement,
            self.random_insertion,
            self.random_deletion,
            self.back_translation_simulation
        ]
        
        for i in range(num_augmentations):
            # 随机选择增强方法
            method = random.choice(augmentation_methods)
            augmented_text = method(text)
            
            # 确保增强后的文本不为空且与原文不同
            if augmented_text.strip() and augmented_text != text:
                augmented_data.append((augmented_text, label))
        
        return augmented_data
    
    def augment_dataset(self, 
                       texts: List[str], 
                       labels: List[str], 
                       target_samples_per_class: int = 10) -> Tuple[List[str], List[str]]:
        """
        对整个数据集进行增强
        
        Args:
            texts: 文本列表
            labels: 标签列表
            target_samples_per_class: 每个类别的目标样本数
            
        Returns:
            增强后的文本和标签列表
        """
        logger.info(f"开始数据增强，目标每类 {target_samples_per_class} 个样本...")
        
        # 统计每个类别的样本数
        label_counts = {}
        label_texts = {}
        
        for text, label in zip(texts, labels):
            if label not in label_counts:
                label_counts[label] = 0
                label_texts[label] = []
            label_counts[label] += 1
            label_texts[label].append(text)
        
        logger.info(f"原始数据分布: {label_counts}")
        
        augmented_texts = []
        augmented_labels = []
        
        for label, count in label_counts.items():
            current_texts = label_texts[label]
            
            # 添加原始数据
            augmented_texts.extend(current_texts)
            augmented_labels.extend([label] * len(current_texts))
            
            # 如果样本数不足，进行增强
            if count < target_samples_per_class:
                needed_samples = target_samples_per_class - count
                
                for i in range(needed_samples):
                    # 随机选择一个原始样本进行增强
                    source_text = random.choice(current_texts)
                    augmented_pairs = self.augment_single_text(source_text, label, 1)
                    
                    # 添加增强样本（跳过原始样本）
                    for aug_text, aug_label in augmented_pairs[1:]:
                        augmented_texts.append(aug_text)
                        augmented_labels.append(aug_label)
                        if len(augmented_texts) - len(current_texts) >= needed_samples:
                            break
        
        logger.info(f"增强后数据量: {len(augmented_texts)}")
        
        # 统计增强后的分布
        final_counts = {}
        for label in augmented_labels:
            final_counts[label] = final_counts.get(label, 0) + 1
        
        logger.info(f"增强后数据分布: {final_counts}")
        
        return augmented_texts, augmented_labels

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据增强工具')
    parser.add_argument('--input', type=str, required=True, help='输入数据文件')
    parser.add_argument('--output', type=str, required=True, help='输出数据文件')
    parser.add_argument('--text-column', type=str, default='响应内容', help='文本列名')
    parser.add_argument('--label-column', type=str, default='报错原因', help='标签列名')
    parser.add_argument('--target-samples', type=int, default=10, help='每类目标样本数')
    
    args = parser.parse_args()
    
    try:
        # 读取数据
        logger.info(f"读取数据: {args.input}")
        if args.input.endswith('.xlsx'):
            df = pd.read_excel(args.input)
        elif args.input.endswith('.csv'):
            df = pd.read_csv(args.input)
        else:
            raise ValueError("不支持的文件格式")
        
        texts = df[args.text_column].astype(str).tolist()
        labels = df[args.label_column].astype(str).tolist()
        
        # 数据增强
        augmenter = DataAugmenter()
        augmented_texts, augmented_labels = augmenter.augment_dataset(
            texts, labels, args.target_samples
        )
        
        # 保存增强后的数据
        output_df = pd.DataFrame({
            args.text_column: augmented_texts,
            args.label_column: augmented_labels
        })
        
        if args.output.endswith('.xlsx'):
            output_df.to_excel(args.output, index=False)
        elif args.output.endswith('.csv'):
            output_df.to_csv(args.output, index=False)
        
        logger.info(f"增强后的数据已保存到: {args.output}")
        logger.info(f"原始样本数: {len(texts)}")
        logger.info(f"增强后样本数: {len(augmented_texts)}")
        logger.info(f"增强倍数: {len(augmented_texts) / len(texts):.2f}x")
        
    except Exception as e:
        logger.error(f"数据增强失败: {e}")
        raise

if __name__ == "__main__":
    main()
