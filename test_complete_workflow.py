"""
完整工作流测试脚本

测试整个应用服务报错根因分析模型的完整流程
"""

import os
import sys
import logging
import pandas as pd
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_data_processing():
    """测试数据处理流程"""
    logger.info("=" * 60)
    logger.info("测试数据处理流程")
    logger.info("=" * 60)
    
    try:
        # 运行数据处理脚本
        os.system("uv run python data_process.py")
        
        # 检查输出文件
        if os.path.exists("processed_data.xlsx"):
            df = pd.read_excel("processed_data.xlsx")
            logger.info(f"✓ 数据处理成功，处理后数据量: {len(df)}")
            return True
        else:
            logger.error("✗ 数据处理失败，未生成processed_data.xlsx")
            return False
            
    except Exception as e:
        logger.error(f"✗ 数据处理过程中发生错误: {e}")
        return False

def test_data_augmentation():
    """测试数据增强流程"""
    logger.info("=" * 60)
    logger.info("测试数据增强流程")
    logger.info("=" * 60)
    
    try:
        # 运行数据增强脚本
        os.system("uv run python scripts/data_augmentation.py --input processed_data.xlsx --output augmented_data.xlsx --target-samples 5")
        
        # 检查输出文件
        if os.path.exists("augmented_data.xlsx"):
            df_original = pd.read_excel("processed_data.xlsx")
            df_augmented = pd.read_excel("augmented_data.xlsx")
            logger.info(f"✓ 数据增强成功，原始数据: {len(df_original)}, 增强后: {len(df_augmented)}")
            return True
        else:
            logger.error("✗ 数据增强失败，未生成augmented_data.xlsx")
            return False
            
    except Exception as e:
        logger.error(f"✗ 数据增强过程中发生错误: {e}")
        return False

def test_model_training():
    """测试模型训练流程"""
    logger.info("=" * 60)
    logger.info("测试模型训练流程")
    logger.info("=" * 60)
    
    try:
        # 使用增强后的数据训练模型（如果存在）
        data_file = "augmented_data.xlsx" if os.path.exists("augmented_data.xlsx") else "processed_data.xlsx"
        
        # 运行训练脚本
        os.system(f"uv run python train_model.py --data {data_file} --output models/error_classifier_test --evaluate")
        
        # 检查模型文件
        model_files = [
            "models/error_classifier_test/model.pth",
            "models/error_classifier_test/config.json",
            "models/error_classifier_test/processor_config.json"
        ]
        
        all_exist = all(os.path.exists(f) for f in model_files)
        
        if all_exist:
            logger.info("✓ 模型训练成功，所有必要文件已生成")
            return True
        else:
            logger.error("✗ 模型训练失败，缺少必要文件")
            return False
            
    except Exception as e:
        logger.error(f"✗ 模型训练过程中发生错误: {e}")
        return False

def test_prediction_service():
    """测试预测服务"""
    logger.info("=" * 60)
    logger.info("测试预测服务")
    logger.info("=" * 60)
    
    try:
        # 测试单条预测
        test_texts = [
            "HTTP 500 Internal Server Error",
            "Connection timeout after 30 seconds",
            "Authentication failed: invalid token"
        ]
        
        model_path = "models/error_classifier_test" if os.path.exists("models/error_classifier_test") else "models/error_classifier"
        
        success_count = 0
        for text in test_texts:
            try:
                # 运行预测命令
                result = os.system(f'uv run python predict_service.py --model {model_path} --mode single --text "{text}"')
                if result == 0:
                    success_count += 1
                    logger.info(f"✓ 预测成功: {text}")
                else:
                    logger.warning(f"✗ 预测失败: {text}")
            except Exception as e:
                logger.warning(f"✗ 预测错误: {text} - {e}")
        
        if success_count > 0:
            logger.info(f"✓ 预测服务测试完成，成功率: {success_count}/{len(test_texts)}")
            return True
        else:
            logger.error("✗ 预测服务测试失败，所有预测都失败")
            return False
            
    except Exception as e:
        logger.error(f"✗ 预测服务测试过程中发生错误: {e}")
        return False

def test_project_structure():
    """测试项目结构完整性"""
    logger.info("=" * 60)
    logger.info("测试项目结构完整性")
    logger.info("=" * 60)
    
    required_files = [
        "README.md",
        "pyproject.toml",
        "Dockerfile",
        "docker-compose.yml",
        "core/__init__.py",
        "core/data_processor.py",
        "core/model_trainer.py",
        "core/predictor.py",
        "train_model.py",
        "predict_service.py",
        "data_process.py",
        "config/model_config.json",
        "scripts/setup.py",
        "scripts/data_augmentation.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if not missing_files:
        logger.info("✓ 项目结构完整，所有必要文件都存在")
        return True
    else:
        logger.error(f"✗ 项目结构不完整，缺少文件: {missing_files}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    logger.info("=" * 60)
    logger.info("测试报告")
    logger.info("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {total_tests - passed_tests}")
    logger.info(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    logger.info("\n详细结果:")
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"  {test_name}: {status}")
    
    # 保存测试报告
    report = {
        "total_tests": total_tests,
        "passed_tests": passed_tests,
        "failed_tests": total_tests - passed_tests,
        "pass_rate": passed_tests/total_tests*100,
        "detailed_results": results
    }
    
    import json
    with open("test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    logger.info(f"\n测试报告已保存到: test_report.json")
    
    return passed_tests == total_tests

def main():
    """主函数"""
    logger.info("开始完整工作流测试...")
    
    # 创建必要的目录
    os.makedirs("models", exist_ok=True)
    os.makedirs("data", exist_ok=True)
    os.makedirs("outputs", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    # 执行测试
    test_results = {}
    
    # 1. 测试项目结构
    test_results["项目结构完整性"] = test_project_structure()
    
    # 2. 测试数据处理
    if os.path.exists("source.xlsx"):
        test_results["数据处理流程"] = test_data_processing()
        
        # 3. 测试数据增强
        if test_results.get("数据处理流程", False):
            test_results["数据增强流程"] = test_data_augmentation()
        
        # 4. 测试模型训练
        if test_results.get("数据处理流程", False):
            test_results["模型训练流程"] = test_model_training()
        
        # 5. 测试预测服务
        test_results["预测服务"] = test_prediction_service()
    else:
        logger.warning("未找到source.xlsx文件，跳过数据相关测试")
        test_results["数据处理流程"] = False
        test_results["数据增强流程"] = False
        test_results["模型训练流程"] = False
        test_results["预测服务"] = test_prediction_service()
    
    # 生成测试报告
    all_passed = generate_test_report(test_results)
    
    if all_passed:
        logger.info("\n🎉 所有测试通过！项目构建成功！")
        logger.info("\n可以开始使用以下命令:")
        logger.info("  训练模型: uv run python train_model.py --data source.xlsx")
        logger.info("  预测服务: uv run python predict_service.py --model models/error_classifier --mode single")
        logger.info("  Docker部署: docker-compose up -d")
    else:
        logger.error("\n❌ 部分测试失败，请检查错误信息并修复问题")
        sys.exit(1)

if __name__ == "__main__":
    main()
