# 文档路径: 07-developer-documentation > 02-offline-inference > 01-llm-class > LLM 类 > 返回

## 返回


一个包含 `RequestOutput` 对象的列表，这些对象包含生成的响应，其顺序与输入消息的顺序相同。

> encode(prompts: [str](https://docs.python.org/3/library/stdtypes.html#str), pooling_params: PoolingParams | [Sequence](https://docs.python.org/3/library/typing.html#typing.Sequence)[PoolingParams] | [None](https://docs.python.org/3/library/constants.html#None) = None, prompt_token_ids: [List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)] | [None](https://docs.python.org/3/library/constants.html#None) = None, use_tqdm: [bool](https://docs.python.org/3/library/functions.html#bool) = True, lora_request: [List](https://docs.python.org/3/library/typing.html#typing.List)[LoRARequest] | LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [List](https://docs.python.org/3/library/typing.html#typing.List)[EmbeddingRequestOutput] 
[[source]](https://docs.vllm.ai/en/latest/_modules/vllm/entrypoints/llm.html#LLM.encode)

> encode(prompts: [List](https://docs.python.org/3/library/typing.html#typing.List)[[str](https://docs.python.org/3/library/stdtypes.html#str)], pooling_params: PoolingParams | [Sequence](https://docs.python.org/3/library/typing.html#typing.Sequence)[PoolingParams] | [None](https://docs.python.org/3/library/constants.html#None) = None, prompt_token_ids: [List](https://docs.python.org/3/library/typing.html#typing.List)[[List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)]] | [None](https://docs.python.org/3/library/constants.html#None) = None, use*tqdm: [bool](https://docs.python.org/3/library/functions.html#bool) = True, lora_request: [List](https://docs.python.org/3/library/typing.html#typing.List)[LoRARequest] | LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [List](https://docs.python.org/3/library/typing.html#typing.List)[EmbeddingRequestOutput]
> encode(prompts: [str](https://docs.python.org/3/library/stdtypes.html#str) | [None](https://docs.python.org/3/library/constants.html#None) = None, pooling_params: PoolingParams | [Sequence](https://docs.python.org/3/library/typing.html#typing.Sequence)[PoolingParams] | [None](https://docs.python.org/3/library/constants.html#None) = None, *, prompt*token_ids: [List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)], use_tqdm: [bool](https://docs.python.org/3/library/functions.html#bool) = True, lora_request: [List](https://docs.python.org/3/library/typing.html#typing.List)[LoRARequest] | LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [List](https://docs.python.org/3/library/typing.html#typing.List)[EmbeddingRequestOutput]
> encode(prompts: [List](https://docs.python.org/3/library/typing.html#typing.List)[[str](https://docs.python.org/3/library/stdtypes.html#str)] | [None](https://docs.python.org/3/library/constants.html#None) = None, pooling_params: PoolingParams | [Sequence](https://docs.python.org/3/library/typing.html#typing.Sequence)[PoolingParams] | [None](https://docs.python.org/3/library/constants.html#None) = None, *, prompt_token_ids: [List](https://docs.python.org/3/library/typing.html#typing.List)[[List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)]], use_tqdm: [bool](https://docs.python.org/3/library/functions.html#bool) = True, lora_request: [List](https://docs.python.org/3/library/typing.html#typing.List)[LoRARequest] | LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [List](https://docs.python.org/3/library/typing.html#typing.List)[EmbeddingRequestOutput]
> encode(prompts: [None](https://docs.python.org/3/library/constants.html#None), pooling_params: [None](https://docs.python.org/3/library/constants.html#None), prompt_token_ids: [List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)] | [List](https://docs.python.org/3/library/typing.html#typing.List)[[List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)]], use_tqdm: [bool](https://docs.python.org/3/library/functions.html#bool) = True, lora_request: [List](https://docs.python.org/3/library/typing.html#typing.List)[LoRARequest] | LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [List](https://docs.python.org/3/library/typing.html#typing.List)[EmbeddingRequestOutput]
> encode(prompts: [PromptType](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.PromptType) | [Sequence](https://docs.python.org/3/library/typing.html#typing.Sequence)[[PromptType](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.PromptType)], /, \*, pooling_params: PoolingParams | [Sequence](https://docs.python.org/3/library/typing.html#typing.Sequence)[PoolingParams] | [None](https://docs.python.org/3/library/constants.html#None) = None, use_tqdm: [bool](https://docs.python.org/3/library/functions.html#bool) = True, lora_request: [List](https://docs.python.org/3/library/typing.html#typing.List)[LoRARequest] | LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [List](https://docs.python.org/3/library/typing.html#typing.List)[EmbeddingRequestOutput]
> 生成输入提示的补全。

这个类会自动对给定的提示进行批处理，同时考虑内存限制。为了获得最佳性能，请将所有提示放入一个列表中并将其传递给此方法。
