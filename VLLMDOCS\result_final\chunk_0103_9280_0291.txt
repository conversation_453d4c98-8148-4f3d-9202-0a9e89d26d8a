# 文档路径: 07-developer-documentation > 04-vllm-paged-attention > vLLM  分页注意力 > 询问 (Query)

## 询问 (Query)


- 本节将介绍查询数据如何存储在内存中以及如何被每个线程获取。如上所述，每个线程组获取一个查询 token 数据，而每个线程本身仅处理一个查询 token 数据的一部分。在每个 warp 中，每个线程组将获取相同的查询 token 数据，但会将其与不同的键 token 数据相乘。

```plain
    const scalar_t* q_ptr = q + seq_idx * q_stride + head_idx * HEAD_SIZE;
```

![图片](/img/docs/07-04/query.png)
1 个头中的 1 个 token 的查询数据

- 每个线程定义自己的 `q_ptr`，它指向全局内存中分配的查询 token 数据。例如，如果 `VEC_SIZE` 为 4 且 `HEAD_SIZE` 为 128，则 `q_ptr` 指向包含总共 128 个元素的数据，这些元素被分为 128 / 4 = 32 个向量。

![图片](/img/docs/07-04/q_vecs.png)
1 个线程组中的 `q_vecs`

```plain
    __shared__ Q_vec q_vecs[THREAD_GROUP_SIZE][NUM_VECS_PER_THREAD];
```

- 接下来，我们需要将`q_ptr`指向的全局内存数据读取到共享内存中作为 `q_vecs`。需要注意的是，每个 vec 都被分配到不同的行。例如，如果 `THREAD_GROUP_SIZE` 为 2，则线程 0 将处理第 0 行 vec，而线程 1 处理第 1 行 vec。通过这种方式读取查询数据，相邻线程（如线程 0 和线程 1）可以读取相邻内存，实现内存合并以提高性能。
