# 文档路径: 03-models > 03-enabling-multimodal-inputs > 启用多模态输入 > 2. 注册输入映射器

## 2. 注册输入映射器

对于模型接受的每种模态类型，您需要使用 `MULTIMODAL_REGISTRY.register_input_mapper` 来装饰模型类。该装饰器会接收一个函数，该函数负责将多模态输入映射到您之前在 `forward()` 中预定义的关键字参数。

```diff
from vllm.model_executor.models.interfaces import SupportsMultiModal
+ from vllm.multimodal import MULTIMODAL_REGISTRY


+ @MULTIMODAL_REGISTRY.register_image_input_mapper()
class YourModelForImage2Seq(nn.Module, SupportsMultiModal):
```

核心 vLLM 库中的每种模式都有一个默认映射器。如果您没有提供自己的函数，将使用此输入映射器。

**另见**

`输入处理管道`
