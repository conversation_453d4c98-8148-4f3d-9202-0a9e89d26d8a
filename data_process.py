import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import re
import jieba
from wordcloud import WordCloud
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def analyze_data(input_file, response_col='响应内容', error_col='报错原因'):
    """
    分析应用服务报错数据的基本特征

    Args:
        input_file (str): 输入Excel文件路径
        response_col (str): 响应内容列名
        error_col (str): 报错原因列名

    Returns:
        dict: 数据分析结果
    """
    print("=" * 60)
    print("开始数据分析...")
    print("=" * 60)

    try:
        # 读取数据
        df = pd.read_excel(input_file)
        print(f"✓ 成功读取文件：{input_file}")
        print(f"✓ 数据形状：{df.shape}")
        print(f"✓ 列名：{list(df.columns)}")

        # 检查必要列是否存在
        if response_col not in df.columns:
            raise ValueError(f"列 '{response_col}' 不存在")
        if error_col not in df.columns:
            raise ValueError(f"列 '{error_col}' 不存在")

        # 基本统计信息
        print("\n" + "=" * 40)
        print("基本统计信息")
        print("=" * 40)
        print(f"总样本数：{len(df)}")
        print(f"响应内容缺失值：{df[response_col].isnull().sum()}")
        print(f"报错原因缺失值：{df[error_col].isnull().sum()}")

        # 去除缺失值
        df_clean = df.dropna(subset=[response_col, error_col])
        print(f"清洗后样本数：{len(df_clean)}")

        # 分析报错原因分布
        print("\n" + "=" * 40)
        print("报错原因分布")
        print("=" * 40)
        error_counts = df_clean[error_col].value_counts()
        print(f"唯一报错原因数量：{len(error_counts)}")
        print("前10个最常见的报错原因：")
        for i, (error, count) in enumerate(error_counts.head(10).items(), 1):
            print(f"{i:2d}. {error[:50]}... ({count}次)")

        # 分析响应内容长度分布
        print("\n" + "=" * 40)
        print("响应内容长度分析")
        print("=" * 40)
        response_lengths = df_clean[response_col].astype(str).str.len()
        print(f"平均长度：{response_lengths.mean():.2f}")
        print(f"中位数长度：{response_lengths.median():.2f}")
        print(f"最短长度：{response_lengths.min()}")
        print(f"最长长度：{response_lengths.max()}")
        print(f"标准差：{response_lengths.std():.2f}")

        # 分析报错原因长度分布
        print("\n" + "=" * 40)
        print("报错原因长度分析")
        print("=" * 40)
        error_lengths = df_clean[error_col].astype(str).str.len()
        print(f"平均长度：{error_lengths.mean():.2f}")
        print(f"中位数长度：{error_lengths.median():.2f}")
        print(f"最短长度：{error_lengths.min()}")
        print(f"最长长度：{error_lengths.max()}")
        print(f"标准差：{error_lengths.std():.2f}")

        # 检查数据重复情况
        print("\n" + "=" * 40)
        print("数据重复分析")
        print("=" * 40)
        duplicates = df_clean.duplicated(subset=[response_col, error_col]).sum()
        print(f"完全重复的样本数：{duplicates}")

        response_duplicates = df_clean.duplicated(subset=[response_col]).sum()
        print(f"响应内容重复的样本数：{response_duplicates}")

        # 分析常见关键词
        print("\n" + "=" * 40)
        print("响应内容关键词分析")
        print("=" * 40)

        # 提取所有响应内容的关键词
        all_responses = ' '.join(df_clean[response_col].astype(str))
        # 简单的关键词提取（基于常见的错误关键词）
        error_keywords = ['error', 'exception', 'failed', 'timeout', 'connection',
                         'null', 'invalid', 'unauthorized', 'forbidden', 'not found',
                         '错误', '异常', '失败', '超时', '连接', '无效', '未授权', '禁止', '未找到']

        keyword_counts = {}
        for keyword in error_keywords:
            count = all_responses.lower().count(keyword.lower())
            if count > 0:
                keyword_counts[keyword] = count

        print("常见错误关键词出现次数：")
        for keyword, count in sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {keyword}: {count}次")

        # 返回分析结果
        analysis_result = {
            'total_samples': len(df),
            'clean_samples': len(df_clean),
            'unique_errors': len(error_counts),
            'error_distribution': error_counts,
            'response_length_stats': {
                'mean': response_lengths.mean(),
                'median': response_lengths.median(),
                'min': response_lengths.min(),
                'max': response_lengths.max(),
                'std': response_lengths.std()
            },
            'error_length_stats': {
                'mean': error_lengths.mean(),
                'median': error_lengths.median(),
                'min': error_lengths.min(),
                'max': error_lengths.max(),
                'std': error_lengths.std()
            },
            'duplicates': duplicates,
            'keyword_counts': keyword_counts,
            'clean_data': df_clean
        }

        print("\n" + "=" * 60)
        print("数据分析完成！")
        print("=" * 60)

        return analysis_result

    except Exception as e:
        print(f"❌ 数据分析过程中发生错误：{e}")
        raise

def preprocess_data(df, response_col='响应内容', error_col='报错原因'):
    """
    数据预处理函数

    Args:
        df (pd.DataFrame): 原始数据
        response_col (str): 响应内容列名
        error_col (str): 报错原因列名

    Returns:
        pd.DataFrame: 预处理后的数据
    """
    print("\n" + "=" * 40)
    print("开始数据预处理...")
    print("=" * 40)

    # 复制数据避免修改原始数据
    df_processed = df.copy()

    # 1. 去除缺失值
    original_len = len(df_processed)
    df_processed = df_processed.dropna(subset=[response_col, error_col])
    print(f"✓ 去除缺失值：{original_len} -> {len(df_processed)}")

    # 2. 去除重复数据
    original_len = len(df_processed)
    df_processed = df_processed.drop_duplicates(subset=[response_col, error_col])
    print(f"✓ 去除重复数据：{original_len} -> {len(df_processed)}")

    # 3. 文本清洗
    def clean_text(text):
        """清洗文本数据"""
        if pd.isna(text):
            return ""

        text = str(text)
        # 去除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 去除首尾空白
        text = text.strip()
        return text

    df_processed[response_col] = df_processed[response_col].apply(clean_text)
    df_processed[error_col] = df_processed[error_col].apply(clean_text)
    print("✓ 完成文本清洗")

    # 4. 过滤过短或过长的文本
    min_length = 5
    max_length = 10000

    original_len = len(df_processed)
    response_lengths = df_processed[response_col].str.len()
    valid_mask = (response_lengths >= min_length) & (response_lengths <= max_length)
    df_processed = df_processed[valid_mask]
    print(f"✓ 过滤异常长度文本：{original_len} -> {len(df_processed)}")

    # 5. 重置索引
    df_processed = df_processed.reset_index(drop=True)

    print(f"✓ 预处理完成，最终数据量：{len(df_processed)}")
    print("=" * 40)

    return df_processed

def save_processed_data(df, output_file):
    """
    保存预处理后的数据

    Args:
        df (pd.DataFrame): 预处理后的数据
        output_file (str): 输出文件路径
    """
    try:
        df.to_excel(output_file, index=False)
        print(f"✓ 预处理后的数据已保存到：{output_file}")
    except Exception as e:
        print(f"❌ 保存数据时发生错误：{e}")
        raise

def deduplicate_and_save_excel(input_file, output_file, col1_name, col2_name):
    """
    读取Excel文件，获取指定两列数据，对这两列数据进行去重，
    并将去重后的数据写入一个新的Excel文件。

    Args:
        input_file (str): 输入Excel文件的路径。
        output_file (str): 输出Excel文件的路径。
        col1_name (str): 要处理的第一列的名称。
        col2_name (str): 要处理的第二列的名称。
    """
    try:
        # 1. 读取Excel文件
        df = pd.read_excel(input_file)
        print(f"成功读取文件：'{input_file}'")
        print(f"原始数据共 {len(df)} 行。")

        # 检查指定的列是否存在
        if col1_name not in df.columns:
            raise ValueError(f"输入文件中不存在列名：'{col1_name}'")
        if col2_name not in df.columns:
            raise ValueError(f"输入文件中不存在列名：'{col2_name}'")

        # 2. 获取这两列数据并创建新的DataFrame
        # 这里我们选择这两列以及它们的原始列名，保持数据结构
        df_selected = df[[col1_name, col2_name]]

        # 3. 对这两列数据的组合进行去重
        # subset 参数指定了根据哪些列进行去重
        df_deduplicated = df_selected.drop_duplicates(subset=[col1_name, col2_name])

        print(f"去重后数据共 {len(df_deduplicated)} 行。")
        print(f"共去除了 {len(df) - len(df_deduplicated)} 行重复数据。")

        # 4. 写入新的Excel文件
        df_deduplicated.to_excel(output_file, index=False) # index=False 不写入DataFrame的索引
        print(f"去重后的数据已成功写入到文件：'{output_file}'")

    except FileNotFoundError:
        print(f"错误：文件 '{input_file}' 未找到。请检查文件路径是否正确。")
    except ValueError as ve:
        print(f"数据错误：{ve}")
    except Exception as e:
        print(f"发生了一个未知错误：{e}")

# --- 主程序 ---
if __name__ == "__main__":
    # 配置文件路径
    input_excel_file = 'source.xlsx'
    processed_file = 'processed_data.xlsx'

    # 列名配置
    response_column = '响应内容'
    error_column = '报错原因'

    try:
        # 1. 数据分析
        analysis_result = analyze_data(input_excel_file, response_column, error_column)

        # 2. 数据预处理
        processed_data = preprocess_data(analysis_result['clean_data'], response_column, error_column)

        # 3. 保存预处理后的数据
        save_processed_data(processed_data, processed_file)

        print("\n" + "=" * 60)
        print("数据分析和预处理流程完成！")
        print(f"原始数据：{analysis_result['total_samples']} 条")
        print(f"最终数据：{len(processed_data)} 条")
        print(f"数据质量提升：{len(processed_data)/analysis_result['total_samples']*100:.1f}% 保留率")
        print("=" * 60)

    except Exception as e:
        print(f"❌ 程序执行失败：{e}")
        raise