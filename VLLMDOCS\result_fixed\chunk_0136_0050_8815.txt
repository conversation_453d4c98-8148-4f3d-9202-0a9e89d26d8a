# 文档路径: 10-tutorials > 01-vLLM-stepbysteb > vLLM 入门教程：零基础分步指南 > 三、启动 vLLM 服务器 > 3.2 启动命令行

## 三、启动 vLLM 服务器

### 3.2 启动命令行


创建兼容 OpenAI API 接口的服务器。运行以下命令启动服务器：

```bash
python3 -m vllm.entrypoints.openai.api_server --model /input0/Qwen-1_8B-Chat/ --host 0.0.0.0 --port 8080 --dtype auto --max-num-seqs 32 --max-model-len 4096 --tensor-parallel-size 1 --trust-remote-code
```

成功启动后，您将看到类似以下的输出：

![图片](/img/docs/02-tutorials/start.png)

vLLM 现在可以作为实现 OpenAI API 协议的服务器进行部署，默认情况下它将在 `http://localhost:8080` 启动服务器。您可以通过 `--host` 和 `--port` 参数指定其他地址。
