# 文档路径: 01-getting-started > 08-quickstart > 快速入门

本指南将说明如何使用 vLLM 进行以下操作：

- 对数据集进行离线批量推理；
- 为大语言模型构建 API 服务器；
- 启动与 OpenAI 兼容的 API 服务器。

在继续进行本指南之前，请务必完成[安装说明](https://docs.vllm.ai/en/latest/getting_started/installation.html#installation)。

**注意**

默认情况下，vLLM 从 [HuggingFace](https://huggingface.co/) 下载模型。如果您想在以下示例中使用 [ModelScope](https://www.modelscope.cn) 中的模型，请设置环境变量：

```shell
export VLLM_USE_MODELSCOPE=True
```