# 文档路径: 03-models > 06-using-vlms > 使用 VLM > 在线推理

## 在线推理


您可以使用兼容 [OpenAI Vision API](https://platform.openai.com/docs/guides/vision) 的 vLLM HTTP 服务器提供视觉语言模型。

以下是一个关于如何使用 vLLM 的 OpenAI 兼容 API 服务器启动同一个 `microsoft/Phi-3.5-vision-instruct` 的示例。

```bash
vllm serve microsoft/Phi-3.5-vision-instruct --task generate \
  --trust-remote-code --max-model-len 4096 --limit-mm-per-prompt image=2
```

**重要**
由于 OpenAI Vision API 基于 [Chat](https://platform.openai.com/docs/api-reference/chat) API，因此需要聊天模板来启动 API 服务器。

虽然 Phi-3.5-Vision 自带了聊天模板，但如果您使用的模型的分词器没有包含聊天模板，您可能需要自行提供。聊天模板通常可以根据 HuggingFace 存储库中模型文档的说明来推断。例如，LLaVA-1.5（`llava-hf/llava-1.5-7b-hf`) 模型就需要一个聊天模板，您可以[在这里](https://github.com/vllm-project/vllm/blob/main/examples/template_llava.jinja)找到该模板。

要使用服务器，您可以使用 OpenAI 客户端，如下例所示:

```python
from openai import OpenAI
openai_api_key = "EMPTY"
openai_api_base = "http://localhost:8000/v1"


client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)
# Single-image input inference
# 单图像输入推理
image_url = "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"


chat_response = client.chat.completions.create(
    model="microsoft/Phi-3.5-vision-instruct",
    messages=[{
        "role": "user",
        "content": [
            # NOTE: The prompt formatting with the image token `<image>` is not needed
            # 注意：不需要使用图像标记 `<image>` 格式化提示
            # since the prompt will be processed automatically by the API server.
            # 因为 API 服务器将自动处理提示。


            {"type": "text", "text": "What’s in this image?"},
            {"type": "image_url", "image_url": {"url": image_url}},
        ],
    }],
)
print("Chat completion output:", chat_response.choices[0].message.content)


# Multi-image input inference
# 多图像输入推理
image_url_duck = "https://upload.wikimedia.org/wikipedia/commons/d/da/2015_Kaczka_krzy%C5%BCowka_w_wodzie_%28samiec%29.jpg"
image_url_lion = "https://upload.wikimedia.org/wikipedia/commons/7/77/002_The_lion_king_Snyggve_in_the_Serengeti_National_Park_Photo_by_Giles_Laurent.jpg"


chat_response = client.chat.completions.create(
    model="microsoft/Phi-3.5-vision-instruct",
    messages=[{
        "role": "user",
        "content": [
            {"type": "text", "text": "What are the animals in these images?"},
            {"type": "image_url", "image_url": {"url": image_url_duck}},
            {"type": "image_url", "image_url": {"url": image_url_lion}},
        ],
    }],
)
print("Chat completion output:", chat_response.choices[0].message.content)


```

完整的代码示例可以在 [examples/openai_api_client_for_multimodal.py](https://github.com/vllm-project/vllm/blob/main/examples/openai_api_client_for_multimodal.py) 中找到。

**注意：**

默认情况下，通过 http url 获取图像的超时时间为 `5` 秒。您可以通过设置环境变量来覆盖这个设置：

```plain
export VLLM_IMAGE_FETCH_TIMEOUT=<timeout>
```

**注意：**
在 API 请求中无需格式化提示词，因为它将由服务器进行处理。
