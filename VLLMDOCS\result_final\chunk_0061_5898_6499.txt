# 文档路径: 03-models > 03-enabling-multimodal-inputs > 启用多模态输入 > 4.（可选）注册虚拟数据

## 4.（可选）注册虚拟数据


在启动过程中，虚拟数据被传递到 vLLM 模型以分配内存。默认情况下仅包含文本输入，这可能不适用于多模态模型。在这种情况下，您可以通过 `INPUT_REGISTRY.register_dummy_data` 注册一个工厂方法来定义自己的虚拟数据。

```diff
from vllm.inputs import INPUT_REGISTRY
from vllm.model_executor.models.interfaces import SupportsMultiModal
from vllm.multimodal import MULTIMODAL_REGISTRY


@MULTIMODAL_REGISTRY.register_image_input_mapper()
@MULTIMODAL_REGISTRY.register_max_image_tokens(<your_calculation>)
+ @INPUT_REGISTRY.register_dummy_data(<your_dummy_data_factory>)
class YourModelForImage2Seq(nn.Mo<PERSON>le, SupportsMultiModal):
```

**注意：**
虚拟数据应具有尽可能多的多模态标记，如上一步所述。

以下是一些示例：

- 图像输入（静态特征尺寸）: [LLaVA-1.5 模型](https://github.com/vllm-project/vllm/blob/main/vllm/model_executor/models/llava.py)
- 图像输入（动态特征尺寸）: [LLaVA-NeXT 模型](https://github.com/vllm-project/vllm/blob/main/vllm/model_executor/models/llava_next.py)

**另见**

`输入处理管道`
