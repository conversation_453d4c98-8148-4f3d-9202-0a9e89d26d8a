# 文档路径: 07-developer-documentation > 04-vllm-paged-attention > vLLM  分页注意力 > QK

## QK


- 如下面的伪代码所示，在整个 for 循环块之前，我们获取一个 token 的查询数据并将其存储在 `q_vecs` 中。然后，在外部 for 循环中，我们迭代指向不同 token 的不同 `k_ptrs` ，并在内部 for 循环中准备 `k_vecs` 。最后，我们在 `q_vecs` 和每个 `k_vecs` 之间执行点乘运算。

```plain
    q_vecs = ...
    for ... {
       k_ptr = ...
       for ... {
          k_vecs[i] = ...
       }
       ...
       float qk = scale * Qk_dot<scalar_t, THREAD_GROUP_SIZE>::dot(q_vecs[thread_group_offset], k_vecs);
    }
```

- 如前所述，对于每个线程，它一次仅获取部分查询和键 token 数据。然而，在`Qk_dot<>::dot` 中会发生跨线程组的归约操作。因此，这里返回的 `qk` 不仅仅是部分查询和键 token 之间点乘的结果，实际上是整个查询和键 token 数据之间的完整结果。
- 例如，如果 `HEAD_SIZE` 的值为 128，`THREAD_GROUP_SIZE` 为 2，则每个线程的 `k_vecs` 将包含总共 64 个元素。然而，返回的 qk 实际上是 128 个查询元素和 128 个键元素之间点乘的结果。如果你想了解更多关于点乘和归约的细节，可以参考 `Qk_dot<>::dot` 的实现。不过，为了简单起见，我们不会在本文档中介绍这些内容。
