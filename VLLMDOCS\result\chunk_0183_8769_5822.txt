# 文档路径: 10-tutorials > 04-vllm-langchain-tutorial > 将 LangChain 与 vLLM 结合使用：完整教程 > 1. 安装和设置 vLLM > Docker 安装

## 1. 安装和设置 vLLM

vLLM 配置要求：

操作系统： Linux

Python 版本： Python >= 3.8

GPU 要求：计算能力 >= 7.0 的 GPU（例如 V100、T4、RTX20xx、A100、L4、H100）。

CUDA 版本： vLLM 使用 CUDA 12.1 编译。请确保您的系统正在运行此版本。

如果您没有运行 CUDA 12.1，您可以安装为您的 CUDA 版本编译的 vLLM 版本或将您的 CUDA 升级到版本 12.1。

在继续之前，建议执行一些基本检查以确保一切都安装正确。您可以通过运行以下命令来验证 PyTorch 是否与 CUDA 一起使用：

```python
# Ensure torch is working with CUDA, this should print: True
python -c 'import torch; print(torch.cuda.is_available())'
```

vLLM 是一个 Python 库，还包含预编译的 C++ 和 CUDA (12.1) 二进制文件。但是，如果您需要 CUDA 11.8，则可以使用以下命令安装兼容版本：

```python
# Install vLLM with CUDA 11.8
export VLLM_VERSION=0.6.1.post1
export PYTHON_VERSION=310
pip install https://github.com/vllm-project/vllm/releases/download/v${VLLM_VERSION}/vllm-${VLLM_VERSION}+cu118-cp${PYTHON_VERSION}-cp${PYTHON_VERSION}-manylinux1_x86_64.whl --extra-index-url https://download.pytorch.org/whl/cu118
```


### Docker 安装

对于那些在构建 vLLM 或处理 CUDA 兼容性时遇到问题的人，建议使用 NVIDIA PyTorch Docker 映像。它提供了一个预配置的环境，其中包含正确版本的 CUDA 和其他依赖项：

```python
# Use `--ipc=host` to ensure the shared memory is sufficient
docker run --gpus all -it --rm --ipc=host nvcr.io/nvidia/pytorch:23.10-py3
```

集成过程最终从安装所需的软件包开始。我们建议将 vLLM 升级到最新版本，以避免兼容性问题并受益于最新的改进和功能。

```python
pip install --upgrade --quiet vllm -q
pip install langchain langchain_community -q
```

本教程已经安装 vllm==0.6.4，只需将 langchain 相关包安装完毕。

```
!pip install -U langchain langchain_community -q
```
