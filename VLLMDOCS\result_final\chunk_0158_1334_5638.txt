# 文档路径: 10-tutorials > 04-vllm-langchain-tutorial > 将 LangChain 与 vLLM 结合使用：完整教程 > 2. 配置 vLLM 以与 LangChain 配合使用

## 2. 配置 vLLM 以与 LangChain 配合使用


现在依赖项已安装完毕，我们可以设置 vLLM 并将其连接到 LangChain。为此，我们将从 LangChain 社区集成中导入 VLLM。下面的示例演示了如何使用 vLLM 库初始化模型并将其与 LangChain 集成。

```
import gc
import ctypes
import torch
def clean_memory(deep=False):
    gc.collect()
    if deep:
        ctypes.CDLL("libc.so.6").malloc_trim(0)
    torch.cuda.empty_cache()
```

```
from langchain_community.llms import VLLM

# Initializing the vLLM model
llm = VLLM(
    model="/input0/Qwen2.5-1.5B-Instruct",
    trust_remote_code=True,  # mandatory for Hugging Face models
    max_new_tokens=128,
    top_k=10,
    top_p=0.95,
    temperature=0.8,
)

# Running a simple query
print(llm.invoke("What are the most popular Halloween Costumes?"))
```

以下是使用 vLLM 与 LangChain 时需要考虑的参数列表：

| 参数名称             | 描述                                                                   |
| -------------------- | ---------------------------------------------------------------------- |
| 模型                 | 要使用的 Hugging Face Transformers 模型的名称或路径。                  |
| top_k                | 将采样池限制为前 k 个 token，以提高多样性。默认值为 -1。               |
| top_p                | 使用累积概率来确定要考虑哪些标记，从而支持更一致的输出。默认值为 1.0。 |
| 信任远程代码         | 允许模型执行远程代码，对某些 Hugging Face 模型有用。默认值为 False。   |
| 温度                 | 控制采样的随机性，值越高，输出越多样化。默认值为 1.0。                 |
| 最大新令牌数         | 指定每个输出序列生成的最大标记数。默认值为 512。                       |
| 回调                 | 添加到运行跟踪的回调，对于在生成期间添加日志记录或监控功能很有用。     |
| 标签                 | 添加到运行跟踪的标签可以方便进行分类和调试。                           |
| tensor_parallel_size | 用于分布式张量并行执行的 GPU 数量。默认值为 1。                        |
| 使用光束搜索         | 是否使用集束搜索而不是采样来生成更优化的序列。默认值为 False。         |
| 复制代码             | 保存对 vLLM LLM 调用有效的未明确指定的附加参数。                       |

在此示例中，我们加载 `Qwen2.5-1.5B-Instruct` 模型并配置`max_new_tokens`、`top_k`和 等参数`temperature`。这些设置会影响模型生成文本的方式。
