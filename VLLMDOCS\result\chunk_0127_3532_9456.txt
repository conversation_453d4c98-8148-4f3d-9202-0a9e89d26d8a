# 文档路径: 05-automatic-prefix-caching > 02-implementation > 实现 (Implementation)

PagedAttention 的核心思想是将每个请求的 KV 缓存划分为多个 KV 块。每个块包含固定数量 token 的注意力键和值。PagedAttention 算法允许这些块被存储在非连续的物理内存中，从而通过按需分配内存来消除内存碎片。

为了自动缓存 KV 缓存，我们利用以下关键来观察结果：每个 KV 块都可以通过块中的 token 以及块之前的前缀 token 唯一标识。

```plain
                    Block 1                  Block 2                  Block 3
         [A gentle breeze stirred] [the leaves as children] [laughed in the distance]
Block 1: |<--- block tokens ---->|
Block 2: |<------- prefix ------>| |<--- block tokens --->|
Block 3: |<------------------ prefix -------------------->| |<--- block tokens ---->|
```

在上述例子中，第 1 个块中的 KV 缓存可以通过 token「A gentle breeze stirred」 唯一标识。第 3 个块可以通过块中的 token「laughed in the distance」，以及前缀 token「A gentle breeze stirred the leaves as children」唯一标识。因此，我们可以构建以下一对一映射关系：

```plain
hash(prefix tokens + block tokens) <--> KV Block
```

通过这种映射，我们可以在 vLLM 的 KV 缓存管理中增加另一层间接层。此前，vLLM 中的每个序列都维护了从其逻辑 KV 块到物理块的映射。为了实现 KV 块的自动缓存，我们将逻辑 KV 块映射到其哈希值，并维护所有物理块的全局哈希表。通过这种方式，所有共享相同哈希值的 KV 块（例如，跨两个请求的共享前缀块）可以映射到相同的物理块，并共享内存空间。

这种设计实现了自动前缀缓存，而无需在 KV 块之间维护树状结构。更具体地说，所有的块都相互独立，可以单独分配和释放，这使我们能够像操作系统中的普通缓存一样管理 KV 缓存。