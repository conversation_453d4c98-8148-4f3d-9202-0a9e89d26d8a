# 文档路径: 07-developer-documentation > 06-multi-modality > readme > 多模态

vLLM 通过 [vllm.multimodal](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#module-vllm.multimodal) 包为多模态模型提供实验性支持。

多模态输入可以通过 [vllm.inputs.PromptInputs](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.PromptInputs) 中的 `multi_modal_data` 字段将文本和 token 提示一起传到[支持的模型](https://docs.vllm.ai/en/latest/models/supported_models.html#supported-vlms)中。

目前，vLLM 仅内置对图像数据的支持。您可以按照[该指南](https://docs.vllm.ai/en/latest/dev/multimodal/adding_multimodal_plugin.html#adding-multimodal-plugin)扩展 vLLM 处理其他模态。

想要添加您自己的多模态模型吗？请按照[此处](https://docs.vllm.ai/en/latest/models/enabling_multimodal_inputs.html#enabling-multimodal-inputs)列出的说明进行操作。