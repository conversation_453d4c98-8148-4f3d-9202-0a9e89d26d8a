version: '3.8'

services:
  logmodel:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: logmodel-app
    ports:
      - "8000:8000"
    volumes:
      # 挂载模型目录（持久化训练好的模型）
      - ./models:/app/models
      # 挂载数据目录（输入数据）
      - ./data:/app/data
      # 挂载输出目录（预测结果）
      - ./outputs:/app/outputs
      # 挂载日志目录
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import torch; print('Health check passed')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 可选：添加GPU支持的服务
  logmodel-gpu:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: logmodel-gpu-app
    ports:
      - "8001:8000"
    volumes:
      - ./models:/app/models
      - ./data:/app/data
      - ./outputs:/app/outputs
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - CUDA_VISIBLE_DEVICES=0
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    profiles:
      - gpu

networks:
  default:
    name: logmodel-network
