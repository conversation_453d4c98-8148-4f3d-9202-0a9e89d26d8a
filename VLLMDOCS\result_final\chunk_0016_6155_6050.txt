# 文档路径: 01-getting-started > 04-installation-with-cpu > 使用 CPU 安装 > 从源代码构建

## 从源代码构建


- 首先，安装推荐的编译器。我们建议使用 `gcc/g++ >= 12.3.0` 作为默认编译器，以避免潜在的问题。例如，在 Ubuntu 22.4 上，您可以运行：

```plain
sudo apt-get update  -y
sudo apt-get install -y gcc-12 g++-12 libnuma-dev
sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-12 10 --slave /usr/bin/g++ g++ /usr/bin/g++-12
```

- 其次，安装用于 vLLM CPU 后端构建的 Python 包：

```plain
pip install --upgrade pip
pip install wheel packaging ninja "setuptools>=49.4.0" numpy
pip install -v -r requirements-cpu.txt --extra-index-url https://download.pytorch.org/whl/cpu
```

- 第三步，从源代码构建并安装 oneDNN 库：

```plain
git clone -b rls-v3.5 https://github.com/oneapi-src/oneDNN.git
cmake -B ./oneDNN/build -S ./oneDNN -G Ninja -DONEDNN_LIBRARY_TYPE=STATIC \
    -DONEDNN_BUILD_DOC=OFF \
    -DONEDNN_BUILD_EXAMPLES=OFF \
    -DONEDNN_BUILD_TESTS=OFF \
    -DONEDNN_BUILD_GRAPH=OFF \
    -DONEDNN_ENABLE_WORKLOAD=INFERENCE \
    -DONEDNN_ENABLE_PRIMITIVE=MATMUL
cmake --build ./oneDNN/build --target install --config Release


```

- 最后，构建并安装 vLLM CPU 后端：

```plain
VLLM_TARGET_DEVICE=cpu python setup.py install
```

**注意**

- BF16 是当前 CPU 后端的默认数据类型 （这意味着后端会将 FP16 转换为 BF16），并且与所有支持 AVX512 ISA 的 CPU 兼容。
- AVX512_BF16 是 ISA 的扩展，提供原生的 BF16 数据类型转换和向量积指令，与纯 AVX512 相比会带来一定的性能提升。CPU 后端构建脚本将检查主机 CPU 标志，以确定是否启用 AVX512_BF16。
- 如果要强制启用 AVX512_BF16 进行交叉编译，请在编译前设置环境变量 VLLM_CPU_AVX512BF16=1。
