# 文档路径: 03-models > 07-speculative-decoding-in-vllm > vLLM 中的推测解码 > 用草稿模型进行推测

**警告：**

请注意，vLLM 中的推测解码尚未优化，通常不会为所有的提示数据集或采样参数带来 token 间延迟的减少。我们正在进行优化工作可以在[这个 issue](https://github.com/vllm-project/vllm/issues/4630) 中跟踪进展。

本文档展示了如何在使用 vLLM 时应用[推测解码](https://x.com/karpathy/status/1697318534555336961)。这种技术能够降低在内存密集型的 LLM 推理过程中，各个 token 之间的延迟。

## 用草稿模型进行推测


以下代码展示了在离线模式下配置 vLLM，并使用草稿模型进行推测解码，每次推测 5 个 token。

```python
from vllm import LLM, SamplingParams


prompts = [
    "The future of AI is",
]
sampling_params = SamplingParams(temperature=0.8, top_p=0.95)


llm = LLM(
    model="facebook/opt-6.7b",
    tensor_parallel_size=1,
    speculative_model="facebook/opt-125m",
    num_speculative_tokens=5,
    use_v2_block_manager=True,
)
outputs = llm.generate(prompts, sampling_params)


for output in outputs:
    prompt = output.prompt
    generated_text = output.outputs[0].text
    print(f"Prompt: {prompt!r}, Generated text: {generated_text!r}")
```

若要在线模式下执行相同的操作，请启动服务器：

```bash
python -m vllm.entrypoints.openai.api_server --host 0.0.0.0 --port 8000 --model facebook/opt-6.7b \
--seed 42 -tp 1 --speculative_model facebook/opt-125m --use-v2-block-manager \
--num_speculative_tokens 5 --gpu_memory_utilization 0.8
```

然后使用一个客户端：

```bash
from openai import OpenAI


# Modify OpenAI's API key and API base to use vLLM's API server.
# 修改 OpenAI 的 API 密钥和 API 库以使用 vLLM 的 API 服务器。


openai_api_key = "EMPTY"
openai_api_base = "http://localhost:8000/v1"


client = OpenAI(
    # defaults to os.environ.get("OPENAI_API_KEY")
    # 默认为 os.environ.get("OPENAI_API_KEY")


    api_key=openai_api_key,
    base_url=openai_api_base,
)


models = client.models.list()
model = models.data[0].id


# Completion API


stream = False
completion = client.completions.create(
    model=model,
    prompt="The future of AI is",
    echo=False,
    n=1,
    stream=stream,
)


print("Completion results:")
if stream:
    for c in completion:
        print(c)
else:
    print(completion)
```
