# 文档路径: 08-indices-and-tables > 01-index > 索引 > A

## A


| [abort() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.abort)                                            | [abort_request() (vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.abort_request) |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------- |
| [add_request() (vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.add_request)                                                | [AsyncLLMEngine (class in vllm)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine)             |
| [audio (vllm.multimodal.MultiModalDataBuiltins attribute)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalDataBuiltins.audio) |                                                                                                                                   |
