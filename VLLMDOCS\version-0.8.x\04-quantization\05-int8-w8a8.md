---
title: INT8 W8A8
---

vLLM 支持将权重和激活量化为 INT8，这不仅有助于节省内存，还能加快推理速度。这种量化方法可以有效地在减小模型大小的同时保持良好的性能。

请访问 Hugging Face 上[由流行的大语言模型的量化 INT8 检查点组成的集合](https://huggingface.co/collections/neuralmagic/int8-llms-for-vllm-668ec32c049dca0369816415)，这些检查点可随时与 vLLM 一起使用。

**注意：**

INT8 计算在计算能力大于 7.5 的 NVIDIA GPU (Turing、Ampere、Ada Lovelace、Hopper) 上得到支持。

## 依赖

如需将 INT8 量化与 vLLM 结合使用，您需要安装 [llm-compressor](https://github.com/vllm-project/llm-compressor/) 库：

```plain
pip install llmcompressor==0.1.0
```

## 量化过程

量化过程涉及 4 个主要步骤：

1. 加载模型

2. 准备校准数据

3. 应用量化

4. 评估 vLLM 的准确性

### 1. 加载模型

使用包装了 `AutoModelForCausalLM` 的 `SparseAutoModelForCausalLM` 来实现量化模型的保存和加载：

```python
from llmcompressor.transformers import SparseAutoModelForCausalLM
from transformers import AutoTokenizer


MODEL_ID = "meta-llama/Meta-Llama-3-8B-Instruct"
model = SparseAutoModelForCausalLM.from_pretrained(
    MODEL_ID, device_map="auto", torch_dtype="auto",
)
tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
```

### 2. 准备校准数据

将激活值量化为 INT8 时，您需要样本数据来估计激活尺度。最好使用与您部署数据高度匹配的校准数据。对于通用的指令调整模型，您可以使用像 `ultrachat` 这样的数据集：

```python
from datasets import load_dataset


NUM_CALIBRATION_SAMPLES = 512
MAX_SEQUENCE_LENGTH = 2048


# Load and preprocess the dataset
# 加载并预处理数据集


ds = load_dataset("HuggingFaceH4/ultrachat_200k", split="train_sft")
ds = ds.shuffle(seed=42).select(range(NUM_CALIBRATION_SAMPLES))


def preprocess(example):
    return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
ds = ds.map(preprocess)


def tokenize(sample):
    return tokenizer(sample["text"], padding=False, max_length=MAX_SEQUENCE_LENGTH, truncation=True, add_special_tokens=False)
ds = ds.map(tokenize, remove_columns=ds.column_names)
```

### 3. 应用量化

现在，应用量化算法：

```python
from llmcompressor.transformers import oneshot
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier


# Configure the quantization algorithms
# 配置量化算法


recipe = [
    SmoothQuantModifier(smoothing_strength=0.8),
    GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
]


# Apply quantization
# 应用量化


oneshot(
    model=model,
    dataset=ds,
    recipe=recipe,
    max_seq_length=MAX_SEQUENCE_LENGTH,
    num_calibration_samples=NUM_CALIBRATION_SAMPLES,
)


# Save the compressed model
# 保存压缩后的模型


SAVE_DIR = MODEL_ID.split("/")[1] + "-W8A8-Dynamic-Per-Token"
model.save_pretrained(SAVE_DIR, save_compressed=True)
tokenizer.save_pretrained(SAVE_DIR)
```

这个过程会创建一个 W8A8 模型，其权重和激活被量化为 8 位整数。

### 4. 评估准确性

量化后，您可以在 vLLM 中加载并运行模型：

```python
from vllm import LLM
model = LLM("./Meta-Llama-3-8B-Instruct-W8A8-Dynamic-Per-Token")
```

如需评估准确性，您可以使用 `lm_eval`：

```plain
lm_eval --model vllm \
  --model_args pretrained="./Meta-Llama-3-8B-Instruct-W8A8-Dynamic-Per-Token",add_bos_token=true \
  --tasks gsm8k \
  --num_fewshot 5 \
  --limit 250 \
  --batch_size 'auto'
```

**注意：**
量化模型可能对 `bos` token 的存在很敏感。在运行评估时，请确保包含 `add_bos_token=True` 这个参数。

## 最佳实践

- 以 512 个样本作为校准数据开始（如果准确性下降则增加样本数量）
- 使用序列长度 2048 作为起点
- 采用模型所训练的聊天模板或指令模板。
- 如果您已经对模型进行了微调，请考虑使用一部分您的训练数据进行校准。

## 故障排除和支持

如果您遇到任何问题或有功能请求，请在 `vllm-project/llm-compressor` GitHub 存储库中提出 issue。
