# 文档路径: 05-automatic-prefix-caching > 02-implementation > 实现 (Implementation) > 通用缓存策略

# 通用缓存策略

将所有 KV 块存储在 1 个哈希表中，使得 vLLM 可以缓存来自早期请求的 KV 块，以节省内存并加速未来请求的计算。例如，如果 1 个新的请求与之前的请求共享系统提示词，那么共享提示的 KV 缓存可以直接用于新请求，而无需重新计算。然而，总的 KV 缓存空间是有限的，当缓存满时，我们必须决定保留哪些 KV 块，淘汰哪些块。

使用哈希表管理 KV 缓存使我们能够实现灵活的缓存策略。例如，在当前的 vLLM 中，我们实现了以下淘汰策略：

- 当没有剩余的空闲块时，我们将淘汰引用计数（即当前使用该块的请求数）为 0 的 KV 块。
- 如果有多个引用计数为 0 的块，我们将优先淘汰最近最少使用的块 (LRU)。
- 如果多个块的最后访问时间相同，我们将优先淘汰前缀最长的块（即，在它之前有最多块的块）。

请注意，当应用于具有全注意力的模型时，这种淘汰策略有效实现了与 [RadixAttention](https://lmsys.org/blog/2024-01-17-sglang/) 中完全相同的策略，即优先淘汰引用计数为 0 和最近最少使用的前缀树中的叶子节点。

然而，基于哈希的 KV 缓存管理使我们能够灵活应对更复杂的服务场景，并实现超越上述策略的更复杂的淘汰策略：

- 多 LoRA 服务。在为多个 LoRA 适配器提供请求服务时，我们可以让每个 KV 块的哈希也包括请求查询的 LoRA ID，以启用所有适配器的缓存。通过这种方式，我们可以共同管理不同适配器的 KV 块，这简化了系统实现，并提高了全局的缓存命中率和效率。
- 多模态模型。当用户输入不仅仅包括离散的 token 时，我们可以使用不同的哈希方法来处理不同模态输入的缓存。例如，使用感知哈希处理图像，以缓存相似的输入图像。
