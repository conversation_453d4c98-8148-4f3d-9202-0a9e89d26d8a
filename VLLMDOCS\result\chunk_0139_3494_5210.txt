# 文档路径: 07-developer-documentation > 08-profiling-vllm > 分析 vLLM > 命令和使用示例： > OpenAI 服务器：

## 命令和使用示例：


### OpenAI 服务器：

```bash
VLLM_TORCH_PROFILER_DIR=/mnt/traces/ python -m vllm.entrypoints.openai.api_server --model meta-llama/Meta-Llama-3-70B
```

benchmark_serving.py:

```bash
python benchmarks/benchmark_serving.py --backend vllm --model meta-llama/Meta-Llama-3-70B --dataset-name sharegpt --dataset-path sharegpt.json --profile --num-prompts 2
```
