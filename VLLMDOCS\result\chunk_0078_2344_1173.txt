# 文档路径: 03-models > 02-adding-a-new-model > 添加新模型 > 3.（可选）实现张量并行和量化支持

## 3.（可选）实现张量并行和量化支持

如果您的模型太大而无法在单个GPU上容纳，可以考虑采用张量并行技术来解决这一问题。具体来说，您需要将模型中的线性层和嵌入层替换为支持张量并行的对应版本。对于嵌入层，您只需将 `torch.nn.Embedding` 替换为 `VocabParallelEmbedding` 即可。对于输出的语言模型头部，您可以使用 `ParallelLMHead` 。对于线性层，我们提供以下选项来并行化它们：

- `ReplicatedLinear`：跨多个 GPU 复制输入和权重。此方法不节省内存。
- `RowParallelLinear`：将输入张量在隐藏维度上进行划分，而权重矩阵则沿行（输入维度）进行划分。在完成矩阵乘法之后，通过执行 all-reduce 操作来减少结果。通常用于第二个 FFN 层和注意力层的输出线性变换。
- `ColumnParallelLinear`：复制输入张量。权重矩阵则按照列（输出维度）进行分割，而计算结果也沿列维度进行分割。这种技术通常用于原始 Transformer 中的第一个 FFN 层和注意力层的分离 QKV 变换。
- `MergedColumnParallelLinear`：合并多个 _ColumnParallelLinear_ 运算符的列并行线性。通常用于具有加权激活函数（例如 SiLU）的第一个 FFN 层。该类处理了多个权重矩阵的分片权重加载逻辑。
- `QKVParallelLinear`: 用于多头和分组查询注意机制的查询、键和值投影的并行线性层。当键/值头的数量小于世界大小时，此类会正确复制键/值头。此类负责处理权重矩阵的权重加载和复制。

请注意，上面的所有线性层均采用 [linear_method] 作为输入。vLLM 会根据不同的量化方案设置该参数，以支持权重量化。

1. 实现权重加载逻辑

您现在需要在 `*ForCausalLM` 类中实现 `load_weights` 方法。此方法应该从 HuggingFace 的检查点文件中加载权重，并将它们分配给模型中的相应层。具体来说，对于 `MergedColumnParallelLinear` 和 `QKVParallelLinear` 层，如果原始模型具有分离的权重矩阵，则需要分别加载不同的部分。
