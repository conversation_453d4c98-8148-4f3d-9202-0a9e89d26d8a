# 文档路径: 07-developer-documentation > 04-vllm-paged-attention > vLLM  分页注意力 > Softmax > `qk_max` 和 `logits`

- 目前，vLLM 使用自己的多头查询注意力内核 (multi-head query attention kernel) 实现（位于`csrc/attention/attention_kernels.cu`）。该内核旨在兼容 vLLM 的分页键值缓存，其中键和值缓存存储在不同的块中（注意，这里的块概念与 GPU 线程块不同。因此，在后续文档中，我们将把 vLLM 分页注意力块称为「块 (block)」，而把 GPU 线程块称为「线程块(thread block)」）。
- 为了实现高性能，该内核依赖于专门设计的内存布局和访问方法，特别是在线程从全局内存读取数据到共享内存时。本文档的目的是逐步提供内核实现的高层次解释，以帮助那些希望了解 vLLM 多头查询注意力内核的人。在阅读完本文档后，用户能够更好地理解，并更容易跟进实际的实现。
- 请注意，本文件可能不会涵盖所有细节，例如如何计算相应数据的正确索引或点乘实现。不过，在阅读完本文档并熟悉高层次的逻辑流程后，您应该能够更容易阅读实际代码并理解细节。

## Softmax

### `qk_max` 和 `logits`


- 得到 `qk` 结果后，我们可以用 `qk` 设置临时 `logits` 结果 （最后，`logits` 应该存储归一化的 softmax 结果）。同时，我们还可以比较并收集当前线程组计算的所有 `qk` 的 `qk_max`。

```plain
    if (thread_group_offset == 0) {
       const bool mask = token_idx >= context_len;
       logits[token_idx - start_token_idx] = mask ? 0.f : qk;
       qk_max = mask ? qk_max : fmaxf(qk_max, qk);
    }
```

- 请注意，这里的 `logits` 存储在共享内存上，因此每个线程组将为其分配的上下文 token 设置字段。总的来说，logits 的大小应该是上下文 token 的数量。

```python
    for (int mask = WARP_SIZE / 2; mask >= THREAD_GROUP_SIZE; mask /= 2) {
        qk_max = fmaxf(qk_max, VLLM_SHFL_XOR_SYNC(qk_max, mask));
    }


    if (lane == 0) {
       red_smem[warp_idx] = qk_max;
    }
```

- 然后我们需要获得每个 warp 上减少的 `qk_max` 。主要思想是让 warp 中的线程相互通信并获得最终的 `qk` 最大值 。

```plain
    for (int mask = NUM_WARPS / 2; mask >= 1; mask /= 2) {
        qk_max = fmaxf(qk_max, VLLM_SHFL_XOR_SYNC(qk_max, mask));
    }
    qk_max = VLLM_SHFL_SYNC(qk_max, 0);
```

- 最后，我们可以通过比较该线程块中所有 warp 的 `qk_max` ，来得到整个线程块的归约 `qk_max` ，然后将最终结果广播到每个线程。
