# 文档路径: 10-tutorials > 04-vllm-langchain-tutorial > 将 LangChain 与 vLLM 结合使用：完整教程 > 3. 使用 LangChain 和 vLLM 创建链

## 3. 使用 LangChain 和 vLLM 创建链


LangChain 的核心功能之一是能够创建操作链，从而实现更复杂的交互。我们可以轻松地将 vLLM 模型集成到 LLMChain 中，从而提供更大的灵活性。

```
from langchain.chains import LLMChain
from langchain_core.prompts import PromptTemplate

# Defining a prompt template for our LLMChain
template = """Question: {question}

Answer: Let's think step by step."""
prompt = PromptTemplate.from_template(template)

# Creating an LLMChain with vLLM
llm_chain = LLMChain(prompt=prompt, llm=llm)

# Testing the LLMChain
question = "Who was the US president in the year the first Pokemon game was released?"
print(llm_chain.invoke(question))
```
