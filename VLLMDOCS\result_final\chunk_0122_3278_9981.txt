# 文档路径: 08-indices-and-tables > 01-index > 索引 > M

## M


| [map_input() (vllm.multimodal.MultiModalPlugin method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin.map_input)                  | [(vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.map_input) |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [model_config (vllm.inputs.registry.InputContext attribute)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputContext.model_config) | module                                                                                                                                                          |
| [vllm.engine](https://docs.vllm.ai/en/latest/dev/engine/engine_index.html#module-vllm.engine)                                                                                            | [vllm.inputs.registry](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#module-vllm.inputs.registry)                                 |
| [vllm.multimodal](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#module-vllm.multimodal)                                                                            | [vllm.multimodal.image](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#module-vllm.multimodal.image)                                       |
| [multi_modal_data (vllm.inputs.LLMInputs attribute)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.LLMInputs.multi_modal_data)                 | [(vllm.inputs.TextPrompt attribute)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TextPrompt.multi_modal_data)              |
| [(vllm.inputs.TokensPrompt attribute)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TokensPrompt.multi_modal_data)                                   | [MULTIMODAL_REGISTRY (in module vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MULTIMODAL_REGISTRY)      |
| [MultiModalDataBuiltins (class in vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalDataBuiltins)                          | [MultiModalDataDict (in module vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalDataDict)        |
| [MultiModalInputs (class in vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalInputs)                                      | [MultiModalPlugin (class in vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin)             |
| [MultiModalRegistry (class in vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry)                                  |                                                                                                                                                                 |
