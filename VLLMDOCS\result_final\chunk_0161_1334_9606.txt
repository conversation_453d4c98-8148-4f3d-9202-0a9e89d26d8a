# 文档路径: 10-tutorials > 04-vllm-langchain-tutorial > 将 LangChain 与 vLLM 结合使用：完整教程 > 5. 利用量化提高效率

## 5. 利用量化提高效率


量化是一种通过减少内存使用和加快计算来提高语言模型性能的有效技术。

vLLM 支持 AWQ 量化格式。要启用它，请通过参数传递量化选项 vllm_kwargs。量化允许在资源受限的环境（例如边缘设备或较旧的 GPU）中部署 LLM，而不会牺牲太多准确性。

```
del llm

clean_memory(deep=True)
```

```
llm_q = VLLM(
    model="/input0/Qwen2.5-3B-Instruct-AWQ",
    trust_remote_code=True,
    max_new_tokens=512,
    vllm_kwargs={"quantization": "awq"},
)
# Running a simple query
print(llm_q.invoke("What are the most popular Halloween Costumes?"))
```

在此示例中，Qwen2.5-3B-Instruct-AWQ模型已量化以实现最佳性能。在将应用程序部署到生产环境（成本和资源效率至关重要）时，此功能尤其有用。
