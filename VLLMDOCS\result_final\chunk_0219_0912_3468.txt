# 文档路径: 07-developer-documentation > 02-offline-inference > 02-llm-inputs > LLM Inputs > class vllm.inputs.TokensPrompt

# class vllm.inputs.TokensPrompt


[[source]](https://docs.vllm.ai/en/latest/_modules/vllm/inputs/data.html#TokensPrompt)

基类：[TypedDict](https://typing-extensions.readthedocs.io/en/latest/index.html#typing_extensions.TypedDict)

标记化提示的架构。

> prompt_token_ids: [List](https://docs.python.org/3/library/typing.html#typing.List)[[int](https://docs.python.org/3/library/functions.html#int)]
> 一个要传递给模型的 token ID 列表。

> multi_modal_data: typing_extensions.NotRequired[MultiModalDataDict]
> 如果模型支持，可传递给模型的可选多模态数据。

> mm_processor_kwargs: typing_extensions.NotRequired[[Dict](https://docs.python.org/3/library/typing.html#typing.Dict)[[str](https://docs.python.org/3/library/stdtypes.html#str), [Any](https://docs.python.org/3/library/typing.html#typing.Any)]]
> 将被转发至多模态输入映射器和处理器的可选多模态处理器关键参数。需要注意的是，如果有多种模态已经为正在考量的模型注册了映射器等相关内容，我们会尝试将这些多模态处理器关键参数传递给它们中的每一个。
