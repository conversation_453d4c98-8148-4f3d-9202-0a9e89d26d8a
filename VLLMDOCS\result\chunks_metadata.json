[{"chunk_id": "4719_7975", "file_name": "chunk_0001_4719_7975.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 入门", "main_title": "入门", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "4719_7975", "file_name": "chunk_0002_4719_7975.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 部署", "main_title": "部署", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "4719_7975", "file_name": "chunk_0003_4719_7975.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 模型", "main_title": "模型", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "4719_7975", "file_name": "chunk_0004_4719_7975.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 量化", "main_title": "量化", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "4719_7975", "file_name": "chunk_0005_4719_7975.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 自动前缀缓存", "main_title": "自动前缀缓存", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "4719_7975", "file_name": "chunk_0006_4719_7975.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 性能基准测试", "main_title": "性能基准测试", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "4719_7975", "file_name": "chunk_0007_4719_7975.txt", "metadata": {"source_file": "version-0.8.x\\index.md", "directory_path": "", "filename": "index", "title_hierarchy": "欢迎来到 vLLM！ > 欢迎来到 vLLM！ > 文档 > 开发者文档", "main_title": "开发者文档", "section_level": 3, "document_title": "欢迎来到 vLLM！"}}, {"chunk_id": "7443_9456", "file_name": "chunk_0008_7443_9456.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\01-installation.md", "directory_path": "01-getting-started", "filename": "01-installation", "title_hierarchy": "安装", "main_title": "前置内容", "section_level": 0, "document_title": "安装"}}, {"chunk_id": "7443_6078", "file_name": "chunk_0009_7443_6078.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\01-installation.md", "directory_path": "01-getting-started", "filename": "01-installation", "title_hierarchy": "安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "安装"}}, {"chunk_id": "7443_7369", "file_name": "chunk_0010_7443_7369.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\01-installation.md", "directory_path": "01-getting-started", "filename": "01-installation", "title_hierarchy": "安装 > 使用 pip 安装", "main_title": "使用 pip 安装", "section_level": 2, "document_title": "安装"}}, {"chunk_id": "7443_1433", "file_name": "chunk_0011_7443_1433.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\01-installation.md", "directory_path": "01-getting-started", "filename": "01-installation", "title_hierarchy": "安装 > 从源代码构建", "main_title": "从源代码构建", "section_level": 2, "document_title": "安装"}}, {"chunk_id": "9913_9456", "file_name": "chunk_0012_9913_9456.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\02-installation-with-rocm.md", "directory_path": "01-getting-started", "filename": "02-installation-with-rocm", "title_hierarchy": "使用 ROCm 安装", "main_title": "前置内容", "section_level": 0, "document_title": "使用 ROCm 安装"}}, {"chunk_id": "9913_6078", "file_name": "chunk_0013_9913_6078.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\02-installation-with-rocm.md", "directory_path": "01-getting-started", "filename": "02-installation-with-rocm", "title_hierarchy": "使用 ROCm 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 ROCm 安装"}}, {"chunk_id": "9913_2702", "file_name": "chunk_0014_9913_2702.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\02-installation-with-rocm.md", "directory_path": "01-getting-started", "filename": "02-installation-with-rocm", "title_hierarchy": "使用 ROCm 安装 > 选项 1：使用 docker 从源代码构建 （推荐）", "main_title": "选项 1：使用 docker 从源代码构建 （推荐）", "section_level": 2, "document_title": "使用 ROCm 安装"}}, {"chunk_id": "9913_3582", "file_name": "chunk_0015_9913_3582.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\02-installation-with-rocm.md", "directory_path": "01-getting-started", "filename": "02-installation-with-rocm", "title_hierarchy": "使用 ROCm 安装 > 选项 2：从源代码构建", "main_title": "选项 2：从源代码构建", "section_level": 2, "document_title": "使用 ROCm 安装"}}, {"chunk_id": "0141_9456", "file_name": "chunk_0016_0141_9456.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装", "main_title": "前置内容", "section_level": 0, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "0141_6078", "file_name": "chunk_0017_0141_6078.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "0141_9896", "file_name": "chunk_0018_0141_9896.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 使用 Dockerfile 快速开始", "main_title": "使用 Dockerfile 快速开始", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "0141_4620", "file_name": "chunk_0019_0141_4620.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 从源代码安装", "main_title": "从源代码安装", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "0141_0935", "file_name": "chunk_0020_0141_0935.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 性能提示", "main_title": "性能提示", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "0141_9863", "file_name": "chunk_0021_0141_9863.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\03-installation-with-openvino.md", "directory_path": "01-getting-started", "filename": "03-installation-with-open<PERSON>o", "title_hierarchy": "使用 OpenVINO 安装 > 局限性", "main_title": "局限性", "section_level": 2, "document_title": "使用 OpenVINO 安装"}}, {"chunk_id": "3831_9456", "file_name": "chunk_0022_3831_9456.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装", "main_title": "前置内容", "section_level": 0, "document_title": "使用 CPU 安装"}}, {"chunk_id": "3831_6078", "file_name": "chunk_0023_3831_6078.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "3831_9896", "file_name": "chunk_0024_3831_9896.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 使用 Dockerfile 快速开始", "main_title": "使用 Dockerfile 快速开始", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "3831_1433", "file_name": "chunk_0025_3831_1433.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 从源代码构建", "main_title": "从源代码构建", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "3831_5044", "file_name": "chunk_0026_3831_5044.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 相关运行时环境变量", "main_title": "相关运行时环境变量", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "3831_4897", "file_name": "chunk_0027_3831_4897.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > PyTorch 的英特尔扩展", "main_title": "PyTorch 的英特尔扩展", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "3831_0935", "file_name": "chunk_0028_3831_0935.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\04-installation-with-cpu.md", "directory_path": "01-getting-started", "filename": "04-installation-with-cpu", "title_hierarchy": "使用 CPU 安装 > 性能提示", "main_title": "性能提示", "section_level": 2, "document_title": "使用 CPU 安装"}}, {"chunk_id": "9739_9456", "file_name": "chunk_0029_9739_9456.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\05-installation-with-neuron.md", "directory_path": "01-getting-started", "filename": "05-installation-with-neuron", "title_hierarchy": "使用 <PERSON><PERSON>on 安装", "main_title": "前置内容", "section_level": 0, "document_title": "使用 <PERSON><PERSON>on 安装"}}, {"chunk_id": "9739_1433", "file_name": "chunk_0030_9739_1433.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\05-installation-with-neuron.md", "directory_path": "01-getting-started", "filename": "05-installation-with-neuron", "title_hierarchy": "使用 Neuron 安装 > 从源代码构建 > 步骤 0. 启动 Trn1/Inf2 实例", "main_title": "步骤 0. 启动 Trn1/Inf2 实例", "section_level": 3, "document_title": "使用 <PERSON><PERSON>on 安装"}}, {"chunk_id": "9739_1433", "file_name": "chunk_0031_9739_1433.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\05-installation-with-neuron.md", "directory_path": "01-getting-started", "filename": "05-installation-with-neuron", "title_hierarchy": "使用 Neuron 安装 > 从源代码构建 > 步骤 1. 安装驱动程序和工具", "main_title": "步骤 1. 安装驱动程序和工具", "section_level": 3, "document_title": "使用 <PERSON><PERSON>on 安装"}}, {"chunk_id": "9739_1433", "file_name": "chunk_0032_9739_1433.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\05-installation-with-neuron.md", "directory_path": "01-getting-started", "filename": "05-installation-with-neuron", "title_hierarchy": "使用 Neuron 安装 > 从源代码构建 > 步骤 2. 安装 Transformers-neuronx 及其依赖", "main_title": "步骤 2. 安装 Transformers-neuronx 及其依赖", "section_level": 3, "document_title": "使用 <PERSON><PERSON>on 安装"}}, {"chunk_id": "9739_1433", "file_name": "chunk_0033_9739_1433.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\05-installation-with-neuron.md", "directory_path": "01-getting-started", "filename": "05-installation-with-neuron", "title_hierarchy": "使用 Neuron 安装 > 从源代码构建 > 步骤 3. 从源代码安装 vLLM", "main_title": "步骤 3. 从源代码安装 vLLM", "section_level": 3, "document_title": "使用 <PERSON><PERSON>on 安装"}}, {"chunk_id": "5933_9456", "file_name": "chunk_0034_5933_9456.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\06-installation-with-tpu.md", "directory_path": "01-getting-started", "filename": "06-installation-with-tpu", "title_hierarchy": "使用 TPU 安装", "main_title": "前置内容", "section_level": 0, "document_title": "使用 TPU 安装"}}, {"chunk_id": "5933_6078", "file_name": "chunk_0035_5933_6078.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\06-installation-with-tpu.md", "directory_path": "01-getting-started", "filename": "06-installation-with-tpu", "title_hierarchy": "使用 TPU 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 TPU 安装"}}, {"chunk_id": "5933_4400", "file_name": "chunk_0036_5933_4400.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\06-installation-with-tpu.md", "directory_path": "01-getting-started", "filename": "06-installation-with-tpu", "title_hierarchy": "使用 TPU 安装 > 使用`Dockerfile.tpu` 构建 Docker 镜像", "main_title": "使用`Dockerfile.tpu` 构建 Docker 镜像", "section_level": 2, "document_title": "使用 TPU 安装"}}, {"chunk_id": "5933_1433", "file_name": "chunk_0037_5933_1433.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\06-installation-with-tpu.md", "directory_path": "01-getting-started", "filename": "06-installation-with-tpu", "title_hierarchy": "使用 TPU 安装 > 从源代码构建", "main_title": "从源代码构建", "section_level": 2, "document_title": "使用 TPU 安装"}}, {"chunk_id": "5398_9456", "file_name": "chunk_0038_5398_9456.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\07-installation-with-xpu.md", "directory_path": "01-getting-started", "filename": "07-installation-with-xpu", "title_hierarchy": "使用 XPU 安装", "main_title": "前置内容", "section_level": 0, "document_title": "使用 XPU 安装"}}, {"chunk_id": "5398_6078", "file_name": "chunk_0039_5398_6078.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\07-installation-with-xpu.md", "directory_path": "01-getting-started", "filename": "07-installation-with-xpu", "title_hierarchy": "使用 XPU 安装 > 依赖环境", "main_title": "依赖环境", "section_level": 2, "document_title": "使用 XPU 安装"}}, {"chunk_id": "5398_9896", "file_name": "chunk_0040_5398_9896.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\07-installation-with-xpu.md", "directory_path": "01-getting-started", "filename": "07-installation-with-xpu", "title_hierarchy": "使用 XPU 安装 > 使用 Dockerfile 快速开始", "main_title": "使用 Dockerfile 快速开始", "section_level": 2, "document_title": "使用 XPU 安装"}}, {"chunk_id": "5398_1433", "file_name": "chunk_0041_5398_1433.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\07-installation-with-xpu.md", "directory_path": "01-getting-started", "filename": "07-installation-with-xpu", "title_hierarchy": "使用 XPU 安装 > 从源代码构建", "main_title": "从源代码构建", "section_level": 2, "document_title": "使用 XPU 安装"}}, {"chunk_id": "8808_9456", "file_name": "chunk_0042_8808_9456.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\08-quickstart.md", "directory_path": "01-getting-started", "filename": "08-quickstart", "title_hierarchy": "快速入门", "main_title": "前置内容", "section_level": 0, "document_title": "快速入门"}}, {"chunk_id": "8808_1939", "file_name": "chunk_0043_8808_1939.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\08-quickstart.md", "directory_path": "01-getting-started", "filename": "08-quickstart", "title_hierarchy": "快速入门 > 兼容 OpenAI 服务器 > 在 vLLM 中使用 OpenAI Completions API", "main_title": "在 vLLM 中使用 OpenAI Completions API", "section_level": 3, "document_title": "快速入门"}}, {"chunk_id": "8808_1939", "file_name": "chunk_0044_8808_1939.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\08-quickstart.md", "directory_path": "01-getting-started", "filename": "08-quickstart", "title_hierarchy": "快速入门 > 兼容 OpenAI 服务器 > 在 vLLM 中使用 OpenAI Chat API", "main_title": "在 vLLM 中使用 OpenAI Chat API", "section_level": 3, "document_title": "快速入门"}}, {"chunk_id": "8094_8253", "file_name": "chunk_0045_8094_8253.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\09-debugging-tips.md", "directory_path": "01-getting-started", "filename": "09-debugging-tips", "title_hierarchy": "调试技巧 > 调试挂起与崩溃问题", "main_title": "调试挂起与崩溃问题", "section_level": 2, "document_title": "调试技巧"}}, {"chunk_id": "1629_9456", "file_name": "chunk_0046_1629_9456.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器", "main_title": "前置内容", "section_level": 0, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "1629_5656", "file_name": "chunk_0047_1629_5656.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > 附加参数 > Chat API 的附加参数", "main_title": "Chat API 的附加参数", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "1629_5656", "file_name": "chunk_0048_1629_5656.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > 附加参数 > Completions API 的附加参数", "main_title": "Completions API 的附加参数", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "1629_8993", "file_name": "chunk_0049_1629_8993.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > 服务器的命令行参数 > 命名参数", "main_title": "命名参数", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "1629_0322", "file_name": "chunk_0050_1629_0322.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > chat completion API 中的工具调用 > 命名函数调用", "main_title": "命名函数调用", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "1629_0322", "file_name": "chunk_0051_1629_0322.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > chat completion API 中的工具调用 > 配置文件", "main_title": "配置文件", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "1629_0322", "file_name": "chunk_0052_1629_0322.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\01-openai-compatible-server.md", "directory_path": "02-serving", "filename": "01-openai-compatible-server", "title_hierarchy": "OpenAI 兼容服务器 > chat completion API 中的工具调用 > 自动函数调用", "main_title": "自动函数调用", "section_level": 3, "document_title": "OpenAI 兼容服务器"}}, {"chunk_id": "2455_9563", "file_name": "chunk_0053_2455_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\02-deploying-with-docker.md", "directory_path": "02-serving", "filename": "02-deploying-with-docker", "title_hierarchy": "使用 Docker 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 Docker 进行部署"}}, {"chunk_id": "0883_0437", "file_name": "chunk_0054_0883_0437.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\03-distributed-inference-and-serving.md", "directory_path": "02-serving", "filename": "03-distributed-inference-and-serving", "title_hierarchy": "分布式推理和服务 > 如何决定分布式推理策略？", "main_title": "如何决定分布式推理策略？", "section_level": 2, "document_title": "分布式推理和服务"}}, {"chunk_id": "0883_3182", "file_name": "chunk_0055_0883_3182.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\03-distributed-inference-and-serving.md", "directory_path": "02-serving", "filename": "03-distributed-inference-and-serving", "title_hierarchy": "分布式推理和服务 > 分布式推理和服务的详细信息", "main_title": "分布式推理和服务的详细信息", "section_level": 2, "document_title": "分布式推理和服务"}}, {"chunk_id": "0883_3889", "file_name": "chunk_0056_0883_3889.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\03-distributed-inference-and-serving.md", "directory_path": "02-serving", "filename": "03-distributed-inference-and-serving", "title_hierarchy": "分布式推理和服务 > 多节点推理和服务", "main_title": "多节点推理和服务", "section_level": 2, "document_title": "分布式推理和服务"}}, {"chunk_id": "7082_9563", "file_name": "chunk_0057_7082_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\04-production-metrics.md", "directory_path": "02-serving", "filename": "04-production-metrics", "title_hierarchy": "生产指标 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "生产指标"}}, {"chunk_id": "8593_9563", "file_name": "chunk_0058_8593_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\05-environment-variables.md", "directory_path": "02-serving", "filename": "05-environment-variables", "title_hierarchy": "环境变量 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "环境变量"}}, {"chunk_id": "1970_9456", "file_name": "chunk_0059_1970_9456.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\06-usage-stats-collection.md", "directory_path": "02-serving", "filename": "06-usage-stats-collection", "title_hierarchy": "使用统计数据收集", "main_title": "前置内容", "section_level": 0, "document_title": "使用统计数据收集"}}, {"chunk_id": "1970_9462", "file_name": "chunk_0060_1970_9462.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\06-usage-stats-collection.md", "directory_path": "02-serving", "filename": "06-usage-stats-collection", "title_hierarchy": "使用统计数据收集 > 收集了哪些数据？", "main_title": "收集了哪些数据？", "section_level": 2, "document_title": "使用统计数据收集"}}, {"chunk_id": "1970_3679", "file_name": "chunk_0061_1970_3679.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\06-usage-stats-collection.md", "directory_path": "02-serving", "filename": "06-usage-stats-collection", "title_hierarchy": "使用统计数据收集 > 退出使用统计数据收集", "main_title": "退出使用统计数据收集", "section_level": 2, "document_title": "使用统计数据收集"}}, {"chunk_id": "4865_9563", "file_name": "chunk_0062_4865_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\08-tensorizer.md", "directory_path": "02-serving", "filename": "08-tensorizer", "title_hierarchy": "使用 CoreWeave 的张量器加载模型 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 CoreWeave 的张量器加载模型"}}, {"chunk_id": "8926_9456", "file_name": "chunk_0063_8926_9456.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\09-compatibility matrix.md", "directory_path": "02-serving", "filename": "09-compatibility matrix", "title_hierarchy": "兼容性矩阵", "main_title": "前置内容", "section_level": 0, "document_title": "兼容性矩阵"}}, {"chunk_id": "8926_2960", "file_name": "chunk_0064_8926_2960.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\09-compatibility matrix.md", "directory_path": "02-serving", "filename": "09-compatibility matrix", "title_hierarchy": "兼容性矩阵 > Feature x Feature", "main_title": "Feature x Feature", "section_level": 2, "document_title": "兼容性矩阵"}}, {"chunk_id": "8926_4195", "file_name": "chunk_0065_8926_4195.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\09-compatibility matrix.md", "directory_path": "02-serving", "filename": "09-compatibility matrix", "title_hierarchy": "兼容性矩阵 > Feature x Hardware", "main_title": "Feature x Hardware", "section_level": 2, "document_title": "兼容性矩阵"}}, {"chunk_id": "1213_9456", "file_name": "chunk_0066_1213_9456.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\10-frequently-asked-questions.md", "directory_path": "02-serving", "filename": "10-frequently-asked-questions", "title_hierarchy": "常见问题", "main_title": "前置内容", "section_level": 0, "document_title": "常见问题"}}, {"chunk_id": "1213_3037", "file_name": "chunk_0067_1213_3037.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\10-frequently-asked-questions.md", "directory_path": "02-serving", "filename": "10-frequently-asked-questions", "title_hierarchy": "常见问题 > 缓解策略", "main_title": "缓解策略", "section_level": 2, "document_title": "常见问题"}}, {"chunk_id": "0366_9456", "file_name": "chunk_0068_0366_9456.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型", "main_title": "前置内容", "section_level": 0, "document_title": "支持的模型"}}, {"chunk_id": "0366_1765", "file_name": "chunk_0069_0366_1765.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 仅文本语言模型 > 文本生成", "main_title": "文本生成", "section_level": 3, "document_title": "支持的模型"}}, {"chunk_id": "0366_1765", "file_name": "chunk_0070_0366_1765.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 仅文本语言模型 > 文本 Embedding", "main_title": "文本 Embedding", "section_level": 3, "document_title": "支持的模型"}}, {"chunk_id": "0366_1765", "file_name": "chunk_0071_0366_1765.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 仅文本语言模型 > 获奖模型", "main_title": "获奖模型", "section_level": 3, "document_title": "支持的模型"}}, {"chunk_id": "0366_5731", "file_name": "chunk_0072_0366_5731.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 多模态语言模型 > 文本生成", "main_title": "文本生成", "section_level": 3, "document_title": "支持的模型"}}, {"chunk_id": "0366_5731", "file_name": "chunk_0073_0366_5731.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\01-supported-models.md", "directory_path": "03-models", "filename": "01-supported-models", "title_hierarchy": "支持的模型 > 多模态语言模型 > 多模态 Embedding", "main_title": "多模态 Embedding", "section_level": 3, "document_title": "支持的模型"}}, {"chunk_id": "2344_9456", "file_name": "chunk_0074_2344_9456.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型", "main_title": "前置内容", "section_level": 0, "document_title": "添加新模型"}}, {"chunk_id": "2344_3549", "file_name": "chunk_0075_2344_3549.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 0. Fork vLLM 存储库", "main_title": "0. Fork vLLM 存储库", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "2344_1868", "file_name": "chunk_0076_2344_1868.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 1. 引入你的模型代码", "main_title": "1. 引入你的模型代码", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "2344_0522", "file_name": "chunk_0077_2344_0522.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 2. 重写 `forward` 的方法", "main_title": "2. 重写 `forward` 的方法", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "2344_1173", "file_name": "chunk_0078_2344_1173.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 3.（可选）实现张量并行和量化支持", "main_title": "3.（可选）实现张量并行和量化支持", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "2344_0765", "file_name": "chunk_0079_2344_0765.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 5. 注册模型", "main_title": "5. 注册模型", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "2344_8194", "file_name": "chunk_0080_2344_8194.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\02-adding-a-new-model.md", "directory_path": "03-models", "filename": "02-adding-a-new-model", "title_hierarchy": "添加新模型 > 6. 树外模型集成", "main_title": "6. 树外模型集成", "section_level": 2, "document_title": "添加新模型"}}, {"chunk_id": "1585_9456", "file_name": "chunk_0081_1585_9456.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入", "main_title": "前置内容", "section_level": 0, "document_title": "启用多模态输入"}}, {"chunk_id": "1585_1779", "file_name": "chunk_0082_1585_1779.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 1. 更新基础 vLLM 模型", "main_title": "1. 更新基础 vLLM 模型", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "1585_9442", "file_name": "chunk_0083_1585_9442.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 2. 注册输入映射器", "main_title": "2. 注册输入映射器", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "1585_8403", "file_name": "chunk_0084_1585_8403.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 3. 注册多模态 token 最大数量", "main_title": "3. 注册多模态 token 最大数量", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "1585_6512", "file_name": "chunk_0085_1585_6512.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 4.（可选）注册虚拟数据", "main_title": "4.（可选）注册虚拟数据", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "1585_2210", "file_name": "chunk_0086_1585_2210.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\03-enabling-multimodal-inputs.md", "directory_path": "03-models", "filename": "03-enabling-multimodal-inputs", "title_hierarchy": "启用多模态输入 > 5.（可选）注册输入处理器", "main_title": "5.（可选）注册输入处理器", "section_level": 2, "document_title": "启用多模态输入"}}, {"chunk_id": "6343_9456", "file_name": "chunk_0087_6343_9456.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\04-engine-arguments.md", "directory_path": "03-models", "filename": "04-engine-arguments", "title_hierarchy": "引擎参数", "main_title": "前置内容", "section_level": 0, "document_title": "引擎参数"}}, {"chunk_id": "6343_5910", "file_name": "chunk_0088_6343_5910.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\04-engine-arguments.md", "directory_path": "03-models", "filename": "04-engine-arguments", "title_hierarchy": "引擎参数 > 命名参数", "main_title": "命名参数", "section_level": 3, "document_title": "引擎参数"}}, {"chunk_id": "6343_9853", "file_name": "chunk_0089_6343_9853.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\04-engine-arguments.md", "directory_path": "03-models", "filename": "04-engine-arguments", "title_hierarchy": "引擎参数 > 异步引擎参数 > 命名参数", "main_title": "命名参数", "section_level": 3, "document_title": "引擎参数"}}, {"chunk_id": "9420_9456", "file_name": "chunk_0090_9420_9456.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器", "main_title": "前置内容", "section_level": 0, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "9420_5527", "file_name": "chunk_0091_9420_5527.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器 > LoRA 适配器服务", "main_title": "LoRA 适配器服务", "section_level": 2, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "9420_6772", "file_name": "chunk_0092_9420_6772.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器 > 动态提供 LoRA 适配器", "main_title": "动态提供 LoRA 适配器", "section_level": 2, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "9420_6287", "file_name": "chunk_0093_9420_6287.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器 > –lora-modules 的新格式", "main_title": "–lora-modules 的新格式", "section_level": 2, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "9420_3347", "file_name": "chunk_0094_9420_3347.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\05-using-lora-adapters.md", "directory_path": "03-models", "filename": "05-using-lora-adapters", "title_hierarchy": "使用 LoRA 适配器 > 模型卡中的 LoRA 模型谱系", "main_title": "模型卡中的 LoRA 模型谱系", "section_level": 2, "document_title": "使用 LoRA 适配器"}}, {"chunk_id": "5342_9456", "file_name": "chunk_0095_5342_9456.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\06-using-vlms.md", "directory_path": "03-models", "filename": "06-using-vlms", "title_hierarchy": "使用 VLM", "main_title": "前置内容", "section_level": 0, "document_title": "使用 VLM"}}, {"chunk_id": "5342_0182", "file_name": "chunk_0096_5342_0182.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\06-using-vlms.md", "directory_path": "03-models", "filename": "06-using-vlms", "title_hierarchy": "使用 VLM > 离线推理 > 单图像输入", "main_title": "单图像输入", "section_level": 3, "document_title": "使用 VLM"}}, {"chunk_id": "5342_0182", "file_name": "chunk_0097_5342_0182.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\06-using-vlms.md", "directory_path": "03-models", "filename": "06-using-vlms", "title_hierarchy": "使用 VLM > 离线推理 > 多图像输入", "main_title": "多图像输入", "section_level": 3, "document_title": "使用 VLM"}}, {"chunk_id": "9308_9456", "file_name": "chunk_0098_9308_9456.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码", "main_title": "前置内容", "section_level": 0, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "9308_0510", "file_name": "chunk_0099_9308_0510.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码 > 用草稿模型进行推测", "main_title": "用草稿模型进行推测", "section_level": 2, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "9308_2495", "file_name": "chunk_0100_9308_2495.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码 > 通过在提示符中匹配 n-grams 进行推测", "main_title": "通过在提示符中匹配 n-grams 进行推测", "section_level": 2, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "9308_3912", "file_name": "chunk_0101_9308_3912.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码 > 使用 MLP 推测器进行推测", "main_title": "使用 MLP 推测器进行推测", "section_level": 2, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "9308_7370", "file_name": "chunk_0102_9308_7370.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\07-speculative-decoding-in-vllm.md", "directory_path": "03-models", "filename": "07-speculative-decoding-in-vllm", "title_hierarchy": "vLLM 中的推测解码 > 相关 vLLM 贡献者的资源", "main_title": "相关 vLLM 贡献者的资源", "section_level": 2, "document_title": "vLLM 中的推测解码"}}, {"chunk_id": "3174_9643", "file_name": "chunk_0103_3174_9643.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\08-performance-and-tuning.md", "directory_path": "03-models", "filename": "08-performance-and-tuning", "title_hierarchy": "性能与调优 > 抢占", "main_title": "抢占", "section_level": 2, "document_title": "性能与调优"}}, {"chunk_id": "3174_5653", "file_name": "chunk_0104_3174_5653.txt", "metadata": {"source_file": "version-0.8.x\\03-models\\08-performance-and-tuning.md", "directory_path": "03-models", "filename": "08-performance-and-tuning", "title_hierarchy": "性能与调优 > 分块预填充", "main_title": "分块预填充", "section_level": 2, "document_title": "性能与调优"}}, {"chunk_id": "5263_9456", "file_name": "chunk_0105_5263_9456.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\01-supported_hardware.md", "directory_path": "04-quantization", "filename": "01-supported_hardware", "title_hierarchy": "量化内核支持的硬件", "main_title": "前置内容", "section_level": 0, "document_title": "量化内核支持的硬件"}}, {"chunk_id": "5263_2896", "file_name": "chunk_0106_5263_2896.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\01-supported_hardware.md", "directory_path": "04-quantization", "filename": "01-supported_hardware", "title_hierarchy": "量化内核支持的硬件 > 注意:", "main_title": "注意:", "section_level": 2, "document_title": "量化内核支持的硬件"}}, {"chunk_id": "9117_9563", "file_name": "chunk_0107_9117_9563.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\02-autoawq.md", "directory_path": "04-quantization", "filename": "02-<PERSON><PERSON><PERSON>", "title_hierarchy": "AutoAWQ > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "AutoAWQ"}}, {"chunk_id": "3178_9456", "file_name": "chunk_0108_3178_9456.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\03-bitsandbytes.md", "directory_path": "04-quantization", "filename": "03-bitsandbytes", "title_hierarchy": "BitsAndBytes", "main_title": "前置内容", "section_level": 0, "document_title": "BitsAndBytes"}}, {"chunk_id": "3178_5359", "file_name": "chunk_0109_3178_5359.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\03-bitsandbytes.md", "directory_path": "04-quantization", "filename": "03-bitsandbytes", "title_hierarchy": "BitsAndBytes > 读取量化 checkpoint", "main_title": "读取量化 checkpoint", "section_level": 2, "document_title": "BitsAndBytes"}}, {"chunk_id": "3178_9052", "file_name": "chunk_0110_3178_9052.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\03-bitsandbytes.md", "directory_path": "04-quantization", "filename": "03-bitsandbytes", "title_hierarchy": "BitsAndBytes > 过程中量化：加载为 4 位量化", "main_title": "过程中量化：加载为 4 位量化", "section_level": 2, "document_title": "BitsAndBytes"}}, {"chunk_id": "0243_9563", "file_name": "chunk_0111_0243_9563.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\04-gguf.md", "directory_path": "04-quantization", "filename": "04-gguf", "title_hierarchy": "GGUF > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "GGUF"}}, {"chunk_id": "2971_9456", "file_name": "chunk_0112_2971_9456.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8", "main_title": "前置内容", "section_level": 0, "document_title": "INT8 W8A8"}}, {"chunk_id": "2971_0079", "file_name": "chunk_0113_2971_0079.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8 > 量化过程 > 1. 加载模型", "main_title": "1. 加载模型", "section_level": 3, "document_title": "INT8 W8A8"}}, {"chunk_id": "2971_0079", "file_name": "chunk_0114_2971_0079.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8 > 量化过程 > 2. 准备校准数据", "main_title": "2. 准备校准数据", "section_level": 3, "document_title": "INT8 W8A8"}}, {"chunk_id": "2971_0079", "file_name": "chunk_0115_2971_0079.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8 > 量化过程 > 3. 应用量化", "main_title": "3. 应用量化", "section_level": 3, "document_title": "INT8 W8A8"}}, {"chunk_id": "2971_0079", "file_name": "chunk_0116_2971_0079.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\05-int8-w8a8.md", "directory_path": "04-quantization", "filename": "05-int8-w8a8", "title_hierarchy": "INT8 W8A8 > 量化过程 > 4. 评估准确性", "main_title": "4. 评估准确性", "section_level": 3, "document_title": "INT8 W8A8"}}, {"chunk_id": "2703_9456", "file_name": "chunk_0117_2703_9456.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8", "main_title": "前置内容", "section_level": 0, "document_title": "FP8 W8A8"}}, {"chunk_id": "2703_0079", "file_name": "chunk_0118_2703_0079.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8 > 量化过程 > 1. 加载模型", "main_title": "1. 加载模型", "section_level": 3, "document_title": "FP8 W8A8"}}, {"chunk_id": "2703_0079", "file_name": "chunk_0119_2703_0079.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8 > 量化过程 > 2. 应用量化", "main_title": "2. 应用量化", "section_level": 3, "document_title": "FP8 W8A8"}}, {"chunk_id": "2703_0079", "file_name": "chunk_0120_2703_0079.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\06-fp8-w8a8.md", "directory_path": "04-quantization", "filename": "06-fp8-w8a8", "title_hierarchy": "FP8 W8A8 > 量化过程 > 3. 评估准确性", "main_title": "3. 评估准确性", "section_level": 3, "document_title": "FP8 W8A8"}}, {"chunk_id": "1149_9563", "file_name": "chunk_0121_1149_9563.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\07-fp8-e5m2-kv-cache.md", "directory_path": "04-quantization", "filename": "07-fp8-e5m2-kv-cache", "title_hierarchy": "FP8 E5M2 KV 缓存 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "FP8 E5M2 KV 缓存"}}, {"chunk_id": "1484_9563", "file_name": "chunk_0122_1484_9563.txt", "metadata": {"source_file": "version-0.8.x\\04-quantization\\08-fp8-e4m3-kv-cache.md", "directory_path": "04-quantization", "filename": "08-fp8-e4m3-kv-cache", "title_hierarchy": "FP8 E4M3 KV 缓存 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "FP8 E4M3 KV 缓存"}}, {"chunk_id": "9634_4311", "file_name": "chunk_0123_9634_4311.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\01-introduction-apc.md", "directory_path": "05-automatic-prefix-caching", "filename": "01-introduction-apc", "title_hierarchy": "简介 > 什么是自动前缀缓存 (Automatic Prefix Caching)", "main_title": "什么是自动前缀缓存 (Automatic Prefix Caching)", "section_level": 2, "document_title": "简介"}}, {"chunk_id": "9634_0701", "file_name": "chunk_0124_9634_0701.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\01-introduction-apc.md", "directory_path": "05-automatic-prefix-caching", "filename": "01-introduction-apc", "title_hierarchy": "简介 > 在 vLLM 中启用 APC", "main_title": "在 vLLM 中启用 APC", "section_level": 2, "document_title": "简介"}}, {"chunk_id": "9634_6150", "file_name": "chunk_0125_9634_6150.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\01-introduction-apc.md", "directory_path": "05-automatic-prefix-caching", "filename": "01-introduction-apc", "title_hierarchy": "简介 > 工作负载示例", "main_title": "工作负载示例", "section_level": 2, "document_title": "简介"}}, {"chunk_id": "9634_9863", "file_name": "chunk_0126_9634_9863.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\01-introduction-apc.md", "directory_path": "05-automatic-prefix-caching", "filename": "01-introduction-apc", "title_hierarchy": "简介 > 局限性", "main_title": "局限性", "section_level": 2, "document_title": "简介"}}, {"chunk_id": "3532_9456", "file_name": "chunk_0127_3532_9456.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\02-implementation.md", "directory_path": "05-automatic-prefix-caching", "filename": "02-implementation", "title_hierarchy": "实现 (Implementation)", "main_title": "前置内容", "section_level": 0, "document_title": "实现 (Implementation)"}}, {"chunk_id": "3532_0274", "file_name": "chunk_0128_3532_0274.txt", "metadata": {"source_file": "version-0.8.x\\05-automatic-prefix-caching\\02-implementation.md", "directory_path": "05-automatic-prefix-caching", "filename": "02-implementation", "title_hierarchy": "实现 (Implementation) > 通用缓存策略", "main_title": "通用缓存策略", "section_level": 1, "document_title": "实现 (Implementation)"}}, {"chunk_id": "5669_9456", "file_name": "chunk_0129_5669_9456.txt", "metadata": {"source_file": "version-0.8.x\\06-performance-benchmarks\\06-benchmark-suites-of-vllm.md", "directory_path": "06-performance-benchmarks", "filename": "06-benchmark-suites-of-vllm", "title_hierarchy": "vLLM 基准套件", "main_title": "前置内容", "section_level": 0, "document_title": "vLLM 基准套件"}}, {"chunk_id": "5669_9376", "file_name": "chunk_0130_5669_9376.txt", "metadata": {"source_file": "version-0.8.x\\06-performance-benchmarks\\06-benchmark-suites-of-vllm.md", "directory_path": "06-performance-benchmarks", "filename": "06-benchmark-suites-of-vllm", "title_hierarchy": "vLLM 基准套件 > 触发基准测试", "main_title": "触发基准测试", "section_level": 2, "document_title": "vLLM 基准套件"}}, {"chunk_id": "0391_9456", "file_name": "chunk_0131_0391_9456.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\01-sampling-parameters.md", "directory_path": "07-developer-documentation", "filename": "01-sampling-parameters", "title_hierarchy": "采样参数", "main_title": "前置内容", "section_level": 0, "document_title": "采样参数"}}, {"chunk_id": "0391_1390", "file_name": "chunk_0132_0391_1390.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\01-sampling-parameters.md", "directory_path": "07-developer-documentation", "filename": "01-sampling-parameters", "title_hierarchy": "采样参数 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "采样参数"}}, {"chunk_id": "4942_9456", "file_name": "chunk_0133_4942_9456.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力", "main_title": "前置内容", "section_level": 0, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "4942_3359", "file_name": "chunk_0134_4942_3359.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > Softmax > `qk_max` 和 `logits`", "main_title": "`qk_max` 和 `logits`", "section_level": 3, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "4942_3359", "file_name": "chunk_0135_4942_3359.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\04-vllm-paged-attention.md", "directory_path": "07-developer-documentation", "filename": "04-vllm-paged-attention", "title_hierarchy": "vLLM  分页注意力 > Softmax > `exp_sum`", "main_title": "`exp_sum`", "section_level": 3, "document_title": "vLLM  分页注意力"}}, {"chunk_id": "5888_9563", "file_name": "chunk_0136_5888_9563.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\07-dockerfile.md", "directory_path": "07-developer-documentation", "filename": "07-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "Dockerfile > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Dockerfile"}}, {"chunk_id": "3494_9456", "file_name": "chunk_0137_3494_9456.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\08-profiling-vllm.md", "directory_path": "07-developer-documentation", "filename": "08-profiling-vllm", "title_hierarchy": "分析 vLLM", "main_title": "前置内容", "section_level": 0, "document_title": "分析 vLLM"}}, {"chunk_id": "3494_5210", "file_name": "chunk_0138_3494_5210.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\08-profiling-vllm.md", "directory_path": "07-developer-documentation", "filename": "08-profiling-vllm", "title_hierarchy": "分析 vLLM > 命令和使用示例： > 离线推理", "main_title": "离线推理", "section_level": 3, "document_title": "分析 vLLM"}}, {"chunk_id": "3494_5210", "file_name": "chunk_0139_3494_5210.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\08-profiling-vllm.md", "directory_path": "07-developer-documentation", "filename": "08-profiling-vllm", "title_hierarchy": "分析 vLLM > 命令和使用示例： > OpenAI 服务器：", "main_title": "OpenAI 服务器：", "section_level": 3, "document_title": "分析 vLLM"}}, {"chunk_id": "3740_0006", "file_name": "chunk_0140_3740_0006.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > A", "main_title": "A", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_3275", "file_name": "chunk_0141_3740_3275.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > B", "main_title": "B", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_8489", "file_name": "chunk_0142_3740_8489.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > C", "main_title": "C", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_2242", "file_name": "chunk_0143_3740_2242.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > D", "main_title": "D", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_7086", "file_name": "chunk_0144_3740_7086.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > E", "main_title": "E", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_8114", "file_name": "chunk_0145_3740_8114.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > F", "main_title": "F", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_5664", "file_name": "chunk_0146_3740_5664.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > G", "main_title": "G", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_6684", "file_name": "chunk_0147_3740_6684.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > H", "main_title": "H", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_4256", "file_name": "chunk_0148_3740_4256.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > I", "main_title": "I", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_2318", "file_name": "chunk_0149_3740_2318.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > L", "main_title": "L", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_1331", "file_name": "chunk_0150_3740_1331.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > M", "main_title": "M", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_8289", "file_name": "chunk_0151_3740_8289.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > N", "main_title": "N", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_4688", "file_name": "chunk_0152_3740_4688.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > P", "main_title": "P", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_1696", "file_name": "chunk_0153_3740_1696.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > R", "main_title": "R", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_5267", "file_name": "chunk_0154_3740_5267.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > S", "main_title": "S", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_8471", "file_name": "chunk_0155_3740_8471.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > T", "main_title": "T", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_1723", "file_name": "chunk_0156_3740_1723.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > U", "main_title": "U", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "3740_8273", "file_name": "chunk_0157_3740_8273.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\01-index.md", "directory_path": "08-indices-and-tables", "filename": "01-index", "title_hierarchy": "索引 > V", "main_title": "V", "section_level": 2, "document_title": "索引"}}, {"chunk_id": "9941_9563", "file_name": "chunk_0158_9941_9563.txt", "metadata": {"source_file": "version-0.8.x\\08-indices-and-tables\\02-python-module-index.md", "directory_path": "08-indices-and-tables", "filename": "02-python-module-index", "title_hierarchy": "Python 模块索引 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Python 模块索引"}}, {"chunk_id": "8702_9563", "file_name": "chunk_0159_8702_9563.txt", "metadata": {"source_file": "version-0.8.x\\09-community\\01-vllm-meetups.md", "directory_path": "09-community", "filename": "01-vllm-meetups", "title_hierarchy": "vLLM 交流会 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "vLLM 交流会"}}, {"chunk_id": "2531_9563", "file_name": "chunk_0160_2531_9563.txt", "metadata": {"source_file": "version-0.8.x\\09-community\\02-sponsors.md", "directory_path": "09-community", "filename": "02-sponsors", "title_hierarchy": "赞助商 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "赞助商"}}, {"chunk_id": "0700_9456", "file_name": "chunk_0161_0700_9456.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南", "main_title": "前置内容", "section_level": 0, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "0700_8432", "file_name": "chunk_0162_0700_8432.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 二、开始使用 > 2.1 模型准备", "main_title": "2.1 模型准备", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "0700_8432", "file_name": "chunk_0163_0700_8432.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 二、开始使用 > 2.2 离线推理", "main_title": "2.2 离线推理", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "0700_2696", "file_name": "chunk_0164_0700_2696.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 三、启动 vLLM 服务器 > 3.1 主要参数设置", "main_title": "3.1 主要参数设置", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "0700_2696", "file_name": "chunk_0165_0700_2696.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 三、启动 vLLM 服务器 > 3.2 启动命令行", "main_title": "3.2 启动命令行", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "0700_0575", "file_name": "chunk_0166_0700_0575.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 四、发出请求 > 4.1 使用 OpenAI 客户端", "main_title": "4.1 使用 OpenAI 客户端", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "0700_0575", "file_name": "chunk_0167_0700_0575.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\01-vLLM-stepbysteb.md", "directory_path": "10-tutorials", "filename": "01-vLLM-stepby<PERSON>b", "title_hierarchy": "vLLM 入门教程：零基础分步指南 > 四、发出请求 > 4.2 使用 Curl 命令请求", "main_title": "4.2 使用 Curl 命令请求", "section_level": 3, "document_title": "vLLM 入门教程：零基础分步指南"}}, {"chunk_id": "5729_9456", "file_name": "chunk_0168_5729_9456.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理", "main_title": "前置内容", "section_level": 0, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "5729_6602", "file_name": "chunk_0169_5729_6602.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 目录", "main_title": "目录", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "5729_4104", "file_name": "chunk_0170_5729_4104.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 1. 安装 vLLM", "main_title": "1. 安装 vLLM", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "5729_8085", "file_name": "chunk_0171_5729_8085.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 2. 使用 vLLM 加载 Qwen 量化模型", "main_title": "2. 使用 vLLM 加载 Qwen 量化模型", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "5729_9610", "file_name": "chunk_0172_5729_9610.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 3. 加载测试数据", "main_title": "3. 加载测试数据", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "5729_0681", "file_name": "chunk_0173_5729_0681.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 4. 提示工程", "main_title": "4. 提示工程", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "5729_6767", "file_name": "chunk_0174_5729_6767.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 5. Infer 测试", "main_title": "5. <PERSON><PERSON> 测试", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "5729_5356", "file_name": "chunk_0175_5729_5356.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 6. 提取推理概率", "main_title": "6. 提取推理概率", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "5729_2590", "file_name": "chunk_0176_5729_2590.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 7. 创建提交 CSV", "main_title": "7. 创建提交 CSV", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "5729_8040", "file_name": "chunk_0177_5729_8040.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\02-infer-34b-with-vllm.md", "directory_path": "10-tutorials", "filename": "02-infer-34b-with-vllm", "title_hierarchy": "使用 vLLM 对 Qwen2.5 推理 > 8. 计算 CV 分数", "main_title": "8. 计算 CV 分数", "section_level": 2, "document_title": "使用 vLLM 对 Qwen2.5 推理"}}, {"chunk_id": "5112_9456", "file_name": "chunk_0178_5112_9456.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)", "main_title": "前置内容", "section_level": 0, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "5112_1237", "file_name": "chunk_0179_5112_1237.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 5. 辅助函数 > 在给定 subject 和 construct 的情况下获取最相似的 question_ids'", "main_title": "在给定 subject 和 construct 的情况下获取最相似的 question_ids'", "section_level": 3, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "5112_1237", "file_name": "chunk_0180_5112_1237.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 5. 辅助函数 > 获取每个问题的聊天对话", "main_title": "获取每个问题的聊天对话", "section_level": 3, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "5112_3329", "file_name": "chunk_0181_5112_3329.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\03-few-shot-w-qwen2-5.md", "directory_path": "10-tutorials", "filename": "03-few-shot-w-qwen2-5", "title_hierarchy": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 7. 找到最相似的误解 > 合并每个问题的每个生成输出的排名", "main_title": "合并每个问题的每个生成输出的排名", "section_level": 3, "document_title": "使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot)"}}, {"chunk_id": "8769_9456", "file_name": "chunk_0182_8769_9456.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\04-vllm-langchain-tutorial.md", "directory_path": "10-tutorials", "filename": "04-vllm-langchain-tutorial", "title_hierarchy": "将 LangChain 与 vLLM 结合使用：完整教程", "main_title": "前置内容", "section_level": 0, "document_title": "将 LangChain 与 vLLM 结合使用：完整教程"}}, {"chunk_id": "8769_5822", "file_name": "chunk_0183_8769_5822.txt", "metadata": {"source_file": "version-0.8.x\\10-tutorials\\04-vllm-langchain-tutorial.md", "directory_path": "10-tutorials", "filename": "04-vllm-langchain-tutorial", "title_hierarchy": "将 LangChain 与 vLLM 结合使用：完整教程 > 1. 安装和设置 vLLM > Docker 安装", "main_title": "Docker 安装", "section_level": 3, "document_title": "将 LangChain 与 vLLM 结合使用：完整教程"}}, {"chunk_id": "8476_7873", "file_name": "chunk_0184_8476_7873.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\10-examples.md", "directory_path": "01-getting-started > 10-examples", "filename": "10-examples", "title_hierarchy": "示例 > 脚本", "main_title": "脚本", "section_level": 2, "document_title": "示例"}}, {"chunk_id": "7847_9563", "file_name": "chunk_0185_7847_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\01-api_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "01-api_client", "title_hierarchy": "API 客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "API 客户端"}}, {"chunk_id": "4226_9563", "file_name": "chunk_0186_4226_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\02-aqlm_example.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "02-aqlm_example", "title_hierarchy": "Aqlm 示例 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Aqlm 示例"}}, {"chunk_id": "1670_9563", "file_name": "chunk_0187_1670_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\03-cpu_offload.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "03-cpu_offload", "title_hierarchy": "CPU 离线处理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "CPU 离线处理"}}, {"chunk_id": "9089_9563", "file_name": "chunk_0188_9089_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\04-gguf_inference.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "04-gguf_inference", "title_hierarchy": "GGUF 推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "GGUF 推理"}}, {"chunk_id": "4441_9563", "file_name": "chunk_0189_4441_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\05-gradio_openai_chatbot_webserver.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "05-gradio_openai_chatbot_webserver", "title_hierarchy": "Gradio OpenAI 聊天机器人 Web 服务器 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Gradio OpenAI 聊天机器人 Web 服务器"}}, {"chunk_id": "9641_9563", "file_name": "chunk_0190_9641_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\06-gradio_webserver.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "06-gradio_webserver", "title_hierarchy": "Gradio Web 服务器 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Gradio Web 服务器"}}, {"chunk_id": "2410_9563", "file_name": "chunk_0191_2410_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\07-llm_engine_example.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "07-llm_engine_example", "title_hierarchy": "LLM 引擎示例 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "LLM 引擎示例"}}, {"chunk_id": "9192_9563", "file_name": "chunk_0192_9192_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\08-lora_with_quantization_inference.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "08-lora_with_quantization_inference", "title_hierarchy": "带量化的 LoRA 推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "带量化的 LoRA 推理"}}, {"chunk_id": "6815_9563", "file_name": "chunk_0193_6815_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\09-multilora_inference.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "09-multilora_inference", "title_hierarchy": "MultiLoRA 推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "MultiLoRA 推理"}}, {"chunk_id": "1433_9563", "file_name": "chunk_0194_1433_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\10-offline_chat_with_tools.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "10-offline_chat_with_tools", "title_hierarchy": "离线聊天工具 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线聊天工具"}}, {"chunk_id": "4932_9563", "file_name": "chunk_0195_4932_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\11-offline_inference.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "11-offline_inference", "title_hierarchy": "API Client > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "API Client"}}, {"chunk_id": "9278_9563", "file_name": "chunk_0196_9278_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\12-offline_inference_arctic.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "12-offline_inference_arctic", "title_hierarchy": "离线推理 Arctic > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 Arctic"}}, {"chunk_id": "3777_9563", "file_name": "chunk_0197_3777_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\13-offline_inference_audio_language.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "13-offline_inference_audio_language", "title_hierarchy": "离线推理音频语言 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理音频语言"}}, {"chunk_id": "7436_9563", "file_name": "chunk_0198_7436_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\14-offline_inference_chat.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "14-offline_inference_chat", "title_hierarchy": "离线推理聊天 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理聊天"}}, {"chunk_id": "9840_9563", "file_name": "chunk_0199_9840_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\15-offline_inference_distributed.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "15-offline_inference_distributed", "title_hierarchy": "离线推理分布式 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理分布式"}}, {"chunk_id": "9486_9563", "file_name": "chunk_0200_9486_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\16-offline_inference_embedding.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "16-offline_inference_embedding", "title_hierarchy": "离线推理嵌入 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理嵌入"}}, {"chunk_id": "5472_9563", "file_name": "chunk_0201_5472_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\17-offline_inference_encoder_decoder.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "17-offline_inference_encoder_decoder", "title_hierarchy": "离线推理编码器-解码器 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理编码器-解码器"}}, {"chunk_id": "1621_9563", "file_name": "chunk_0202_1621_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\18-offline_inference_mlpspeculator.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "18-offline_inference_mlpspeculator", "title_hierarchy": "离线推理 MlpSpeculator > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 MlpSpeculator"}}, {"chunk_id": "9218_9563", "file_name": "chunk_0203_9218_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\19-offline_inference_neuron.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "19-offline_inference_neuron", "title_hierarchy": "离线推理 Neuron > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 Neuron"}}, {"chunk_id": "0158_9563", "file_name": "chunk_0204_0158_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\20-offline_inference_neuron_int8_quantization.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "20-offline_inference_neuron_int8_quantization", "title_hierarchy": "离线推理 Neuron Int8 量化 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 Neuron Int8 量化"}}, {"chunk_id": "3726_9563", "file_name": "chunk_0205_3726_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\21-offline_inference_pixtral.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "21-offline_inference_pixtral", "title_hierarchy": "使用 Pixtral 进行离线推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 Pixtral 进行离线推理"}}, {"chunk_id": "9376_9563", "file_name": "chunk_0206_9376_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\22-offline_inference_tpu.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "22-offline_inference_tpu", "title_hierarchy": "离线推理 TPU > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理 TPU"}}, {"chunk_id": "6653_9563", "file_name": "chunk_0207_6653_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\23-offline_inference_vision_language.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "23-offline_inference_vision_language", "title_hierarchy": "离线推理视觉语言 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理视觉语言"}}, {"chunk_id": "9301_9563", "file_name": "chunk_0208_9301_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\24-offline_inference_vision_language_multi_image.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "24-offline_inference_vision_language_multi_image", "title_hierarchy": "离线推理视觉语言多图像 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理视觉语言多图像"}}, {"chunk_id": "9055_9563", "file_name": "chunk_0209_9055_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\25-offline_inference_with_prefix.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "25-offline_inference_with_prefix", "title_hierarchy": "带前缀的离线推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "带前缀的离线推理"}}, {"chunk_id": "9812_9563", "file_name": "chunk_0210_9812_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\26-offline_inference_with_profiler.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "26-offline_inference_with_profiler", "title_hierarchy": "启用分析器的离线推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "启用分析器的离线推理"}}, {"chunk_id": "0287_9563", "file_name": "chunk_0211_0287_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\27-openai_audio_api_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "27-openai_audio_api_client", "title_hierarchy": "OpenAI 音频 API 客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 音频 API 客户端"}}, {"chunk_id": "7836_9563", "file_name": "chunk_0212_7836_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\28-openai_chat_completion_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "28-openai_chat_completion_client", "title_hierarchy": "OpenAI 聊天补全客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 聊天补全客户端"}}, {"chunk_id": "3806_9563", "file_name": "chunk_0213_3806_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\29-openai_chat_completion_client_with_tools.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "29-openai_chat_completion_client_with_tools", "title_hierarchy": "带有工具的 OpenAI 聊天补全客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "带有工具的 OpenAI 聊天补全客户端"}}, {"chunk_id": "4036_9563", "file_name": "chunk_0214_4036_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\30-openai_completion_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "30-openai_completion_client", "title_hierarchy": "OpenAI 补全客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 补全客户端"}}, {"chunk_id": "4350_9563", "file_name": "chunk_0215_4350_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\31-openai_embedding_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "31-openai_embedding_client", "title_hierarchy": "OpenAI 嵌入客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 嵌入客户端"}}, {"chunk_id": "6367_9563", "file_name": "chunk_0216_6367_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\32-openai_vision_api_client.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "32-openai_vision_api_client", "title_hierarchy": "OpenAI 视觉 API 客户端 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "OpenAI 视觉 API 客户端"}}, {"chunk_id": "9270_9563", "file_name": "chunk_0217_9270_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\33-save_sharded_state.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "33-save_sharded_state", "title_hierarchy": "保存分片状态 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "保存分片状态"}}, {"chunk_id": "9637_9563", "file_name": "chunk_0218_9637_9563.txt", "metadata": {"source_file": "version-0.8.x\\01-getting-started\\10-examples\\examples\\34-tensorize_vllm_model.md", "directory_path": "01-getting-started > 10-examples > examples", "filename": "34-tensorize_vllm_model", "title_hierarchy": "Tensorize vLLM 模型 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "Tensorize vLLM 模型"}}, {"chunk_id": "1975_9456", "file_name": "chunk_0219_1975_9456.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\01-deploying&scaling-up-with-skypilot.md", "directory_path": "02-serving > 07-integrations", "filename": "01-deploying&scaling-up-with-skypilot", "title_hierarchy": "使用 SkyPilot 进行部署和扩充", "main_title": "前置内容", "section_level": 0, "document_title": "使用 SkyPilot 进行部署和扩充"}}, {"chunk_id": "1975_6358", "file_name": "chunk_0220_1975_6358.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\01-deploying&scaling-up-with-skypilot.md", "directory_path": "02-serving > 07-integrations", "filename": "01-deploying&scaling-up-with-skypilot", "title_hierarchy": "使用 SkyPilot 进行部署和扩充 > 扩充到多个副本 > **可选**: 将 GUI 连接到端点", "main_title": "**可选**: 将 GUI 连接到端点", "section_level": 3, "document_title": "使用 SkyPilot 进行部署和扩充"}}, {"chunk_id": "1365_9563", "file_name": "chunk_0221_1365_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\02-deploying-with-kserve.md", "directory_path": "02-serving > 07-integrations", "filename": "02-deploying-with-kserve", "title_hierarchy": "使用 KServe 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 KServe 进行部署"}}, {"chunk_id": "7902_9563", "file_name": "chunk_0222_7902_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\03-deploying-with-nvidia-triton.md", "directory_path": "02-serving > 07-integrations", "filename": "03-deploying-with-nvidia-triton", "title_hierarchy": "使用 NVIDIA Triton 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 NVIDIA Triton 进行部署"}}, {"chunk_id": "2325_9563", "file_name": "chunk_0223_2325_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\04-deploying-with-bentoml.md", "directory_path": "02-serving > 07-integrations", "filename": "04-deploying-with-bentoml", "title_hierarchy": "使用 BentoML 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 BentoML 进行部署"}}, {"chunk_id": "3857_9563", "file_name": "chunk_0224_3857_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\05-deploying-with-cerebrium.md", "directory_path": "02-serving > 07-integrations", "filename": "05-deploying-with-cerebrium", "title_hierarchy": "使用 Cerebrium 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 Cerebrium 进行部署"}}, {"chunk_id": "2833_9563", "file_name": "chunk_0225_2833_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\06-deploying-with-lws.md", "directory_path": "02-serving > 07-integrations", "filename": "06-deploying-with-lws", "title_hierarchy": "使用 LWS 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 LWS 进行部署"}}, {"chunk_id": "4241_9563", "file_name": "chunk_0226_4241_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\07-deploying-with-dstack.md", "directory_path": "02-serving > 07-integrations", "filename": "07-deploying-with-dstack", "title_hierarchy": "使用 dstack 进行部署 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 dstack 进行部署"}}, {"chunk_id": "3601_9563", "file_name": "chunk_0227_3601_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\08-serving-with-langchain.md", "directory_path": "02-serving > 07-integrations", "filename": "08-serving-with-langchain", "title_hierarchy": "使用 Langchain 提供服务 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 Langchain 提供服务"}}, {"chunk_id": "9012_9563", "file_name": "chunk_0228_9012_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\09-serving-with-llama_index.md", "directory_path": "02-serving > 07-integrations", "filename": "09-serving-with-llama_index", "title_hierarchy": "使用 llama_index 服务 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "使用 llama_index 服务"}}, {"chunk_id": "7963_9563", "file_name": "chunk_0229_7963_9563.txt", "metadata": {"source_file": "version-0.8.x\\02-serving\\07-integrations\\readme.md", "directory_path": "02-serving > 07-integrations", "filename": "readme", "title_hierarchy": "整合 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "整合"}}, {"chunk_id": "7520_9456", "file_name": "chunk_0230_7520_9456.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类", "main_title": "前置内容", "section_level": 0, "document_title": "LLM 类"}}, {"chunk_id": "7520_1390", "file_name": "chunk_0231_7520_1390.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "7520_1390", "file_name": "chunk_0232_7520_1390.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "7520_0650", "file_name": "chunk_0233_7520_0650.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 返回", "main_title": "返回", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "7520_1390", "file_name": "chunk_0234_7520_1390.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "7520_0650", "file_name": "chunk_0235_7520_0650.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 返回", "main_title": "返回", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "7520_1390", "file_name": "chunk_0236_7520_1390.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 参数：", "main_title": "参数：", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "7520_0650", "file_name": "chunk_0237_7520_0650.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\01-llm-class.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "01-llm-class", "title_hierarchy": "LLM 类 > 返回", "main_title": "返回", "section_level": 2, "document_title": "LLM 类"}}, {"chunk_id": "8264_9253", "file_name": "chunk_0238_8264_9253.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\02-llm-inputs.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "02-llm-inputs", "title_hierarchy": "LLM Inputs > vllm.inputs.PromptType", "main_title": "vllm.inputs.PromptType", "section_level": 2, "document_title": "LLM Inputs"}}, {"chunk_id": "8596_9563", "file_name": "chunk_0239_8596_9563.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\02-offline-inference\\readme.md", "directory_path": "07-developer-documentation > 02-offline-inference", "filename": "readme", "title_hierarchy": "离线推理 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "离线推理"}}, {"chunk_id": "5461_9456", "file_name": "chunk_0240_5461_9456.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine", "main_title": "前置内容", "section_level": 0, "document_title": "LLMEngine"}}, {"chunk_id": "5461_2854", "file_name": "chunk_0241_5461_2854.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine > Example", "main_title": "Example", "section_level": 2, "document_title": "LLMEngine"}}, {"chunk_id": "5461_7437", "file_name": "chunk_0242_5461_7437.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine > 案例", "main_title": "案例", "section_level": 2, "document_title": "LLMEngine"}}, {"chunk_id": "5461_3445", "file_name": "chunk_0243_5461_3445.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine > 详细信息：", "main_title": "详细信息：", "section_level": 2, "document_title": "LLMEngine"}}, {"chunk_id": "5461_3500", "file_name": "chunk_0244_5461_3500.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\01-llmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "01-<PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "LLMEngine > 示例", "main_title": "示例", "section_level": 2, "document_title": "LLMEngine"}}, {"chunk_id": "9420_9563", "file_name": "chunk_0245_9420_9563.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\02-asyncllmengine.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "02-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title_hierarchy": "AsyncLLMEngine > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "AsyncLLMEngine"}}, {"chunk_id": "4499_9563", "file_name": "chunk_0246_4499_9563.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\03-vllm-engine\\readme.md", "directory_path": "07-developer-documentation > 03-vllm-engine", "filename": "readme", "title_hierarchy": "vLLM Engine > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "vLLM Engine"}}, {"chunk_id": "8534_9456", "file_name": "chunk_0247_8534_9456.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\05-input-processing\\01-model_inputs_index.md", "directory_path": "07-developer-documentation > 05-input-processing", "filename": "01-model_inputs_index", "title_hierarchy": "输入处理", "main_title": "前置内容", "section_level": 0, "document_title": "输入处理"}}, {"chunk_id": "8534_4640", "file_name": "chunk_0248_8534_4640.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\05-input-processing\\01-model_inputs_index.md", "directory_path": "07-developer-documentation > 05-input-processing", "filename": "01-model_inputs_index", "title_hierarchy": "输入处理 > 模块内容 > LLM 引擎输入", "main_title": "LLM 引擎输入", "section_level": 3, "document_title": "输入处理"}}, {"chunk_id": "8534_4640", "file_name": "chunk_0249_8534_4640.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\05-input-processing\\01-model_inputs_index.md", "directory_path": "07-developer-documentation > 05-input-processing", "filename": "01-model_inputs_index", "title_hierarchy": "输入处理 > 模块内容 > 注册", "main_title": "注册", "section_level": 3, "document_title": "输入处理"}}, {"chunk_id": "8904_9563", "file_name": "chunk_0250_8904_9563.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\05-input-processing\\02-input-processing-pipeline.md", "directory_path": "07-developer-documentation > 05-input-processing", "filename": "02-input-processing-pipeline", "title_hierarchy": "输入处理管道 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "输入处理管道"}}, {"chunk_id": "1837_9563", "file_name": "chunk_0251_1837_9563.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\06-multi-modality\\01-adding-a-multimodal-plugin.md", "directory_path": "07-developer-documentation > 06-multi-modality", "filename": "01-adding-a-multimodal-plugin", "title_hierarchy": "添加多模态插件 > 文档内容", "main_title": "文档内容", "section_level": 1, "document_title": "添加多模态插件"}}, {"chunk_id": "2531_9456", "file_name": "chunk_0252_2531_9456.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\06-multi-modality\\readme.md", "directory_path": "07-developer-documentation > 06-multi-modality", "filename": "readme", "title_hierarchy": "多模态", "main_title": "前置内容", "section_level": 0, "document_title": "多模态"}}, {"chunk_id": "2531_4640", "file_name": "chunk_0253_2531_4640.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\06-multi-modality\\readme.md", "directory_path": "07-developer-documentation > 06-multi-modality", "filename": "readme", "title_hierarchy": "多模态 > 模块内容 > 注册", "main_title": "注册", "section_level": 3, "document_title": "多模态"}}, {"chunk_id": "2531_4640", "file_name": "chunk_0254_2531_4640.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\06-multi-modality\\readme.md", "directory_path": "07-developer-documentation > 06-multi-modality", "filename": "readme", "title_hierarchy": "多模态 > 模块内容 > 基础类 (Base Classes)", "main_title": "基础类 (Base Classes)", "section_level": 3, "document_title": "多模态"}}, {"chunk_id": "2531_4640", "file_name": "chunk_0255_2531_4640.txt", "metadata": {"source_file": "version-0.8.x\\07-developer-documentation\\06-multi-modality\\readme.md", "directory_path": "07-developer-documentation > 06-multi-modality", "filename": "readme", "title_hierarchy": "多模态 > 模块内容 > 图像类", "main_title": "图像类", "section_level": 3, "document_title": "多模态"}}]