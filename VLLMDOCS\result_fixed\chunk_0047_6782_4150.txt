# 文档路径: 02-serving > 03-distributed-inference-and-serving > 分布式推理和服务 > 多节点推理和服务

## 多节点推理和服务


如果单个节点没有足够的 GPU 来容纳模型，您可以使用多个节点运行模型。确保所有节点上的执行环境相同是非常重要的，包括模型路径、Python 环境等。推荐的方法是使用 docker 镜像来确保相同的环境，并通过将它们映射到相同的 docker 配置来隐藏主机的异构性。

第一步是启动容器并将它们组织成一个集群。我们提供了一个辅助[脚本](https://github.com/vllm-project/vllm/tree/main/examples/run_cluster.sh)来启动集群。

选择一个节点作为头节点，然后运行以下命令：

```plain
bash run_cluster.sh \
                  vllm/vllm-openai \
                  ip_of_head_node \
                  --head \
                  /path/to/the/huggingface/home/<USER>/this/node
```

在其余工作节点上，运行以下命令：

```plain
bash run_cluster.sh \
                  vllm/vllm-openai \
                  ip_of_head_node \
                  --worker \
                  /path/to/the/huggingface/home/<USER>/this/node
```

然后你会得到一个由容器组成的 Ray 集群。请注意，您需要使运行这些命令的 shell 保持活动状态以维持集群。任何 shell 的断开连接都会终止集群。另外，请注意参数 `ip_of_head_node` 应该是头节点的 IP 地址，所有工作节点都可以访问该 IP 地址。一个常见的误解是使用工作节点的 IP 地址，这是不正确的。

然后，在任意节点上，使用 `docker exec -it node /bin/bash` 进入容器，执行 `ray status` 查看 Ray 集群的状态。您应该看到正确数量的节点和 GPU。

之后，在任何节点上，您都可以照常使用 vLLM，就像所有的 GPU 都在一个节点上一样。常见的做法是将张量并行大小设置为每个节点中的 GPU 数量，将管道并行大小设置为节点数量。例如，如果 2 个节点中有 16 个 GPU（每个节点 8 个 GPU），则可以将张量并行大小设置为 8，将管道并行大小设置为 2：

```plain
vllm serve /path/to/the/model/in/the/container \
    --tensor-parallel-size 8 \
    --pipeline-parallel-size 2
```

您也可以使用张量并行而不使用管道并行，只需将张量并行大小设置为集群中的 GPU 数量即可。例如，如果 2 个节点中有 16 个 GPU（每个节点 8 个 GPU），则可以将张量并行大小设置为 16：

```plain
vllm serve /path/to/the/model/in/the/container \
    --tensor-parallel-size 16
```

为了使张量并行具有良好的性能，您应该确保节点之间的通信高效，例如使用 Infiniband 等高速网卡。要正确设置集群以使用 Infiniband，请将 `--privileged -e NCCL_IB_HCA=mlx5` 等附加参数附加到 `run_cluster.sh` 脚本中。请联系您的系统管理员以获取有关如何设置标志的更多信息。确认 Infiniband 是否正常工作的一种方法是使用 `NCCL_DEBUG=TRACE` 环境变量集运行 vLLM，例如`NCCL_DEBUG=TRACE vllmserve ...` ，并检查日志以了解 NCCL 版本和使用的网络。如果你在日志中发现 `[send] via NET/Socket` ，则意味着 NCCL 使用原始 TCP Socket，这对于跨节点张量并行来说效率不高。如果您在日志中找到 `[send] via NET/IB/GDRDMA` ，则意味着 NCCL 使用 Infiniband 和 GPU-Direct RDMA，效率很高。

**警告**

在启动 Ray 集群后，最好检查一下节点之间的 GPU-GPU 通信，设置这个并不简单。请参阅 [健全性检查脚本](https://docs.vllm.ai/en/latest/getting_started/debugging.html) 了解更多信息。如果需要为通信配置设置一些环境变量，可以将它们附加到`run_cluster.sh`脚本中，例如`-e NCCL_SOCKET_IFNAME=eth0`。请注意，在 shell 中设置环境变量（例如 `NCCL_SOCKET_IFNAME=eth0 vllmserve ...`）仅适用于同一节点中的进程，不适用于其他节点中的进程，推荐在创建集群时设置环境变量。有关更多信息，请参阅 [讨论](https://github.com/vllm-project/vllm/issues/6803)。

**警告**

请确保你已将模型下载到所有节点（具有相同的路径），或者将模型下载到所有节点均可访问的某个分布式文件系统中。

当您使用 Huggingface repo id 来引用模型时，您应该将您的 Huggingface token 附加到 `run_cluster.sh` 脚本中，如`-e HF_TOKEN=`。推荐的方式是先下载模型，然后使用路径引用模型。
