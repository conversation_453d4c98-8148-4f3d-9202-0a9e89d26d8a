# 文档路径: 07-developer-documentation > 03-vllm-engine > 01-llmengine > LLMEngine > Example

> class vllm.LLMEngine(model_config: ModelConfig, cache_config: CacheConfig, parallel_config: ParallelConfig, scheduler_config: SchedulerConfig, device_config: DeviceConfig, load_config: LoadConfig, lora_config: LoRAConfig | [None](https://docs.python.org/3/library/constants.html#None), speculative_config: SpeculativeConfig | [None](https://docs.python.org/3/library/constants.html#None), decoding_config: DecodingConfig | [None](https://docs.python.org/3/library/constants.html#None), observability_config: ObservabilityConfig | [None](https://docs.python.org/3/library/constants.html#None), prompt_adapter_config: PromptAdapterConfig | [None](https://docs.python.org/3/library/constants.html#None), executor_class: [Type](https://docs.python.org/3/library/typing.html#typing.Type)[ExecutorBase], log_stats: [bool](https://docs.python.org/3/library/functions.html#bool), usage_context: UsageContext = UsageContext.ENGINE_CONTEXT, stat_loggers: [Dict](https://docs.python.org/3/library/typing.html#typing.Dict)[[str](https://docs.python.org/3/library/stdtypes.html#str), StatLoggerBase] | [None](https://docs.python.org/3/library/constants.html#None) = None, input_registry: [InputRegistry](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputRegistry) = INPUT_REGISTRY, use_cached_outputs: [bool](https://docs.python.org/3/library/functions.html#bool) = False)
[[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine)

一个接收请求并生成文本的大语言模型 (LLM) 引擎。

这是 vLLM 引擎的主类。它接收客户端的请求并从 LLM 中生成文本。它包括一个 tokenizer、一个语言模型（可能分布在多个 GPU 上）以及为中间状态分配的 GPU 内存空间（又名 KV 缓存）。该类利用 iteration-level 调度和高效的内存管理使得服务吞吐量最大化。

[LLM](https://docs.vllm.ai/en/latest/dev/offline_inference/llm.html#vllm.LLM) 类包装该类进行离线批量推理，[AsyncLLMEngine](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine) 类包装该类进行在线服务。

配置参数源自 `EngineArgs`。（参见 [Engine Arguments](https://docs.vllm.ai/en/latest/models/engine_args.html#engine-args)）

**参数：**

- **model_config** – 与 LLM 模型相关的配置。
- **cache_config** – 与 KV 缓存管理相关的配置。
- **parallel_config** – 与分布式执行相关的配置。
- **scheduler_config** – 与请求调度程序相关的配置。
- **device_config** – 与设备相关的配置。
- **lora_config (Optional)** – 与服务多 LoRA 相关的配置。
- **speculative_config (Optional)** – 与推测解码相关的配置。
- **executor_class** – 用于管理分布式执行的模型执行器类。
- **prompt_adapter_config (Optional)** – 与服务提示适配器相关的配置。
- **log_stats** – 是否记录统计信息。
- **usage_context** – 指定的入口点，用于收集使用信息。

> DO_VALIDATE_OUTPUT: [ClassVar](https://docs.python.org/3/library/typing.html#typing.ClassVar)[[bool](https://docs.python.org/3/library/functions.html#bool)] = False
> 用于切换是否验证请求输出类型的标志。

> abort_request(request_id: [str](https://docs.python.org/3/library/stdtypes.html#str) | [Iterable](https://docs.python.org/3/library/typing.html#typing.Iterable)[[str](https://docs.python.org/3/library/stdtypes.html#str)]) → None
[[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.abort_request)

中止给定 ID 的请求。

**参数：**

- **request_id** – 中止请求的 ID。

**细节：**

- 请参阅 `Scheduler` 类中的 `abort_seq_group()`。

**案例**

```plain
# initialize engine and add a request with request_id
# 初始化引擎并添加一个带 request_id 的请求
request_id = str(0)
# abort the request
# 中止请求
engine.abort_request(request_id)
```

> add_request(request_id: [str](https://docs.python.org/3/library/stdtypes.html#str), inputs: [str](https://docs.python.org/3/library/stdtypes.html#str) | [TextPrompt](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TextPrompt) | [TokensPrompt](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TokensPrompt) | ExplicitEncoderDecoderPrompt, params: [SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams) | PoolingParams, arrival_time: [float](https://docs.python.org/3/library/functions.html#float) | [None](https://docs.python.org/3/library/constants.html#None) = None, lora_request: LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None, trace_headers: [Mapping](https://docs.python.org/3/library/typing.html#typing.Mapping)[[str](https://docs.python.org/3/library/stdtypes.html#str), [str](https://docs.python.org/3/library/stdtypes.html#str)] | [None](https://docs.python.org/3/library/constants.html#None) = None, prompt_adapter_request: PromptAdapterRequest | [None](https://docs.python.org/3/library/constants.html#None) = None) → [None](https://docs.python.org/3/library/constants.html#None)
[[源代码]](https://docs.vllm.ai/en/latest/_modules/vllm/engine/llm_engine.html#LLMEngine.add_request)

> add_request(request_id: [str](https://docs.python.org/3/library/stdtypes.html#str), prompt: PromptType, params: [SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams) | PoolingParams, arrival_time: [float](https://docs.python.org/3/library/functions.html#float) | [None](https://docs.python.org/3/library/constants.html#None) = None, lora_request: LoRARequest | [None](https://docs.python.org/3/library/constants.html#None) = None, trace_headers: Mapping[[str](https://docs.python.org/3/library/stdtypes.html#str), [str](https://docs.python.org/3/library/stdtypes.html#str)] | [None](https://docs.python.org/3/library/constants.html#None) = None, prompt_adapter_request: PromptAdapterRequest | [None](https://docs.python.org/3/library/constants.html#None) = None, priority: [int](https://docs.python.org/3/library/functions.html#int) = 0) → [None](https://docs.python.org/3/library/constants.html#None)

将请求添加到引擎的请求池中。

该请求将被添加到请求池中，并在调用 engine.step() 时由调度程序进行处理。具体的调度策略由调度器决定。

**参数：**

- **request_id** – 请求的唯一 ID。
- **inputs** – 提供给 LLM 的输入。有关每个输入格式的更多详细信息，请参阅 [PromptInputs](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.PromptInputs)。
- **params** – 用于采样或池化的参数。[SamplingParams](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams) 用于文本生成。`PoolingParams`用于池化。
- **arrival_time** – 请求的到达时间。如果为 None，我们将使用当前的单调时间。
- **trace_headers** – OpenTelemetry 跟踪头。
- **priority\*\***–\*\*请求的优先级。仅在优先级调度时适用。

##

**细节：**

- 如果到达时间 (arrival_time) 为 None，则将其设置为当前时间。
- 如果 prompt_token_ids 为 None，则将其设置为编码提示。
- 创建 n 个 `Sequence` 对象。
- 从`Sequence`列表中创建一个 `SequenceGroup` 对象。
- 将 `SequenceGroup` 对象添加到调度程序中。

## Example

