"""
高级错误分析器

进一步优化的版本，包含更好的特征工程和数据增强
"""

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.metrics import classification_report, accuracy_score
from sklearn.preprocessing import LabelEncoder, StandardScaler
from imblearn.over_sampling import SMOTE
from imblearn.combine import SMOTETomek
import re
import jieba
from typing import List, Dict, Tuple, Any
import logging
import json
import joblib
import os
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedErrorAnalyzer:
    """
    高级错误分析器
    
    包含更好的特征工程、数据平衡和模型集成
    """
    
    def __init__(self):
        self.vectorizer = None
        self.model = None
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        self.feature_names = []
        self.label_mapping = {}
        
        # 基于实际数据优化的错误模式
        self.error_patterns = {
            # 超时相关
            '超时模式': ['600s', '600000ms', 'timeout', 'time', '超时', '时间', 'observe', 'terminal', 'signal'],
            
            # 认证相关  
            '认证模式': ['auth', 'authentication', 'token', '认证', '鉴权', '权限', 'failed', 'invalid'],
            
            # 模型相关
            '模型模式': ['model', 'deepseek', 'qwen', 'embedding', '模型', 'name', 'not found'],
            
            # 请求格式相关
            '格式模式': ['json', 'parse', 'arguments', 'format', '格式', '解析', 'body', 'content'],
            
            # 参数相关
            '参数模式': ['parameter', 'param', 'top_p', 'dimensions', '参数', 'field', 'missing'],
            
            # 多模态相关
            '多模态模式': ['multimodal', 'image', 'text', 'type', '多模态', '图片', 'base64'],
            
            # 工具调用相关
            '工具模式': ['tool', 'function', 'call', '工具', '函数', 'tools'],
            
            # 连接相关
            '连接模式': ['connection', 'connect', '连接', '链接', 'refused', 'closed'],
            
            # 响应相关
            '响应模式': ['response', 'answer', 'null', '响应', '回答', 'empty'],
            
            # 长度/大小相关
            '长度模式': ['length', 'size', 'long', 'large', '长', '大', 'tokens', 'limit']
        }
        
        # 关键词权重（基于重要性）
        self.keyword_weights = {
            '600s': 5.0, '600000ms': 5.0, 'timeout': 4.0,
            'authentication': 4.0, 'token': 3.0, 'failed': 3.0,
            'model': 3.0, 'json': 3.0, 'arguments': 4.0,
            'multimodal': 4.0, 'image': 3.0, 'tools': 3.0,
            'connection': 3.0, 'null': 3.0, 'tokens': 3.0
        }
    
    def extract_semantic_features(self, text: str) -> Dict[str, float]:
        """
        提取语义特征
        
        Args:
            text: 输入文本
            
        Returns:
            特征字典
        """
        features = {}
        text_lower = text.lower()
        
        # 1. 错误模式匹配（带权重）
        for pattern_name, keywords in self.error_patterns.items():
            weighted_score = 0
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    weight = self.keyword_weights.get(keyword, 1.0)
                    weighted_score += weight
            features[f'pattern_{pattern_name}'] = weighted_score
        
        # 2. 数字和时间特征
        numbers = re.findall(r'\d+', text)
        features['number_count'] = len(numbers)
        features['has_timeout_number'] = 1 if any(n in ['600', '30', '60'] for n in numbers) else 0
        features['has_http_code'] = 1 if any(n in ['400', '401', '403', '404', '500', '502', '503'] for n in numbers) else 0
        features['has_large_number'] = 1 if any(int(n) > 1000 for n in numbers if n.isdigit()) else 0
        
        # 3. 特殊字符和结构
        features['has_quotes'] = text.count('"') + text.count("'")
        features['has_brackets'] = text.count('(') + text.count('[') + text.count('{')
        features['has_json_structure'] = 1 if ('{' in text and '}' in text) else 0
        features['has_url_structure'] = 1 if ('http' in text_lower or 'www' in text_lower) else 0
        
        # 4. 文本统计特征
        words = text.split()
        features['text_length'] = len(text)
        features['word_count'] = len(words)
        features['avg_word_length'] = np.mean([len(word) for word in words]) if words else 0
        features['unique_word_ratio'] = len(set(words)) / len(words) if words else 0
        
        # 5. 语言和编码特征
        chinese_chars = len(re.findall(r'[\u4e00-\u9fa5]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        total_chars = len(text)
        features['chinese_ratio'] = chinese_chars / total_chars if total_chars else 0
        features['english_ratio'] = english_chars / total_chars if total_chars else 0
        features['digit_ratio'] = len(re.findall(r'\d', text)) / total_chars if total_chars else 0
        
        # 6. 错误严重程度指示
        critical_words = ['error', 'failed', 'exception', 'null', 'invalid', 'refused', 'timeout']
        features['critical_word_count'] = sum(1 for word in critical_words if word in text_lower)
        
        # 7. 技术术语密度
        tech_terms = ['api', 'http', 'json', 'token', 'model', 'request', 'response', 'parameter']
        features['tech_term_density'] = sum(1 for term in tech_terms if term in text_lower) / len(words) if words else 0
        
        return features
    
    def create_advanced_features(self, texts: List[str]) -> Tuple[np.ndarray, List[str]]:
        """
        创建高级特征
        
        Args:
            texts: 文本列表
            
        Returns:
            特征矩阵和特征名称
        """
        # 1. 文本预处理
        processed_texts = []
        for text in texts:
            # 保留更多有用信息
            text = str(text).strip()
            # 标准化但保留结构
            text = re.sub(r'\s+', ' ', text)
            processed_texts.append(text)
        
        # 2. TF-IDF特征（优化参数）
        if self.vectorizer is None:
            self.vectorizer = TfidfVectorizer(
                max_features=150,  # 适中的特征数量
                ngram_range=(1, 3),  # 包含三元组
                min_df=1,
                max_df=0.9,
                stop_words=None,
                token_pattern=r'(?u)\b\w+\b',
                sublinear_tf=True,  # 使用对数缩放
                norm='l2'
            )
            tfidf_features = self.vectorizer.fit_transform(processed_texts)
        else:
            tfidf_features = self.vectorizer.transform(processed_texts)
        
        # 3. 语义特征
        semantic_features = []
        for text in texts:
            features = self.extract_semantic_features(text)
            semantic_features.append(list(features.values()))
        
        semantic_features = np.array(semantic_features)
        
        # 4. 特征标准化
        if not hasattr(self, '_semantic_fitted'):
            semantic_features = self.scaler.fit_transform(semantic_features)
            self._semantic_fitted = True
        else:
            semantic_features = self.scaler.transform(semantic_features)
        
        # 5. 组合特征
        combined_features = np.hstack([tfidf_features.toarray(), semantic_features])
        
        # 6. 特征名称
        tfidf_names = [f'tfidf_{i}' for i in range(tfidf_features.shape[1])]
        semantic_names = list(self.extract_semantic_features("").keys())
        feature_names = tfidf_names + semantic_names
        
        return combined_features, feature_names
    
    def balance_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        数据平衡处理
        
        Args:
            X: 特征矩阵
            y: 标签
            
        Returns:
            平衡后的特征和标签
        """
        try:
            # 使用SMOTE进行过采样
            smote_tomek = SMOTETomek(
                smote=SMOTE(
                    sampling_strategy='auto',
                    k_neighbors=min(3, len(np.unique(y)) - 1),
                    random_state=42
                ),
                random_state=42
            )
            X_balanced, y_balanced = smote_tomek.fit_resample(X, y)
            
            logger.info(f"数据平衡: {X.shape[0]} -> {X_balanced.shape[0]} 样本")
            return X_balanced, y_balanced
            
        except Exception as e:
            logger.warning(f"数据平衡失败: {e}, 使用原始数据")
            return X, y
    
    def train_ensemble_model(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练集成模型
        
        Args:
            X: 特征矩阵
            y: 标签
            
        Returns:
            训练结果
        """
        # 基础模型
        rf = RandomForestClassifier(
            n_estimators=200,
            max_depth=15,
            min_samples_split=3,
            min_samples_leaf=2,
            random_state=42,
            class_weight='balanced',
            bootstrap=True
        )
        
        lr = LogisticRegression(
            max_iter=2000,
            random_state=42,
            class_weight='balanced',
            C=0.5,
            solver='liblinear'
        )
        
        # 集成模型
        ensemble = VotingClassifier(
            estimators=[
                ('rf', rf),
                ('lr', lr)
            ],
            voting='soft'
        )
        
        # 交叉验证评估
        cv = StratifiedKFold(n_splits=min(5, len(np.unique(y))), shuffle=True, random_state=42)
        
        models = {
            'RandomForest': rf,
            'LogisticRegression': lr,
            'Ensemble': ensemble
        }
        
        results = {}
        
        for name, model in models.items():
            try:
                cv_scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
                model.fit(X, y)
                
                results[name] = {
                    'model': model,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'cv_scores': cv_scores
                }
                
                logger.info(f"{name}: CV准确率 = {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
                
            except Exception as e:
                logger.warning(f"{name} 训练失败: {e}")
                continue
        
        # 选择最佳模型
        if results:
            best_model_name = max(results.keys(), key=lambda k: results[k]['cv_mean'])
            self.model = results[best_model_name]['model']
            logger.info(f"选择最佳模型: {best_model_name}")
        
        return results
    
    def train(self, texts: List[str], labels: List[str]) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            texts: 文本列表
            labels: 标签列表
            
        Returns:
            训练结果
        """
        logger.info("开始训练高级错误分析模型...")
        logger.info(f"训练数据: {len(texts)} 条, 类别数: {len(set(labels))}")
        
        # 标签编码
        y = self.label_encoder.fit_transform(labels)
        self.label_mapping = {i: label for i, label in enumerate(self.label_encoder.classes_)}
        
        # 特征提取
        X, feature_names = self.create_advanced_features(texts)
        self.feature_names = feature_names
        
        logger.info(f"原始特征维度: {X.shape[1]}")
        
        # 数据平衡
        X_balanced, y_balanced = self.balance_data(X, y)
        
        # 训练集成模型
        model_results = self.train_ensemble_model(X_balanced, y_balanced)
        
        # 在原始数据上评估
        if self.model is not None:
            y_pred = self.model.predict(X)
            accuracy = accuracy_score(y, y_pred)
            
            logger.info(f"原始数据准确率: {accuracy:.3f}")
            
            # 分类报告
            report = classification_report(y, y_pred, target_names=self.label_encoder.classes_, zero_division=0)
            logger.info(f"分类报告:\n{report}")
        
        return {
            'model_results': model_results,
            'original_samples': len(texts),
            'balanced_samples': len(X_balanced),
            'feature_count': X.shape[1],
            'class_count': len(self.label_encoder.classes_),
            'label_mapping': self.label_mapping
        }
    
    def predict(self, text: str) -> Dict[str, Any]:
        """
        预测单条文本
        
        Args:
            text: 输入文本
            
        Returns:
            预测结果
        """
        if self.model is None:
            raise ValueError("模型未训练")
        
        # 特征提取
        X, _ = self.create_advanced_features([text])
        
        # 预测
        pred_proba = self.model.predict_proba(X[0:1])[0]
        pred_class = np.argmax(pred_proba)
        
        # 获取Top-5预测
        top_indices = np.argsort(pred_proba)[::-1][:5]
        top_predictions = [(self.label_mapping[i], pred_proba[i]) for i in top_indices]
        
        # 置信度评估
        confidence = pred_proba[pred_class]
        is_confident = confidence > 0.3  # 调整阈值
        
        return {
            'predicted_label': self.label_mapping[pred_class],
            'confidence': confidence,
            'is_confident': is_confident,
            'top_5_predictions': top_predictions,
            'prediction_explanation': self._explain_prediction(text, pred_class, pred_proba)
        }
    
    def _explain_prediction(self, text: str, pred_class: int, pred_proba: np.ndarray) -> Dict[str, Any]:
        """
        解释预测结果
        
        Args:
            text: 输入文本
            pred_class: 预测类别
            pred_proba: 预测概率
            
        Returns:
            解释信息
        """
        features = self.extract_semantic_features(text)
        
        # 找出激活的特征
        activated_patterns = []
        for pattern_name, score in features.items():
            if pattern_name.startswith('pattern_') and score > 0:
                activated_patterns.append((pattern_name.replace('pattern_', ''), score))
        
        activated_patterns.sort(key=lambda x: x[1], reverse=True)
        
        return {
            'predicted_class': self.label_mapping[pred_class],
            'confidence': pred_proba[pred_class],
            'activated_patterns': activated_patterns[:3],
            'key_indicators': self._find_key_indicators(text)
        }
    
    def _find_key_indicators(self, text: str) -> List[str]:
        """
        找出关键指示词
        
        Args:
            text: 输入文本
            
        Returns:
            关键指示词列表
        """
        text_lower = text.lower()
        indicators = []
        
        for keyword, weight in self.keyword_weights.items():
            if keyword.lower() in text_lower and weight >= 3.0:
                indicators.append(keyword)
        
        return indicators
    
    def save_model(self, path: str):
        """保存模型"""
        os.makedirs(path, exist_ok=True)
        
        # 保存模型组件
        joblib.dump(self.model, os.path.join(path, 'model.pkl'))
        joblib.dump(self.vectorizer, os.path.join(path, 'vectorizer.pkl'))
        joblib.dump(self.label_encoder, os.path.join(path, 'label_encoder.pkl'))
        joblib.dump(self.scaler, os.path.join(path, 'scaler.pkl'))
        
        # 保存配置
        config = {
            'feature_names': self.feature_names,
            'label_mapping': self.label_mapping,
            'error_patterns': self.error_patterns,
            'keyword_weights': self.keyword_weights
        }
        
        with open(os.path.join(path, 'config.json'), 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"高级模型已保存到: {path}")

def main():
    """主函数"""
    # 读取数据
    df = pd.read_excel('processed_data.xlsx')
    texts = df['响应内容'].astype(str).tolist()
    labels = df['报错原因'].astype(str).tolist()
    
    # 创建和训练模型
    analyzer = AdvancedErrorAnalyzer()
    results = analyzer.train(texts, labels)
    
    # 保存模型
    analyzer.save_model('models/advanced_classifier')
    
    # 测试预测
    test_texts = [
        "Did not observe any item or terminal signal within 600000ms in 'peek'",
        "Authentication failed: invalid token",
        "JSON parse error in arguments field",
        "Model name not found in request",
        "Connection timeout after 30 seconds",
        "请求体中模型名称错误",
        "大模型鉴权未通过"
    ]
    
    logger.info("\n高级模型测试预测:")
    for text in test_texts:
        try:
            result = analyzer.predict(text)
            logger.info(f"文本: {text}")
            logger.info(f"预测: {result['predicted_label']} (置信度: {result['confidence']:.3f})")
            logger.info(f"可信度: {'是' if result['is_confident'] else '否'}")
            logger.info(f"Top3: {result['top_5_predictions'][:3]}")
            if result['prediction_explanation']['activated_patterns']:
                logger.info(f"激活模式: {result['prediction_explanation']['activated_patterns']}")
            logger.info("-" * 60)
        except Exception as e:
            logger.error(f"预测失败: {e}")

if __name__ == "__main__":
    main()
