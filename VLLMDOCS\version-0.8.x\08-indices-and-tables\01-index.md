---
title: 索引
---

## A

| [abort() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.abort)                                            | [abort_request() (vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.abort_request) |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------- |
| [add_request() (vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.add_request)                                                | [AsyncLLMEngine (class in vllm)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine)             |
| [audio (vllm.multimodal.MultiModalDataBuiltins attribute)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalDataBuiltins.audio) |                                                                                                                                   |

## B

| [batch() (vllm.multimodal.MultiModalInputs static method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalInputs.batch) | [BatchedTensorInputs (in module vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.BatchedTensorInputs) |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [beam_search() (vllm.LLM method)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm.html#vllm.LLM.beam_search)                                                  |                                                                                                                                                            |

## C

| [chat() (vllm.LLM method)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm.html#vllm.LLM.chat)                                                                                                       | [check_health() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.check_health)                                                 |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [clone() (vllm.SamplingParams method)](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams.clone)                                                                                     | [create_input_mapper() (vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.create_input_mapper) |
| [create_input_processor() (vllm.inputs.registry.InputRegistry method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputRegistry.create_input_processor) |                                                                                                                                                                                                 |

## D

| [DEPRECATE_LEGACY (vllm.LLM attribute)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm.html#vllm.LLM.DEPRECATE_LEGACY)                                      | [do_log_stats() (vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.do_log_stats)                                                                                   |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [DO_VALIDATE_OUTPUT (vllm.LLMEngine attribute)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.DO_VALIDATE_OUTPUT)                          | [dummy_data_for_profiling() (vllm.inputs.registry.InputRegistry method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputRegistry.dummy_data_for_profiling) |
| [DummyDataFactory (class in vllm.inputs.registry)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.DummyDataFactory) |                                                                                                                                                                                                                   |

## E

| [encode() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.encode)           | [(vllm.LLM method)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm.html#vllm.LLM.encode) |
| :-------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------- |
| [engine_step() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.engine_step) |                                                                                                    |

## F

| [from_engine_args() (vllm.AsyncLLMEngine class method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.from_engine_args) | [(vllm.LLMEngine class method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.from_engine_args) |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------ | :------------------------------------------------------------------------------------------------------------------------- |

## G

| [generate() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.generate)                                                                   | [(vllm.LLM method)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm.html#vllm.LLM.generate)                                                                                                                      |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| [get_data_key() (vllm.multimodal.image.ImagePlugin method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.image.ImagePlugin.get_data_key)                           | [(vllm.multimodal.MultiModalPlugin method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin.get_data_key)                                                            |
| [get_decoding_config() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.get_decoding_config)                                             | [(vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.get_decoding_config)                                                                                                   |
| [get_hf_config() (vllm.inputs.registry.InputContext method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputContext.get_hf_config)                 | [get_hf_image_processor_config() (vllm.inputs.registry.InputContext method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputContext.get_hf_image_processor_config) |
| [get_lora_config() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.get_lora_config)                                                     | [(vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.get_lora_config)                                                                                                       |
| [get_max_multimodal_tokens() (vllm.multimodal.MultiModalPlugin method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin.get_max_multimodal_tokens)   | [(vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.get_max_multimodal_tokens)                                           |
| [get_mm_limits_per_prompt() (vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.get_mm_limits_per_prompt) | [get_model_config() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.get_model_config)                                                                   |
| [(vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.get_model_config)                                                                                      | [get_num_unfinished_requests() (vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.get_num_unfinished_requests)                                                             |
| [get_parallel_config() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.get_parallel_config)                                             | [(vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.get_parallel_config)                                                                                                   |
| [get_scheduler_config() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.get_scheduler_config)                                           | [(vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.get_scheduler_config)                                                                                                  |

## H

| [has_unfinished_requests() (vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.has_unfinished_requests) | [has_unfinished_requests_for_virtual_engine() (vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.has_unfinished_requests_for_virtual_engine) |
| :---------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |

## I

| [image (vllm.multimodal.MultiModalDataBuiltins attribute)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalDataBuiltins.image)                                | [ImagePlugin (class in vllm.multimodal.image)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.image.ImagePlugin)              |
| :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [init_mm_limits_per_prompt() (vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.init_mm_limits_per_prompt) | [INPUT_REGISTRY (in module vllm.inputs)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.INPUT_REGISTRY)                   |
| [InputContext (class in vllm.inputs.registry)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputContext)                                               | [InputProcessor (in module vllm.inputs.registry)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputProcessor) |
| [InputRegistry (class in vllm.inputs.registry)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputRegistry)                                             |                                                                                                                                                                    |

## L

| [LLM (class in vllm)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm.html#vllm.LLM)                                         | [LLMEngine (class in vllm)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine) |
| :------------------------------------------------------------------------------------------------------------------------------------ | :---------------------------------------------------------------------------------------------------- |
| [LLMInputs (class in vllm.inputs)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.LLMInputs) |                                                                                                       |

## M

| [map_input() (vllm.multimodal.MultiModalPlugin method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin.map_input)                  | [(vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.map_input) |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [model_config (vllm.inputs.registry.InputContext attribute)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputContext.model_config) | module                                                                                                                                                          |
| [vllm.engine](https://docs.vllm.ai/en/latest/dev/engine/engine_index.html#module-vllm.engine)                                                                                            | [vllm.inputs.registry](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#module-vllm.inputs.registry)                                 |
| [vllm.multimodal](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#module-vllm.multimodal)                                                                            | [vllm.multimodal.image](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#module-vllm.multimodal.image)                                       |
| [multi_modal_data (vllm.inputs.LLMInputs attribute)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.LLMInputs.multi_modal_data)                 | [(vllm.inputs.TextPrompt attribute)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TextPrompt.multi_modal_data)              |
| [(vllm.inputs.TokensPrompt attribute)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TokensPrompt.multi_modal_data)                                   | [MULTIMODAL_REGISTRY (in module vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MULTIMODAL_REGISTRY)      |
| [MultiModalDataBuiltins (class in vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalDataBuiltins)                          | [MultiModalDataDict (in module vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalDataDict)        |
| [MultiModalInputs (class in vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalInputs)                                      | [MultiModalPlugin (class in vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin)             |
| [MultiModalRegistry (class in vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry)                                  |                                                                                                                                                                 |

## N

| [NestedTensors (in module vllm.multimodal)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.NestedTensors) |     |
| :--------------------------------------------------------------------------------------------------------------------------------------------- | :-- |

## P

| [process_input() (vllm.inputs.registry.InputRegistry method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputRegistry.process_input) | [prompt (vllm.inputs.LLMInputs attribute)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.LLMInputs.prompt)                     |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [(vllm.inputs.TextPrompt attribute)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TextPrompt.prompt)                                                    | [prompt_token_ids (vllm.inputs.LLMInputs attribute)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.LLMInputs.prompt_token_ids) |
| [(vllm.inputs.TokensPrompt attribute)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TokensPrompt.prompt_token_ids)                                      | [PromptType (in module vllm.inputs)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.PromptType)                                        |

## R

| [register_dummy_data() (vllm.inputs.registry.InputRegistry method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputRegistry.register_dummy_data)           | [register_image_input_mapper() (vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.register_image_input_mapper) |
| :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [register_input_mapper() (vllm.multimodal.MultiModalPlugin method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin.register_input_mapper)                   | [(vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.register_input_mapper)                                     |
| [register_input_processor() (vllm.inputs.registry.InputRegistry method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputRegistry.register_input_processor) | [register_max_image_tokens() (vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.register_max_image_tokens)     |
| [register_max_multimodal_tokens() (vllm.multimodal.MultiModalPlugin method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin.register_max_multimodal_tokens) | [(vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.register_max_multimodal_tokens)                            |
| [register_plugin() (vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.register_plugin)                           | [run_engine_loop() (vllm.AsyncLLMEngine static method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.run_engine_loop)                                                    |

## S

| [SamplingParams (class in vllm)](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams)                                                     | [shutdown_background_loop() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.shutdown_background_loop) |
| :---------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [start_background_loop() (vllm.AsyncLLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.start_background_loop) | [step() (vllm.LLMEngine method)](https://docs.vllm.ai/en/latest/dev/engine/llm_engine.html#vllm.LLMEngine.step)                                                         |

## T

| [TextPrompt (class in vllm.inputs)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TextPrompt) | [TokensPrompt (class in vllm.inputs)](https://docs.vllm.ai/en/latest/dev/offline_inference/llm_inputs.html#vllm.inputs.TokensPrompt) |
| :------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------- |

## U

| [update_from_generation_config() (vllm.SamplingParams method)](https://docs.vllm.ai/en/latest/dev/sampling_params.html#vllm.SamplingParams.update_from_generation_config) |     |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :-- |

## V

vllm.engine

- [module](https://docs.vllm.ai/en/latest/dev/engine/engine_index.html#module-vllm.engine)

vllm.inputs.registry

- [module](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#module-vllm.inputs.registry)

vllm.multimodal

- [module](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#module-vllm.multimodal)

vllm.multimodal.image

- [module](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#module-vllm.multimodal.image)

##
