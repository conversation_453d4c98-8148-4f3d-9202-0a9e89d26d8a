# 文档路径: 01-getting-started > 05-installation-with-neuron > 使用 Neuron 安装 > 依赖环境

从 vLLM 0.3.3 版本起，支持在带有 Neuron SDK 的 AWS Trainium/Inferentia 上进行模型推理和服务。目前 Neuron SDK 不支持分页注意力 (Paged Attention)，但 Transformers-neuronx 支持简单的连续批处理。Neuron SDK 目前支持的数据类型为 FP16 和 BF16。

## 依赖环境


- 操作系统：Linux
- Python：3.8 -- 3.11
- 加速器：NeuronCore_v2（在 trn1/inf2 实例中）
- Pytorch 2.0.1/2.1.1
- AWS Neuron SDK 2.16/2.17（在 python 3.8 上验证）

安装步骤:

-  [从源代码构建](#从源代码构建)

  - [步骤 0. 启动 Trn1/Inf2 实例](#步骤-0-启动-trn1inf2-实例)

  - [步骤 1. 安装驱动程序和工具](#步骤-1-安装驱动程序和工具)

  - [步骤 2. 安装 Transformers-neuronx 及其依赖](#步骤-2-安装-transformers-neuronx-及其依赖)

  - [步骤 3. 从源代码安装 vLLM](#步骤-3-从源代码安装-vllm)
