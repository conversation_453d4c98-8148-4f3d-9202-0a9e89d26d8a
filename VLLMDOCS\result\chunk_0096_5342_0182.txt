# 文档路径: 03-models > 06-using-vlms > 使用 VLM > 离线推理 > 单图像输入

## 离线推理


### 单图像输入

`LLM` 类的实例化过程与语言模型的实例化方式大致相同。

```python
llm = LLM(model="llava-hf/llava-1.5-7b-hf")
```

要将图像传递给模型，请注意 `vllm.inputs.PromptInputs` 中的以下内容:

- `prompt`: 提示应遵循 HuggingFace 中记录的格式。
- `multi_modal_data`: 这是一个字典，它遵循 `vllm.multimodal.MultiModalDataDict` 中定义的模式。

```python
# Refer to the HuggingFace repo for the correct format to use
# 请参阅 HuggingFace 存储库以了解要使用的正确格式


prompt = "USER: <image>\nWhat is the content of this image?\nASSISTANT:"


# Load the image using PIL.Image
# 使用 PIL.Image 加载图像


image = PIL.Image.open(...)


# Single prompt inference
# 单提示推理


outputs = llm.generate({
    "prompt": prompt,
    "multi_modal_data": {"image": image},
})


for o in outputs:
    generated_text = o.outputs[0].text
    print(generated_text)


# Inference with image embeddings as input
# 以图像嵌入作为输入进行推理


image_embeds = torch.load(...) # torch.Tensor of shape (1, image_feature_size, hidden_size of LM)


image_embeds = torch.load(...) # torch.Tensor 形状为 (1, image_feature_size, hide_size of LM)


outputs = llm.generate({
    "prompt": prompt,
    "multi_modal_data": {"image": image_embeds},
})


for o in outputs:
    generated_text = o.outputs[0].text
    print(generated_text)


# Batch inference
# 批量推理


image_1 = PIL.Image.open(...)
image_2 = PIL.Image.open(...)
outputs = llm.generate(
    [
        {
            "prompt": "USER: <image>\nWhat is the content of this image?\nASSISTANT:",
            "multi_modal_data": {"image": image_1},
        },
        {
            "prompt": "USER: <image>\nWhat's the color of this image?\nASSISTANT:",
            "multi_modal_data": {"image": image_2},
        }
    ]
)


for o in outputs:
    generated_text = o.outputs[0].text
    print(generated_text)
```

代码示例可以在 [examples/offline_inference_vision_language.py](https://github.com/vllm-project/vllm/blob/main/examples/offline_inference_vision_language.py) 中找到。
