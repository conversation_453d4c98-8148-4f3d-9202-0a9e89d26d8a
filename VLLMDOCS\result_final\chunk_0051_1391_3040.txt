# 文档路径: 03-models > 01-supported-models > 支持的模型 > 模型支持政策

# 模型支持政策


在 vLLM 中，我们致力于促进第三方模型在我们的生态系统中的集成和支持。我们的方法旨在平衡对鲁棒性的需求和支持各种模型范围的实际限制。以下是我们管理第三方模型支持的方式：

1. **社区驱动的支持\*\***：\*\*我们倡导并鼓励社区成员积极参与，引入新模型。每当用户提出对新模型的支持需求时，我们都非常期待来自社区的拉取请求 (PRs)。在评估这些贡献时，我们主要关注它们产生的输出的合理性，而不是它们与现有实现（例如在 transformers 中的实现）的严格一致性。 贡献号召：非常感谢直接来自于模型供应商的 PR！

2. **尽力保持一致性\*\***：\*\*虽然我们努力保持 vLLM 中实现的模型与 transformers 等其他框架之间的一致性，但并不总是能够完全对齐。加速技术的使用和低精度计算等因素可能会引入差异。我们承诺确保所实现的模型功能正常并产生合理的结果。

3. **问题解决和模型更新\*\***：\*\*我们鼓励用户报告在使用第三方模型时遇到的任何错误或问题。建议通过拉取请求（PRs）提交修复方案，并清楚地说明问题所在以及提出解决方案的理由。如果一个模型的修复影响另一个模型，我们依靠社区来突出和解决这些跨模型依赖关系。注意：对于 bug 修复 PR，通知原作者并获得他们的反馈是一种良好的礼仪。

4. **监控和更新\*\***：\*\*对特定模型感兴趣的用户建议监控这些模型的提交历史记录 （例如，通过跟踪 main/vllm/model_executor/models 目录中的更改）。这种主动方法可以帮助用户随时了解可能影响他们使用的模型的更新和更改。

5. **有选择的关注\*\***：\*\*我们的资源主要集中在那些受到广泛用户关注和具有较大影响力的模型上。对于那些使用频率不高的模型，我们可能无法投入同样多的精力，因此我们依赖社区在这些模型的维护和提升中扮演更加积极的角色。

通过这种方法，vLLM 营造了一个协作环境，核心开发团队和更广泛的社区都为我们生态系统中支持的第三方模型的稳健性和多样性做出了贡献。

请注意，作为推理引擎，vLLM 并没有引入新模型。因此，vLLM 支持的所有模型都是第三方模型。

我们对模型进行以下级别的测试：

1. **严格一致性**：我们将模型的输出与 HuggingFace Transformers 库中模型在贪婪解码下的输出进行比较。这是最严格的测试。请参考[模型测试](https://github.com/vllm-project/vllm/blob/main/tests/models)，了解哪些模型通过了此测试。

2. **输出合理性**：我们通过测量输出的困惑度 (perplexity) 并检查是否存在明显错误，来判断模型输出是否合理和连贯。这是一个较为宽松的测试。

3. **运行时功能**：我们检查模型是否能够无错误地加载并运行。这是最不严格的测试。请参考已通过此测试的模型的[功能测试](https://github.com/vllm-project/vllm/tree/main/tests)和[示例](https://github.com/vllm-project/vllm/tree/main/examples)。

4. **社区反馈**：我们依赖社区对模型提供反馈。如果某个模型出现问题或未按预期工作，我们鼓励用户提出问题报告或提交拉取请求 (PRs) 以修复问题。其余模型属于此类。
