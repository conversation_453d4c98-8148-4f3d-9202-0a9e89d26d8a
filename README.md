# 应用服务报错根因分析模型

## 项目概述

本项目是一个基于深度学习的应用服务报错根因分析系统，能够自动分析应用服务的错误响应内容，并预测可能的报错原因。系统采用先进的Transformer架构结合手工特征工程，针对小样本学习场景进行了优化。

## 技术栈

### 核心技术
- **深度学习框架**: PyTorch 2.7+
- **预训练模型**: BERT (bert-base-chinese)
- **自然语言处理**: Transformers, Tokenizers
- **机器学习**: Scikit-learn
- **数据处理**: Pandas, NumPy
- **中文处理**: Jieba分词

### 开发工具
- **Python版本**: 3.12+
- **包管理**: UV (现代Python包管理器)
- **数据可视化**: Matplotlib, Seaborn, Plotly
- **日志记录**: Python logging
- **配置管理**: JSON配置文件

### 部署支持
- **容器化**: 支持Docker部署
- **设备支持**: CPU/GPU自适应
- **并发处理**: 支持批量预测
- **API接口**: 命令行和Python API

## 业务逻辑

### 问题背景
在现代微服务架构中，应用服务经常出现各种错误，运维人员需要快速定位错误原因。传统的人工分析方式效率低下，且容易遗漏关键信息。本系统通过机器学习自动化这一过程。

### 核心功能
1. **错误文本分析**: 解析应用服务的错误响应内容
2. **根因预测**: 基于历史数据预测最可能的报错原因
3. **置信度评估**: 提供预测结果的可信度评分
4. **解释性分析**: 解释预测依据，提供关键特征和错误指示词
5. **批量处理**: 支持大规模错误日志的批量分析

### 应用场景
- **运维监控**: 自动化错误日志分析
- **故障诊断**: 快速定位系统问题根因
- **知识积累**: 建立错误模式知识库
- **预警系统**: 基于错误模式进行预警

## 架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据输入层    │    │   核心处理层    │    │   输出服务层    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Excel/CSV文件 │    │ • 数据预处理    │    │ • 预测结果      │
│ • 文本输入      │───▶│ • 模型训练      │───▶│ • 置信度评估    │
│ • API接口       │    │ • 预测推理      │    │ • 解释性分析    │
│ • 批量数据      │    │ • 特征提取      │    │ • 批量输出      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术架构
```
┌─────────────────────────────────────────────────────────────┐
│                        应用层                               │
├─────────────────┬─────────────────┬─────────────────────────┤
│   训练脚本      │   预测服务      │      工具脚本           │
│ train_model.py  │predict_service.py│  data_augmentation.py  │
└─────────────────┴─────────────────┴─────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        核心层                               │
├─────────────────┬─────────────────┬─────────────────────────┤
│  数据预处理模块 │   模型训练模块  │     预测推理模块        │
│DataProcessor    │ ModelTrainer    │     Predictor           │
└─────────────────┴─────────────────┴─────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        基础层                               │
├─────────────────┬─────────────────┬─────────────────────────┤
│   深度学习      │   机器学习      │      数据处理           │
│  PyTorch        │  Scikit-learn   │     Pandas/NumPy        │
│  Transformers   │  特征工程       │     文本处理            │
└─────────────────┴─────────────────┴─────────────────────────┘
```

## 模块设计

### 1. 数据预处理模块 (core/data_processor.py)

**主要功能**:
- 文本清洗和标准化
- 错误特征提取
- 中英文混合文本处理
- 标签编码和解码

**核心类**:
```python
class DataProcessor:
    def clean_text(text: str) -> str
    def extract_error_features(text: str) -> Dict[str, int]
    def process_dataset(texts: List[str], labels: List[str]) -> Tuple
```

**特征工程**:
- 错误关键词统计 (连接、超时、认证等10大类)
- 文本长度和词汇数量
- 数字特征 (错误码、时间等)
- 格式特征 (JSON、URL、IP等)

### 2. 模型训练模块 (core/model_trainer.py)

**主要功能**:
- 混合神经网络模型构建
- 小样本学习优化
- 模型训练和验证
- 模型保存和加载

**核心类**:
```python
class HybridErrorClassifier(nn.Module):
    # BERT文本编码 + 手工特征融合

class ModelTrainer:
    def train(texts, labels, features) -> Dict
    def save_model(save_path: str)
    def load_model(load_path: str)
```

**模型特点**:
- **混合架构**: BERT文本编码 + 手工特征
- **类别平衡**: 自动计算类别权重
- **小样本优化**: 适应数据稀少场景
- **梯度裁剪**: 防止梯度爆炸

### 3. 预测推理模块 (core/predictor.py)

**主要功能**:
- 单条和批量预测
- 置信度评估
- 解释性分析
- 结果可视化

**核心类**:
```python
class Predictor:
    def predict_single(text: str) -> Dict[str, Any]
    def predict_batch(texts: List[str]) -> List[Dict]
    def explain_prediction(text: str, result: Dict) -> Dict
```

**预测特性**:
- **多级预测**: Top-K预测结果
- **置信度阈值**: 可配置的可信度判断
- **特征解释**: 关键特征和错误指示词分析
- **性能监控**: 推理时间统计

## 部署设计

### 环境要求

**硬件要求**:
- CPU: 4核心以上 (推荐8核心)
- 内存: 8GB以上 (推荐16GB)
- 存储: 10GB可用空间
- GPU: 可选 (NVIDIA GPU with CUDA support)

**软件要求**:
- Python 3.12+
- UV包管理器
- 操作系统: Windows/Linux/macOS

### 安装部署

1. **克隆项目**:
```bash
git clone <repository-url>
cd logmodel
```

2. **环境初始化**:
```bash
# 安装UV (如果未安装)
pip install uv

# 安装项目依赖
uv sync

# 运行环境设置脚本
uv run python scripts/setup.py
```

3. **数据准备**:
```bash
# 将训练数据放置在根目录
# 文件格式: Excel (.xlsx) 或 CSV (.csv)
# 必需列: "响应内容", "报错原因"
```

4. **模型训练**:
```bash
# 使用默认配置训练
uv run python train_model.py --data source.xlsx --output models/error_classifier

# 使用自定义配置
uv run python train_model.py --data source.xlsx --config config/model_config.json --evaluate
```

5. **预测服务**:
```bash
# 单条预测
uv run python predict_service.py --model models/error_classifier --mode single --text "HTTP 500 Error"

# 批量预测
uv run python predict_service.py --model models/error_classifier --mode file --input data.xlsx --output results.xlsx
```

### Docker部署

**Dockerfile示例**:
```dockerfile
FROM python:3.12-slim

WORKDIR /app
COPY . .

RUN pip install uv
RUN uv sync

EXPOSE 8000
CMD ["uv", "run", "python", "predict_service.py", "--model", "models/error_classifier"]
```

**构建和运行**:
```bash
docker build -t logmodel .
docker run -p 8000:8000 -v $(pwd)/models:/app/models logmodel
```

## 并发设计

### 批量处理优化

**数据并行**:
- 支持批量数据加载和预处理
- 自动批次分割，避免内存溢出
- 进度监控和日志记录

**模型推理优化**:
- 批量推理减少模型加载开销
- GPU内存管理和优化
- 异步处理支持

**性能监控**:
```python
# 批量预测性能统计
results = predictor.predict_batch(texts, batch_size=32)
confidence_analysis = predictor.analyze_prediction_confidence(results)
```

### 扩展性设计

**水平扩展**:
- 支持多进程并行处理
- 模型服务化部署
- 负载均衡支持

**垂直扩展**:
- GPU加速支持
- 内存优化策略
- 模型压缩技术

## 使用指南

### 快速开始

1. **数据分析**:
```bash
# 分析数据质量和分布
uv run python data_process.py
```

2. **数据增强** (可选):
```bash
# 针对小样本场景进行数据增强
uv run python scripts/data_augmentation.py --input processed_data.xlsx --output augmented_data.xlsx --target-samples 20
```

3. **模型训练**:
```bash
# 快速训练
uv run python scripts/quick_train.py
```

4. **模型预测**:
```bash
# 快速预测
uv run python scripts/quick_predict.py
```

### 高级用法

**自定义配置**:
```json
{
  "model_name": "bert-base-chinese",
  "max_length": 512,
  "batch_size": 16,
  "learning_rate": 2e-5,
  "num_epochs": 20,
  "confidence_threshold": 0.7
}
```

**API调用**:
```python
from core.predictor import Predictor

# 创建预测器
predictor = Predictor("models/error_classifier")

# 单条预测
result = predictor.predict_single("HTTP 500 Internal Server Error", explain=True)
print(f"预测结果: {result['predicted_label']}")
print(f"置信度: {result['confidence']:.3f}")

# 批量预测
results = predictor.predict_batch(texts, return_probabilities=True)
```

## 性能指标

### 模型性能
- **准确率**: 根据数据质量和数量而定
- **推理速度**: CPU ~100ms/条, GPU ~10ms/条
- **内存占用**: ~2GB (模型加载后)
- **支持类别**: 理论上无限制，实际建议<100类

### 数据要求
- **最小样本**: 每类至少2个样本
- **推荐样本**: 每类20+个样本
- **文本长度**: 5-10000字符
- **支持语言**: 中英文混合

## 故障排除

### 常见问题

1. **内存不足**:
   - 减小batch_size
   - 使用CPU模式
   - 清理无用变量

2. **训练效果差**:
   - 增加训练数据
   - 调整学习率
   - 使用数据增强

3. **预测速度慢**:
   - 使用GPU加速
   - 增大batch_size
   - 模型量化优化

### 日志分析
```bash
# 查看训练日志
tail -f logs/training.log

# 查看预测日志
tail -f logs/prediction.log
```

## 贡献指南

### 开发环境
```bash
# 安装开发依赖
uv add --dev pytest black flake8 mypy

# 代码格式化
black .

# 类型检查
mypy core/

# 运行测试
pytest tests/
```

### 代码规范
- 遵循PEP 8编码规范
- 使用类型注解
- 编写完整的文档字符串
- 添加单元测试

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目维护者: LogModel Team
- 邮箱: <EMAIL>
- 问题反馈: [GitHub Issues](https://github.com/logmodel/issues)

## 更新日志

### v0.1.0 (2025-01-07)
- 初始版本发布
- 支持基础的错误分类功能
- 提供完整的训练和预测流程
- 包含数据增强和可解释性分析