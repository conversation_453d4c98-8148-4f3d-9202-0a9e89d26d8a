# 文档路径: 03-models > 06-using-vlms > 使用 VLM

vLLM 为视觉语言模型 (VLM) 提供实验性支持，可以参阅「支持的 VLM 列表」。本文档将向您展示如何使用 vLLM 运行并提供这些模型的服务。

**注意：**

我们正在积极改进对 VLM 的支持。预计在即将发布的版本中，VLM 的使用和开发会发生重大变化，但无需事先弃用。

We are continuously improving user & developer experience for VLMs. Please [open an issue on GitHub](https://github.com/vllm-project/vllm/issues/new/choose) if you have any feedback or feature requests.

我们不断改善 VLMs 的用户和开发人员体验。如果您有任何反馈或功能请求，请[访问 GitHub 并提出 issue](https://github.com/vllm-project/vllm/issues/new/choose)。