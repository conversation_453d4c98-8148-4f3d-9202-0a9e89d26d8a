{
  "混合规则+ML": {
    "train_accuracy": 0.9130434782608695,
    "test_accuracy": 0.5,
    "avg_confidence": 0.5272665063971231,
    "avg_inference_time": 0.0003000497817993164,
    "rule_coverage": 0.043478260869565216,
    "rule_accuracy": 1.0,
    "predictions": [
      "大模型请求超600s未返回",
      "大模型鉴权未通过",
      "tool_call中的arguments为非标准JSON体，大模型无法解析",
      "请求体中模型名称错误",
      "大模型600S还没回答完，导致超时关闭连接",
      "非标准JSON入参",
      "非标准JSON入参",
      "大模型鉴权未通过",
      "用户主动切断大模型回复",
      "请求体中content为数组"
    ],
    "confidences": [
      0.95,
      0.95,
      0.7,
      0.7,
      0.20471056263038875,
      0.20866220600007526,
      0.1925513558143222,
      0.28559841974577627,
      0.7,
      0.38114251978066993
    ],
    "methods": [
      "规则匹配",
      "规则匹配",
      "规则匹配",
      "规则匹配",
      "机器学习",
      "机器学习",
      "机器学习",
      "机器学习",
      "规则匹配",
      "机器学习"
    ],
    "details": {
      "rule_coverage": 0.043478260869565216,
      "rule_accuracy": 1.0,
      "hybrid_accuracy": 0.9130434782608695,
      "ml_results": {
        "RandomForest": {
          "model": 