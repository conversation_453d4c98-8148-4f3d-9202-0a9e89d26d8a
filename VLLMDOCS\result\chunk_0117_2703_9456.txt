# 文档路径: 04-quantization > 06-fp8-w8a8 > FP8 W8A8

vLLM 支持在 Nvidia H100 和 AMD MI300x 等 GPU 上利用硬件加速进行 FP8（8 位浮点数）的权重和激活量化。当前，仅 Hopper 和 Ada Lovelace GPU 对 W8A8 提供官方支持。对于 W8A16（仅权重为 FP8），可借助 Marlin 内核在 Ampere GPU 上得到支持。通过 FP8 对模型进行量化，能够使模型内存需求降低一半，吞吐量最多可提高 1.6 倍，同时对准确性的影响最小。

请访问 Hugging Face 上[可与 vLLM 一起使用的流行 LLM 的量化 FP8 检查点集合](https://huggingface.co/collections/neuralmagic/fp8-llms-for-vllm-666742ed2b78b7ac8df13127)。

硬件中通常支持的 FP8 类型有 2 种不同的表示形式，每种表示形式在不同的场景中都很有用：

- **E4M3**：由 1 位符号位、4 位指数位和 3 位尾数组成。它可以存储高达 +/-448 和 `nan` 的值。
- **E5M2**：由 1 位符号位、5 位指数位和 2 位尾数组成。它可以存储高达 +/-57344、+/- `inf` 和 `nan` 的值。增加动态范围的代价是存储值的精度较低。

**注意：**

FP8 计算支持计算能力大于 8.9 的 NVIDIA GPU (Ada Lovelace，Hopper)。FP8 模型将在计算能力大于 8.0 (Ampere) 的 GPU 上以仅权重为 W8A16 的形式运行，使用 FP8 Marlin。