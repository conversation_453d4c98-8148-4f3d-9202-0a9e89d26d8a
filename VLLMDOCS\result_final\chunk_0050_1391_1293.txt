# 文档路径: 03-models > 01-supported-models > 支持的模型 > 多模态语言模型 > 文本生成 > 多模态 Embedding

## 多模态语言模型


根据模型的不同，支持以下模态：

- 文本
- 图像
- 视频
- 音频


### 文本生成


| 架构                                   | 模型                         | 输入         | HF 模型案例                                                                               | [LoRA](https://docs.vllm.ai/en/latest/models/lora.html#lora) | [PP](https://docs.vllm.ai/en/latest/serving/distributed_serving.html#distributed-serving) |
| :------------------------------------- | :--------------------------- | :----------- | :---------------------------------------------------------------------------------------- | :----------------------------------------------------------- | :---------------------------------------------------------------------------------------- |
| Blip2ForConditionalGeneration          | BLIP-2                       | T + IE       | Salesforce/blip2-opt-2.7b, Salesforce/blip2-opt-6.7b, etc.                                |                                                              | ✅︎                                                                                       |
| ChameleonForConditionalGeneration      | Chameleon                    | T + I        | facebook/chameleon-7b etc.                                                                |                                                              | ✅︎                                                                                       |
| FuyuForCausalLM                        | Fuyu                         | T + I        | adept/fuyu-8b etc.                                                                        |                                                              | ✅︎                                                                                       |
| ChatGLMModel                           | GLM-4V                       | T + I        | THUDM/glm-4v-9b etc.                                                                      |                                                              | ✅︎                                                                                       |
| InternVLChatModel                      | InternVL2                    | T + IE+      | OpenGVLab/InternVL2-4B, OpenGVLab/InternVL2-8B, etc.                                      |                                                              | ✅︎                                                                                       |
| LlavaForConditionalGeneration          | LLaVA-1.5                    | T + IE+      | llava-hf/llava-1.5-7b-hf, llava-hf/llava-1.5-13b-hf, etc.                                 |                                                              | ✅︎                                                                                       |
| LlavaNextForConditionalGeneration      | LLaVA-NeXT                   | T + IE+      | llava-hf/llava-v1.6-mistral-7b-hf, llava-hf/llava-v1.6-vicuna-7b-hf, etc.                 |                                                              | ✅︎                                                                                       |
| LlavaNextVideoForConditionalGeneration | LLaVA-NeXT-Video             | T + V        | llava-hf/LLaVA-NeXT-Video-7B-hf, etc.                                                     |                                                              | ✅︎                                                                                       |
| LlavaOnevisionForConditionalGeneration | LLaVA-Onevision              | T + I+ + V   | llava-hf/llava-onevision-qwen2-7b-ov-hf, llava-hf/llava-onevision-qwen2-0.5b-ov-hf, etc.  |                                                              | ✅︎                                                                                       |
| MiniCPMV                               | MiniCPM-V                    | T + IE+      | openbmb/MiniCPM-V-2 (see note), openbmb/MiniCPM-Llama3-V-2_5, openbmb/MiniCPM-V-2_6, etc. | ✅︎                                                          | ✅︎                                                                                       |
| MllamaForConditionalGeneration         | Llama 3.2                    | T + I        | meta-llama/Llama-3.2-90B-Vision-Instruct, meta-llama/Llama-3.2-11B-Vision, etc.           |                                                              |                                                                                           |
| MolmoForCausalLM                       | Molmo                        | Image        | allenai/Molmo-7B-D-0924, allenai/Molmo-72B-0924, etc.                                     |                                                              | ✅︎                                                                                       |
| NVLM_D_Model                           | NVLM-D 1.0                   | T + IE+      | nvidia/NVLM-D-72B, etc.                                                                   |                                                              | ✅︎                                                                                       |
| PaliGemmaForConditionalGeneration      | PaliGemma                    | T + IE       | google/paligemma-3b-pt-224, google/paligemma-3b-mix-224, etc.                             |                                                              | ✅︎                                                                                       |
| Phi3VForCausalLM                       | Phi-3-Vision, Phi-3.5-Vision | T + IE+      | microsoft/Phi-3-vision-128k-instruct, microsoft/Phi-3.5-vision-instruct etc.              |                                                              | ✅︎                                                                                       |
| PixtralForConditionalGeneration        | Pixtral                      | T + I+       | mistralai/Pixtral-12B-2409                                                                |                                                              | ✅︎                                                                                       |
| QWenLMHeadModel                        | Qwen-VL                      | T + IE+      | Qwen/Qwen-VL, Qwen/Qwen-VL-Chat, etc.                                                     |                                                              | ✅︎                                                                                       |
| Qwen2VLForConditionalGeneration        | Qwen2-VL                     | T + IE+ + V+ | Qwen/Qwen2-VL-2B-Instruct, Qwen/Qwen2-VL-7B-Instruct, Qwen/Qwen2-VL-72B-Instruct, etc.    |                                                              | ✅︎                                                                                       |
| UltravoxModel                          | Ultravox                     | T + AE+      | fixie-ai/ultravox-v0_3                                                                    |                                                              | ✅︎                                                                                       |

E 预计算的嵌入可以作为此模态的输入。

- 对于此模态，每个文本提示可以输入多个项目。

**注意**

对于 `openbmb/MiniCPM-V-2` ，官方仓库还不能工作，所以我们现在需要使用一个分支版本 （`HwwwH/MiniCPM-V-2`）。更多详情请参见: [https://github.com/vllm-project/vllm/pull/4087#issuecomment-2250397630](https://github.com/vllm-project/vllm/pull/4087#issuecomment-2250397630)


### 多模态 Embedding


| 架构             | 模型               | 输入  | HF 模型案例            | [LoRA](https://docs.vllm.ai/en/latest/models/lora.html#lora) | [PP](https://docs.vllm.ai/en/latest/serving/distributed_serving.html#distributed-serving) |
| :--------------- | :----------------- | :---- | :--------------------- | :----------------------------------------------------------- | :---------------------------------------------------------------------------------------- |
| Phi3VForCausalLM | Phi-3-Vision-based | T + I | TIGER-Lab/VLM2Vec-Full | 🚧                                                           | ✅︎                                                                                       |

**注意：**

有些模型架构同时支持生成和嵌入任务。在这种情况下，你需要传入 `--task embedding` 参数，才能以嵌入模式运行该模型。

如果您的模型使用上述模型架构之一，您可以使用 vLLM 无缝运行您的模型。否则，请参阅 `添加新模型 <adding_a_new_model>` 和 `启用多模式输入 <enabling_multimodal_inputs>` 中的说明了解如何为您的模型提供支持。或者，您可以在我们的 [GitHub](https://github.com/vllm-project/vllm/issues) 项目上提出问题。

**提示：**

要确认您的模型是否得到支持，最简单的方法是执行以下程序：

```python
from vllm import LLM


llm = LLM(model=...)  # Name or path of your model


llm = LLM(model=...) # 模型的名称或路径


output = llm.generate("Hello, my name is")
print(output)
```

如果 vLLM 成功生成文本，则表明您的模型受支持。

**提示：**

要使用 [ModelScope](https://www.modelscope.cn) 中的模型而不是 HuggingFace Hub 的模型，请设置一个环境变量：

```plain
export VLLM_USE_MODELSCOPE=True
```

并与 `trust_remote_code=True` 一起使用。

```python
from vllm import LLM


llm = LLM(model=..., revision=..., trust_remote_code=True)  # Name or path of your model


llm = LLM(model=..., revision=..., trust_remote_code=True) # 模型的名称或路径


output = llm.generate("Hello, my name is")
print(output)
```
