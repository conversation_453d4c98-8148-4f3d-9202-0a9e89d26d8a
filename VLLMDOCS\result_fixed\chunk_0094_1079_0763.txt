# 文档路径: 04-quantization > 05-int8-w8a8 > INT8 W8A8 > 量化过程 > 4. 评估准确性

## 量化过程

### 4. 评估准确性


量化后，您可以在 vLLM 中加载并运行模型：

```python
from vllm import LLM
model = LLM("./Meta-Llama-3-8B-Instruct-W8A8-Dynamic-Per-Token")
```

如需评估准确性，您可以使用 `lm_eval`：

```plain
lm_eval --model vllm \
  --model_args pretrained="./Meta-Llama-3-8B-Instruct-W8A8-Dynamic-Per-Token",add_bos_token=true \
  --tasks gsm8k \
  --num_fewshot 5 \
  --limit 250 \
  --batch_size 'auto'
```

**注意：**
量化模型可能对 `bos` token 的存在很敏感。在运行评估时，请确保包含 `add_bos_token=True` 这个参数。
