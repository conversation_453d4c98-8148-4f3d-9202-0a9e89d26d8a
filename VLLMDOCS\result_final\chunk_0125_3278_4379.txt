# 文档路径: 08-indices-and-tables > 01-index > 索引 > R

## R


| [register_dummy_data() (vllm.inputs.registry.InputRegistry method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputRegistry.register_dummy_data)           | [register_image_input_mapper() (vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.register_image_input_mapper) |
| :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [register_input_mapper() (vllm.multimodal.MultiModalPlugin method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin.register_input_mapper)                   | [(vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.register_input_mapper)                                     |
| [register_input_processor() (vllm.inputs.registry.InputRegistry method)](https://docs.vllm.ai/en/latest/dev/input_processing/model_inputs_index.html#vllm.inputs.registry.InputRegistry.register_input_processor) | [register_max_image_tokens() (vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.register_max_image_tokens)     |
| [register_max_multimodal_tokens() (vllm.multimodal.MultiModalPlugin method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalPlugin.register_max_multimodal_tokens) | [(vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.register_max_multimodal_tokens)                            |
| [register_plugin() (vllm.multimodal.MultiModalRegistry method)](https://docs.vllm.ai/en/latest/dev/multimodal/multimodal_index.html#vllm.multimodal.MultiModalRegistry.register_plugin)                           | [run_engine_loop() (vllm.AsyncLLMEngine static method)](https://docs.vllm.ai/en/latest/dev/engine/async_llm_engine.html#vllm.AsyncLLMEngine.run_engine_loop)                                                    |
