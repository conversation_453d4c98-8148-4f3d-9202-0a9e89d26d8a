# 文档路径: 10-tutorials > 03-few-shot-w-qwen2-5 > 使用 vLLM 加载 AWQ 量化 Qwen2.5-3B-Instruct 进行少样本学习 (Few shot) > 3. 使用 vLLM 启动 Qwen2.5-3B-Instruct-AWQ

## 3. 使用 vLLM 启动 Qwen2.5-3B-Instruct-AWQ


如果出现 OOM 错误，将 max_num_seqs减少到 4 或 8 甚至 1 可能会有所帮助（默认值为 256）。

```
llm = LLM(
    llm_model_pth,
    trust_remote_code=True,
    dtype="half", max_model_len=4096,
    tensor_parallel_size=1, gpu_memory_utilization=0.95,
)

tokenizer = llm.get_tokenizer()
```
