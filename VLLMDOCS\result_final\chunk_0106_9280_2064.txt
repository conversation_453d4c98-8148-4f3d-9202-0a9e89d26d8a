# 文档路径: 07-developer-documentation > 04-vllm-paged-attention > vLLM  分页注意力 > Softmax > `qk_max` 和 `logits` > `exp_sum`

## Softmax


- 接下来，我们需要计算所有 `qk` 的归一化 softmax，如上所示，其中每个 $$x$$ 代表一个`qk`。为此，我们必须获得所有 `qk` 的 `qk_max`(m(x)) 和 `exp_sum` ($$\ell(x)$$) 的归约值。归约操作需要在整个线程块上执行，包括查询 token 和所有上下文键 token 之间的结果。

$$\begin{gather*}      m(x):=\max _i \quad x_i \\ \quad f(x):=\left[\begin{array}{lll}e^{x_1-m(x)} & \ldots & e^{x_B-m(x)}\end{array}\right]\\ \quad \ell(x):=\sum_i f(x)_i \\      \quad \operatorname{softmax}(x):=\frac{f(x)}{\ell(x)}      \end{gather*}$$


### `qk_max` 和 `logits`


- 得到 `qk` 结果后，我们可以用 `qk` 设置临时 `logits` 结果 （最后，`logits` 应该存储归一化的 softmax 结果）。同时，我们还可以比较并收集当前线程组计算的所有 `qk` 的 `qk_max`。

```plain
    if (thread_group_offset == 0) {
       const bool mask = token_idx >= context_len;
       logits[token_idx - start_token_idx] = mask ? 0.f : qk;
       qk_max = mask ? qk_max : fmaxf(qk_max, qk);
    }
```

- 请注意，这里的 `logits` 存储在共享内存上，因此每个线程组将为其分配的上下文 token 设置字段。总的来说，logits 的大小应该是上下文 token 的数量。

```python
    for (int mask = WARP_SIZE / 2; mask >= THREAD_GROUP_SIZE; mask /= 2) {
        qk_max = fmaxf(qk_max, VLLM_SHFL_XOR_SYNC(qk_max, mask));
    }


    if (lane == 0) {
       red_smem[warp_idx] = qk_max;
    }
```

- 然后我们需要获得每个 warp 上减少的 `qk_max` 。主要思想是让 warp 中的线程相互通信并获得最终的 `qk` 最大值 。

```plain
    for (int mask = NUM_WARPS / 2; mask >= 1; mask /= 2) {
        qk_max = fmaxf(qk_max, VLLM_SHFL_XOR_SYNC(qk_max, mask));
    }
    qk_max = VLLM_SHFL_SYNC(qk_max, 0);
```

- 最后，我们可以通过比较该线程块中所有 warp 的 `qk_max` ，来得到整个线程块的归约 `qk_max` ，然后将最终结果广播到每个线程。


### `exp_sum`


- 与 `qk_max` 类似，我们也需要从整个线程块中获取归约后的总和值。

```plain
    for (int i = thread_idx; i < num_tokens; i += NUM_THREADS) {
        float val = __expf(logits[i] - qk_max);
        logits[i] = val;
        exp_sum += val;
    }
    ...
    exp_sum = block_sum<NUM_WARPS>(&red_smem[NUM_WARPS], exp_sum);
```

- 首先，对每个线程组的所有 exp 值求和，同时将 `logits` 的每个条目从 `qk` 转换为 `exp(qk - qk_max)`。请注意，这里的 `qk_max` 已经是整个线程块的最大 `qk` 。然后我们可以像对 `qk_max` 一样在整个线程块上归约 `exp_sum` 。

```plain
    const float inv_sum = __fdividef(1.f, exp_sum + 1e-6f);
    for (int i = thread_idx; i < num_tokens; i += NUM_THREADS) {
       logits[i] *= inv_sum;
    }
```

- 最后，通过归约后的 `qk_max` 和 `exp_sum`，我们可以获得最终的归一化 softmax 结果，即 `logits`。这个 `logits` 变量将在后续步骤中用于与值数据进行点乘运算。现在，它应该存储所有分配的上下文 token 的 `qk` 的归一化 softmax 结果。
