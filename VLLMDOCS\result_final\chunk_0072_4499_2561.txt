# 文档路径: 03-models > 07-speculative-decoding-in-vllm > vLLM 中的推测解码 > 通过在提示符中匹配 n-grams 进行推测

## 通过在提示符中匹配 n-grams 进行推测


以下代码配置了 vLLM 使用推测解码，其中通过匹配提示中的 n-grams 生成建议。更多信息请阅读[此线程](https://x.com/joao_gante/status/1747322413006643259)。

```python
from vllm import LLM, SamplingParams


prompts = [
    "The future of AI is",
]
sampling_params = SamplingParams(temperature=0.8, top_p=0.95)


llm = LLM(
    model="facebook/opt-6.7b",
    tensor_parallel_size=1,
    speculative_model="[ngram]",
    num_speculative_tokens=5,
    ngram_prompt_lookup_max=4,
    use_v2_block_manager=True,
)
outputs = llm.generate(prompts, sampling_params)


for output in outputs:
    prompt = output.prompt
    generated_text = output.outputs[0].text
    print(f"Prompt: {prompt!r}, Generated text: {generated_text!r}")
```
