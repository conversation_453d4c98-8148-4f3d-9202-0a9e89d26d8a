# 文档路径: 01-getting-started > 10-examples > 10-examples > 示例 > 脚本

## 脚本


API 客户端

Aqlm 示例

CPU 离线处理

GGUF 推理

Gradio OpenAI 聊天机器人 Web 服务器

Gradio Web 服务器

LLM 引擎示例

带量化的 LoRA 推理

MultiLoRA 推理

离线聊天工具

离线推理

离线推理 Arctic

离线推理音频语言

离线推理聊天

离线推理分布式

离线推理嵌入

离线推理编码器-解码器

离线推理 MlpSpeculator

离线推理 Neuron

离线推理 Neuron Int8 量化

离线推理 Pixtral

离线推理 TPU

离线推理视觉语言

离线推理视觉语言多图像

带前缀的离线推理

带有分析器的离线推理

OpenAI 音频 API 客户端

OpenAI 聊天补全客户端

带有工具的 OpenAI 聊天补全客户端

OpenAI 补全客户端

OpenAI 嵌入客户端

OpenAI 视觉 API 客户端

保存分片状态

Tensorize vLLM 模型
